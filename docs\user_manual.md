# RunSim GUI 使用手册

本文档提供了 RunSim GUI 应用程序的使用指南，包括基本操作、功能说明和高级用法。

## 1. 界面概述

RunSim GUI 的界面分为以下几个主要部分：

- **左侧面板**：用例管理面板，显示用例树和提供用例管理功能
- **右侧面板**：包含两个标签页
  - **运行参数配置**：配置运行参数
  - **执行日志**：显示执行日志
- **菜单栏**：提供文件操作和工具功能
- **状态栏**：显示应用程序状态和消息

## 2. 基本操作

### 2.1 加载用例

1. 在左侧用例管理面板中，点击"添加用例"按钮
2. 在弹出的文件对话框中选择用例文件
3. 用例文件将被添加到用例树中

### 2.2 选择用例

1. 在左侧用例树中，单击要运行的用例
2. 用例名称将自动填充到右侧配置面板的"用例"输入框中

### 2.3 配置参数

在右侧"运行参数配置"标签页中，可以配置以下参数：

- **基础参数**
  - **Base**：基础模块名称
  - **Block**：模块名称
  - **用例**：用例名称（通常由选择用例自动填充）
  - **运行目录**：运行目录路径
  - **其他选项**：其他命令行选项

- **仿真选项**
  - **FSDB**：生成 FSDB 波形文件
  - **VWDB**：生成 VWDB 波形文件
  - **CL**：编译并运行
  - **FSDB 文件**：FSDB 文件路径
  - **Dump SVA**：导出 SVA 信息
  - **覆盖率**：收集覆盖率信息
  - **UPF**：启用 UPF 功能
  - **仅仿真**：仅运行仿真
  - **仅编译**：仅编译不运行

- **高级选项**
  - **Dump Memory**：导出内存内容
  - **WDD**：波形调试目录
  - **种子号**：随机种子号
  - **Simarg**：仿真参数
  - **CFG DEF**：配置定义
  - **Post**：后处理命令
  - **回归文件**：回归测试文件
  - **FM**：形式验证
  - **BP Server**：断点服务器

### 2.4 执行命令

1. 配置完参数后，点击"执行"按钮
2. 系统将生成命令并在新的日志标签页中执行
3. 执行结果将实时显示在日志标签页中

### 2.5 查看日志

1. 切换到"执行日志"标签页
2. 选择要查看的日志标签页
3. 查看执行日志内容

### 2.6 保存和加载配置

- **保存配置**：点击菜单栏中的"文件" -> "保存配置"
- **加载配置**：点击菜单栏中的"文件" -> "加载配置"

### 2.7 清除历史记录

点击菜单栏中的"文件" -> "清除历史"，可以清除命令历史记录。

## 3. 高级功能

### 3.1 批量执行

1. 在左侧用例树中，按住 Ctrl 键选择多个用例
2. 点击"批量执行"按钮
3. 系统将为每个用例创建一个日志标签页并执行

### 3.2 使用历史记录

1. 在右侧配置面板中，点击"历史"按钮
2. 在弹出的历史记录列表中选择一条历史记录
3. 系统将自动填充相应的配置参数

### 3.3 使用插件

1. 点击菜单栏中的"工具"
2. 选择要使用的插件
3. 插件将在新窗口中打开

### 3.4 结果比较

1. 在"执行日志"标签页中，选择两个日志标签页
2. 点击"比较"按钮
3. 系统将打开结果比较插件，显示两个日志的差异

### 3.5 性能监控

1. 点击菜单栏中的"工具" -> "性能监控"
2. 性能监控插件将显示系统资源使用情况

### 3.6 覆盖率合并

1. 点击菜单栏中的"工具" -> "覆盖率合并"
2. 在覆盖率合并插件中选择要合并的覆盖率文件
3. 点击"合并"按钮，生成合并后的覆盖率报告

## 4. 插件系统

RunSim GUI 提供了丰富的插件系统，可以扩展应用程序的功能。

### 4.1 内置插件

- **批量执行插件**：支持多个用例的批量执行和并行处理
- **覆盖率合并插件**：合并多个覆盖率文件
- **日志分析插件**：分析日志文件，提取关键信息
- **性能分析插件**：分析仿真性能
- **性能监控插件**：监控系统资源使用情况
- **资源监控插件**：监控系统资源使用情况
- **结果比较插件**：比较两个日志文件的差异
- **时间分析插件**：分析仿真时间
- **波形比较插件**：比较两个波形文件的差异

### 4.2 用户插件

用户可以创建自定义插件，放置在 `plugins/user/` 目录下。

## 5. 常见问题

### 5.1 命令执行失败

问题：执行命令时出现错误。

解决方法：

1. 检查配置参数是否正确
2. 检查用例文件是否存在
3. 检查运行目录是否有写入权限
4. 查看日志中的错误信息

### 5.2 插件无法加载

问题：插件无法加载或运行。

解决方法：

1. 检查插件文件是否存在
2. 检查插件是否与当前版本兼容
3. 查看日志中的错误信息

### 5.3 性能问题

问题：应用程序运行缓慢或占用大量资源。

解决方法：

1. 关闭不需要的日志标签页
2. 减少同时运行的用例数量
3. 使用资源监控插件监控资源使用情况
4. 定期清理临时文件和日志文件

## 6. 快捷键

RunSim GUI 提供了以下快捷键：

- **Ctrl+S**：保存配置
- **Ctrl+O**：加载配置
- **Ctrl+R**：执行命令
- **Ctrl+T**：添加用例
- **Ctrl+W**：关闭当前日志标签页
- **Ctrl+Tab**：切换标签页
- **F5**：刷新用例树
- **F1**：显示帮助

## 7. 命令行参数

RunSim GUI 支持以下命令行参数：

```bash
python run_app.py [options]
```

选项：

- `--config <file>`：指定配置文件
- `--case <file>`：指定用例文件
- `--rundir <dir>`：指定运行目录
- `--help`：显示帮助信息

示例：

```bash
python run_app.py --config my_config.json --case my_case.cfg
```

## 8. 配置文件格式

RunSim GUI 使用 JSON 格式的配置文件，包含以下主要字段：

```json
{
  "window": {
    "maximized": false,
    "x": 100,
    "y": 100,
    "width": 1400,
    "height": 900
  },
  "base": "",
  "block": "",
  "case_files": [],
  "rundir": "",
  "other_options": "",
  "fsdb": false,
  "vwdb": false,
  "cl": false,
  "fsdb_file": "",
  "dump_sva": false,
  "cov": false,
  "upf": false,
  "sim_only": false,
  "compile_only": false,
  "dump_mem": "",
  "wdd": "",
  "seed": "",
  "simarg": "",
  "cfg_def": "",
  "post": "",
  "regr_file": "",
  "fm_checked": false,
  "bp_server": "",
  "last_command": ""
}
```
