"""
应用程序控制器模块

该模块实现了应用程序的主控制器，负责协调各个子控制器、模型和视图之间的交互。
AppController 是应用程序的核心，管理整个应用程序的生命周期，包括初始化、
配置加载和保存、历史记录管理、插件系统集成以及资源优化。
"""
import os
import sys
import gc
from PyQt5.QtWidgets import QApplication, QFileDialog, QMessageBox, QTabWidget
from PyQt5.QtCore import QObject, pyqtSlot, QTimer

from utils.event_bus import EventBus
from utils.resource_optimizer import ResourceOptimizer

from models.config_model import ConfigModel
from models.history_model import HistoryModel
from views.main_window import MainWindow
from controllers.case_controller import CaseController
from controllers.config_controller import ConfigController
from controllers.execution_controller import ExecutionController

class AppController(QObject):
    """
    应用程序控制器，负责协调各个控制器和视图

    AppController 是应用程序的主控制器，负责初始化和协调各个子控制器、
    模型和视图之间的交互。它管理应用程序的生命周期，包括配置加载和保存、
    历史记录管理、插件系统集成以及资源优化。

    主要职责：
    1. 初始化应用程序的模型、视图和控制器
    2. 管理配置的加载和保存
    3. 管理历史记录
    4. 集成插件系统
    5. 优化资源使用

    Attributes:
        config_model (ConfigModel): 配置数据模型
        history_model (HistoryModel): 历史记录模型
        main_window (MainWindow): 主窗口
        case_controller (CaseController): 用例控制器
        config_controller (ConfigController): 配置控制器
        execution_controller (ExecutionController): 执行控制器
        event_bus (EventBus): 事件总线
        plugin_manager (PluginManager): 插件管理器
        resource_optimizer (ResourceOptimizer): 资源优化器
    """

    def __init__(self):
        """
        初始化应用程序控制器

        创建并初始化应用程序的模型、视图和控制器，设置信号连接，
        加载配置和历史记录，初始化插件系统和资源优化器。
        """
        super().__init__()

        # 创建模型
        self.config_model = ConfigModel()
        self.history_model = HistoryModel()

        # 创建主窗口
        self.main_window = MainWindow()

        # 创建右侧面板容器
        from PyQt5.QtWidgets import QWidget, QTabWidget

        # 创建子控制器
        self.case_controller = CaseController(self.main_window, self.config_model)

        # 创建配置控制器和执行控制器
        self.config_controller = ConfigController(
            self.main_window,
            self.config_model,
            self.history_model
        )

        self.execution_controller = ExecutionController(
            self.main_window,
            self.config_model,
            self.history_model
        )

        # 设置配置控制器的执行控制器引用
        self.config_controller.set_execution_controller(self.execution_controller)

        # 创建标签页控件作为右侧面板
        right_panel = QTabWidget()
        right_panel.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #cccccc;
                background: white;
                border-radius: 3px;
            }
            QTabBar::tab {
                background: #f0f0f0;
                border: 1px solid #cccccc;
                padding: 8px 15px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background: white;
                border-bottom-color: white;
            }
            QTabBar::tab:hover:!selected {
                background: #e6f3ff;
            }
        """)

        # 将配置面板和执行面板添加为标签页
        right_panel.addTab(self.config_controller.config_panel, "运行参数配置")
        right_panel.addTab(self.execution_controller.execution_panel, "执行日志")

        # 设置默认显示"运行参数配置"标签页
        right_panel.setCurrentIndex(0)

        # 设置左侧和右侧面板
        self.main_window.set_left_panel(self.case_controller.case_panel)
        self.main_window.set_right_panel(right_panel)

        # 创建事件总线
        self.event_bus = EventBus.instance()

        # 连接信号
        self.connect_signals()

        # 使用事件总线连接控制器之间的信号
        try:
            # 用例选择信号
            self.event_bus.case_selected.connect(self.on_case_selected)

            # 命令执行信号
            self.event_bus.command_executed.connect(self.execution_controller.execute_command)

            # 配置变更信号
            self.event_bus.config_changed.connect(self.config_controller.on_config_changed)

            # 历史记录更新信号
            self.event_bus.history_updated.connect(self.config_controller.update_history_view)
        except Exception as e:
            print(f"警告: 连接事件总线信号时出错: {str(e)}")

        # 加载配置和历史记录
        self.load_config()
        self.load_history()

        # 初始化插件系统
        self.init_plugins()

        # 初始化资源优化器
        self.init_resource_optimizer()

    def connect_signals(self):
        """
        连接信号和槽

        连接主窗口的信号到相应的槽函数，包括保存配置、加载配置和清除历史记录等。
        这些连接使得用户界面的操作能够触发相应的功能。
        """
        # 主窗口信号
        self.main_window.save_config_requested.connect(self.save_config)
        self.main_window.load_config_requested.connect(self.load_config)
        self.main_window.clear_history_requested.connect(self.clear_history)

    def show(self):
        """
        显示主窗口

        显示应用程序的主窗口，使其对用户可见。
        """
        self.main_window.show()

    @pyqtSlot()
    def save_config(self):
        """
        保存配置

        将当前配置保存到配置文件中，并在状态栏显示保存结果。
        如果保存失败，则显示错误对话框。

        Returns:
            bool: 保存是否成功
        """
        success = self.config_model.save_config()
        if success:
            self.main_window.show_message("配置已保存")
        else:
            self.main_window.show_error("保存失败", "无法保存配置文件")
        return success

    @pyqtSlot()
    def load_config(self):
        """
        加载配置

        从配置文件加载配置，更新窗口位置和大小，并通知子控制器配置已加载。
        确保种子号为空字符串，以避免使用旧的种子号。

        Returns:
            dict: 加载的配置
        """
        config = self.config_model.load_config()

        # 更新窗口位置和大小
        if "window" in config:
            window_config = config["window"]
            if not window_config.get("maximized", False):
                self.main_window.setGeometry(
                    window_config.get("x", 100),
                    window_config.get("y", 100),
                    window_config.get("width", 1400),
                    window_config.get("height", 900)
                )
            else:
                self.main_window.showMaximized()

        # 确保seed为空字符串
        config["seed"] = ""
        self.config_model.config["seed"] = ""

        # 通知子控制器配置已加载
        self.case_controller.on_config_loaded(config)
        self.config_controller.on_config_loaded(config)
        self.execution_controller.on_config_loaded(config)

        self.main_window.show_message("配置已加载")
        return config

    @pyqtSlot()
    def load_history(self):
        """
        加载历史记录

        从历史记录文件加载历史记录，并更新配置控制器的历史记录视图。

        Returns:
            list: 加载的历史记录列表
        """
        history = self.history_model.load_history()
        self.config_controller.update_history_view(history)
        return history

    @pyqtSlot()
    def clear_history(self):
        """
        清空历史记录

        显示确认对话框，询问用户是否确定要清除所有历史记录。
        如果用户确认，则清空历史记录并更新历史记录视图。

        Returns:
            bool: 是否清空了历史记录
        """
        reply = QMessageBox.question(
            self.main_window,
            "确认清除",
            "确定要清除所有历史记录吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.history_model.clear_history()
            self.config_controller.update_history_view([])
            self.main_window.show_message("历史记录已清除")
            return True
        return False

    def init_plugins(self):
        """
        初始化插件系统

        导入插件管理器，确保辅助模块可用，创建插件管理器实例，
        加载插件并设置插件菜单。如果插件系统不可用或初始化过程中
        出现错误，则打印错误信息并跳过插件加载。

        Returns:
            bool: 插件系统是否成功初始化
        """
        try:
            # 导入插件管理器
            from plugins.manager import PluginManager

            # 确保 utils 目录中的模块可用
            from utils import async_logger, async_task_manager, cache_manager, common_widgets

            # 创建插件管理器
            self.plugin_manager = PluginManager(self.main_window)

            # 加载插件
            self.plugin_manager.load_plugins()

            # 设置插件菜单
            self.plugin_manager.setup_plugin_menu()

            return True

        except ImportError:
            print("插件系统未找到，跳过插件加载")
            return False
        except Exception as e:
            print(f"初始化插件系统时出错: {str(e)}")
            return False

    @pyqtSlot(str)
    def on_case_selected(self, case_name):
        """
        处理用例选择事件

        当用户在用例树中选择用例时，更新配置面板中的用例名称，
        清空种子号输入框，以确保每次运行使用新的随机种子，
        并触发命令预览的更新。

        Args:
            case_name (str): 用例名称，即用例文件的路径或名称

        Returns:
            bool: 是否成功处理了用例选择事件
        """
        # 更新配置面板中的用例名称
        if hasattr(self.config_controller, 'config_panel') and hasattr(self.config_controller.config_panel, 'case_input'):
            # 设置用例名称
            self.config_controller.config_panel.case_input.setText(case_name)

            # 更新配置模型中的用例名称
            self.config_model.update_config({"case": case_name})

            # 清空种子号输入框
            if case_name and not case_name.startswith("已选择"):
                if hasattr(self.config_controller.config_panel, 'seed_input'):
                    self.config_controller.config_panel.seed_input.setText("")

            # 强制立即触发命令预览更新
            if hasattr(self.config_controller, 'generate_command_preview'):
                # 确保命令预览立即更新，不受延迟影响
                self.config_controller.config_panel.config_changed_flag = True
                self.config_controller.config_panel.last_user_input = 0  # 设置为0以确保立即更新
                self.config_controller.generate_command_preview()

            return True
        return False

    def save_window_state(self):
        """
        保存窗口状态

        保存主窗口的位置、大小和最大化状态到配置模型中，
        并将配置保存到文件。这样在下次启动应用程序时，
        可以恢复窗口的位置和大小。

        Returns:
            bool: 保存是否成功
        """
        if self.main_window.isMaximized():
            self.config_model.update_config({
                "window": {
                    "maximized": True,
                    "x": 100,
                    "y": 100,
                    "width": 1400,
                    "height": 900
                }
            })
        else:
            geometry = self.main_window.geometry()
            self.config_model.update_config({
                "window": {
                    "maximized": False,
                    "x": geometry.x(),
                    "y": geometry.y(),
                    "width": geometry.width(),
                    "height": geometry.height()
                }
            })

        return self.config_model.save_config()

    def init_resource_optimizer(self):
        """
        初始化资源优化器

        创建资源优化器实例，设置优化参数，连接信号，并启动优化。
        资源优化器用于监控和优化应用程序的资源使用，减少内存和CPU占用。

        Returns:
            bool: 资源优化器是否成功初始化
        """
        try:
            # 创建资源优化器
            self.resource_optimizer = ResourceOptimizer()

            # 连接信号
            self.resource_optimizer.optimization_performed.connect(self.on_optimization_performed)

            # 设置优化参数
            self.resource_optimizer.set_thresholds(memory_threshold=500, cpu_threshold=50)
            self.resource_optimizer.set_log_parameters(cache_size=10000, refresh_interval=500)

            # 启动优化
            self.resource_optimizer.start_optimization(interval=60000)  # 每分钟优化一次

            print("资源优化器已启动")
            return True

        except Exception as e:
            print(f"初始化资源优化器时出错: {str(e)}")
            return False

    @pyqtSlot(dict)
    def on_optimization_performed(self, result):
        """
        处理优化执行事件

        当资源优化器执行优化后，处理优化结果，打印优化信息，
        并在优化效果显著时在状态栏显示提示信息。

        Args:
            result (dict): 优化结果，包含优化前后的内存和CPU使用情况
                - memory_before (float): 优化前的内存使用量（MB）
                - memory_after (float): 优化后的内存使用量（MB）
                - cpu_before (float): 优化前的CPU使用率（%）
                - cpu_after (float): 优化后的CPU使用率（%）
        """
        # 打印优化结果
        print(f"资源优化已执行: 内存使用从 {result['memory_before']:.1f}MB 减少到 {result['memory_after']:.1f}MB")

        # 如果优化效果显著，显示提示
        if result['memory_before'] - result['memory_after'] > 50:  # 内存减少超过50MB
            self.main_window.show_message(f"已优化资源使用，释放了 {result['memory_before'] - result['memory_after']:.1f}MB 内存")
