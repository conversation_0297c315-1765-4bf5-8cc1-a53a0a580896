# RunSim GUI 代码注释规范

## 1. 模块级文档字符串

每个模块（.py文件）都应该包含一个模块级文档字符串，描述模块的功能和职责。

```python
"""
模块名称

模块详细描述，包括模块的功能、职责和使用方法。
如果模块包含多个类或函数，应该简要描述它们之间的关系。
"""
```

## 2. 类级文档字符串

每个类都应该包含一个类级文档字符串，描述类的功能和职责。

```python
class ClassName:
    """
    类名称

    类详细描述，包括类的功能、职责和使用方法。
    如果类是某个设计模式的实现，应该说明设计模式的名称。
    如果类有特殊的使用注意事项，应该在这里说明。
    """
```

## 3. 方法级文档字符串

每个公共方法都应该包含一个方法级文档字符串，描述方法的功能、参数和返回值。

```python
def method_name(param1, param2):
    """
    方法名称

    方法详细描述，包括方法的功能和使用方法。

    Args:
        param1 (type): 参数1的描述
        param2 (type): 参数2的描述

    Returns:
        type: 返回值的描述

    Raises:
        ExceptionType: 异常的描述
    """
```

## 4. 行内注释

对于复杂的代码块，应该添加行内注释，解释代码的功能和逻辑。

```python
# 这是一个行内注释，解释下面代码的功能
x = calculate_something()  # 这是一个行末注释，解释这行代码的功能
```

## 5. TODO 注释

对于需要后续完善的代码，应该添加 TODO 注释。

```python
# TODO: 这里需要添加错误处理
```

## 6. 注释风格

- 使用中文注释，确保注释清晰易懂
- 注释应该解释代码的"为什么"，而不仅仅是"做了什么"
- 避免过多的注释，代码本身应该是自解释的
- 保持注释的更新，确保注释与代码保持一致

## 7. 示例

```python
"""
配置控制器模块

该模块实现了配置控制器，负责管理应用程序的配置参数，
包括加载、保存和更新配置。
"""

class ConfigController:
    """
    配置控制器类
    
    负责管理应用程序的配置参数，包括加载、保存和更新配置。
    实现了观察者模式，当配置发生变化时通知相关组件。
    """
    
    def load_config(self, file_path):
        """
        从文件加载配置
        
        从指定的文件路径加载配置，并更新当前配置。
        
        Args:
            file_path (str): 配置文件的路径
            
        Returns:
            dict: 加载的配置
            
        Raises:
            FileNotFoundError: 当配置文件不存在时
            JSONDecodeError: 当配置文件格式不正确时
        """
        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"配置文件不存在: {file_path}")
            
        # 加载配置文件
        with open(file_path, 'r') as f:
            config = json.load(f)  # 解析JSON配置
            
        # 更新当前配置
        self._update_config(config)
        
        return config
```
