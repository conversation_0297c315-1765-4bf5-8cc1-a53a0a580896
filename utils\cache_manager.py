import os
import json
import hashlib
from datetime import datetime

class CacheManager:
    """缓存管理器"""
    def __init__(self, cache_dir=".cache"):
        self.cache_dir = cache_dir
        if not os.path.exists(cache_dir):
            os.makedirs(cache_dir)
            
    def get_cache_key(self, file_path):
        """获取缓存键"""
        file_stat = os.stat(file_path)
        key_data = f"{file_path}_{file_stat.st_mtime}_{file_stat.st_size}"
        return hashlib.md5(key_data.encode()).hexdigest()
        
    def get_cached_data(self, file_path):
        """获取缓存数据"""
        cache_key = self.get_cache_key(file_path)
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")
        
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'r') as f:
                    cache_data = json.load(f)
                    # 验证缓存是否过期(24小时)
                    if (datetime.now() - datetime.fromisoformat(cache_data['timestamp'])).days < 1:
                        return cache_data['data']
            except Exception:
                pass
        return None
        
    def save_cache(self, file_path, data):
        """保存数据到缓存"""
        cache_key = self.get_cache_key(file_path)
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")
        
        try:
            cache_data = {
                'timestamp': datetime.now().isoformat(),
                'data': data
            }
            with open(cache_file, 'w') as f:
                json.dump(cache_data, f)
        except Exception as e:
            print(f"保存缓存失败: {str(e)}")
