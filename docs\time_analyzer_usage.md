# 仿真时间分析器插件使用说明

## 概述

仿真时间分析器插件用于分析EDA仿真用例的编译和仿真时间，帮助用户识别耗时较长的用例，优化仿真流程。

## 主要改进

### 版本更新 (2024年)
- **默认不自动分析**: 插件启动时不再自动分析当前目录，避免在包含大量用例的目录中造成长时间等待
- **GUI界面**: 提供友好的图形界面，用户可以选择分析目录并手动触发分析
- **进度条显示**: 分析过程中显示实时进度和状态信息
- **异步处理**: 使用工作线程进行分析，不会阻塞主界面
- **取消功能**: 支持在分析过程中取消操作

## 使用方法

### 1. 启动插件

在RunSim GUI的工具菜单中选择"仿真时间分析器"，或者通过插件管理器启动。

### 2. 配置分析目录

- **默认目录**: 插件会自动设置默认分析目录为 `$PROJ_DIR/work`
  - `PROJ_DIR` 是环境变量，如果未设置则使用当前工作目录
- **手动选择**: 可以在输入框中直接输入目录路径，或点击"浏览..."按钮选择目录
- **目录要求**: 分析目录应包含多个用例子目录，每个用例目录下应有 `log` 子目录

### 3. 开始分析

1. 确认分析目录路径正确
2. 点击"开始分析"按钮
3. 观察进度条和状态信息
4. 如需取消，点击"取消分析"按钮

### 4. 查看结果

分析完成后，结果会显示在表格中，包含以下信息：
- **用例名称**: 用例目录名
- **编译时间**: 编译阶段耗时（分钟）
- **仿真时间**: 仿真阶段耗时（分钟）
- **总时间**: 编译+仿真总耗时（分钟）

表格支持：
- 按列排序（点击列标题）
- 行选择
- 列宽调整

### 5. 导出数据

点击"导出到Excel"按钮可将分析结果保存为Excel文件，便于进一步分析和报告。

## 目录结构要求

插件期望的目录结构如下：

```
分析目录/
├── case1/
│   └── log/
│       ├── irun_compile.log  # 编译日志
│       └── irun_sim.log      # 仿真日志
├── case2/
│   └── log/
│       ├── irun_compile.log
│       └── irun_sim.log
└── ...
```

## 日志文件格式

插件会从日志文件中提取时间信息：

### 编译日志 (irun_compile.log)
查找包含 "CPU time" 的行，格式如：
```
CPU time: 1 minutes, 23.45 seconds
```

### 仿真日志 (irun_sim.log)
查找包含 "CPU time" 或 "Simulation complete" 的行，格式如：
```
CPU time: 5 minutes, 12.34 seconds
Simulation complete via $finish(1) at time 1000 ns + 0
```

## 环境变量

- **PROJ_DIR**: 项目根目录，用于设置默认分析目录
  - 如果设置了此环境变量，默认分析目录为 `$PROJ_DIR/work`
  - 如果未设置，默认分析目录为当前工作目录

## 窗口管理

- **最小化**: 点击"最小化窗口"将窗口最小化到任务栏
- **后台运行**: 点击"后台运行"隐藏窗口但保持分析进程运行
- **关闭**: 点击"关闭"完全关闭插件窗口

## 性能优化

- 使用多线程处理，避免界面冻结
- 支持分析过程中的取消操作
- 进度条实时显示分析状态
- 自动跳过无效的用例目录

## 故障排除

### 常见问题

1. **目录不存在**
   - 检查输入的目录路径是否正确
   - 确保目录存在且可访问

2. **未找到用例**
   - 检查目录结构是否符合要求
   - 确保用例目录下有 `log` 子目录

3. **未找到日志文件**
   - 检查 `log` 目录下是否有 `irun_compile.log` 和 `irun_sim.log` 文件
   - 确保日志文件不为空且包含时间信息

4. **分析时间过长**
   - 可以使用取消功能中止分析
   - 考虑分析较小的目录范围

### 调试信息

插件会在控制台输出调试信息，包括：
- 扫描到的用例目录数量
- 当前正在分析的用例
- 解析失败的日志文件信息

## 技术细节

- **开发语言**: Python 3.8+
- **GUI框架**: PyQt5
- **多线程**: QThread
- **Excel导出**: openpyxl
- **时间解析**: 正则表达式

## 更新历史

- **2024年**: 重构为GUI模式，添加进度条和异步处理
- **之前版本**: 命令行模式，自动分析当前目录
