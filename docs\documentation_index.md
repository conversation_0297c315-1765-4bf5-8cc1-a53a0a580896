# RunSim GUI Documentation Index

This document provides an index of RunSim GUI application documentation, helping users and developers quickly find the documents they need.

## 1. User Documentation

### 1.1 [Installation Guide](installation_guide.md)

The installation guide provides installation steps and environment requirements for the RunSim GUI application, including:

- Environment requirements
- Installation steps
- Configuration instructions
- Troubleshooting common issues

### 1.2 [User Manual](user_manual.md)

The user manual provides usage guidelines for the RunSim GUI application, including:

- Interface overview
- Basic operations
- Advanced features
- Plugin system
- Common issues
- Keyboard shortcuts
- Command line parameters
- Configuration file format

## 2. Development Documentation

### 2.1 [Architecture Overview](architecture_overview.md)

The architecture overview provides the architectural design of the RunSim GUI application, including:

- Overall architecture
- Module responsibilities
- Data flow
- Design patterns
- Extension points
- Performance optimization

### 2.2 [API Reference](api_reference.md)

The API reference provides documentation for the public interfaces of the RunSim GUI application, including:

- Controller APIs
- Model APIs
- View APIs
- Utility APIs

### 2.3 [Development Guide](development_guide.md)

The development guide provides instructions on how to extend and modify the RunSim GUI application, including:

- Development environment setup
- Project structure
- Development workflow
- Extending the application
- Best practices

### 2.4 [Class and Flow Diagrams](class_and_flow_diagrams.md)

The class and flow diagrams provide the class structure and execution flow of the RunSim GUI application, including:

- Overall class diagram
- Controller class diagrams
- Model class diagrams
- View class diagrams
- Utility class diagrams
- Application startup flow
- Case loading flow
- Command execution flow
- Configuration save and load flow
- Plugin loading flow
- Event bus communication flow

### 2.5 [Code Comment Guidelines](code_comment_guidelines.md)

The code comment guidelines provide standards for code comments in the RunSim GUI application, including:

- Module-level docstrings
- Class-level docstrings
- Method-level docstrings
- Inline comments
- TODO comments
- Comment style
- Examples

## 3. Testing Documentation

### 3.1 [Testing Documentation](testing_documentation.md)

The testing documentation provides test plans and test cases for the RunSim GUI application, including:

- Testing strategy
- Unit tests
- Integration tests
- Functional tests
- Performance tests
- UI tests
- Test execution plan
- Test report templates

## 4. Project Management Documentation

### 4.1 [Refactoring Progress Report](../重构进展报告.md)

The refactoring progress report provides information on the progress of the RunSim GUI application refactoring, including:

- Completed work
- Next steps
- Refactoring strategy
- Current directory structure
- How to run the refactored application
- How to test the refactored application
- Future work plan

### 4.2 [Refactoring Design Document](../runsim_gui_重构设计文档.md)

The refactoring design document provides the design for the RunSim GUI application refactoring, including:

- Current code structure analysis
- Planned modules and their responsibilities
- Module dependencies
- Refactoring steps and implementation plan
- Potential risks and mitigation measures
- Benefits of refactoring
- Future optimization directions

## 5. Other Documentation

### 5.1 [Optimization Plan](../优化计划.md)

The optimization plan provides the optimization plan for the RunSim GUI application, including:

- Performance optimization
- User experience optimization
- Code quality optimization
- Feature optimization

### 5.2 [Optimization Results Report](../优化结果报告.md)

The optimization results report provides the results of the RunSim GUI application optimization, including:

- Performance optimization results
- User experience optimization results
- Code quality optimization results
- Feature optimization results

### 5.3 [Final Integration Guide](../最终集成指南.md)

The final integration guide provides the steps for the final integration of the RunSim GUI application, including:

- Integration preparation
- Integration steps
- Integration testing
- Post-integration verification

### 5.4 [Final Integration Report](../最终集成报告.md)

The final integration report provides the results of the final integration of the RunSim GUI application, including:

- Integration process
- Integration results
- Issues encountered and solutions
- Future work
