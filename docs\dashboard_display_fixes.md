# RunSim GUI 仪表盘显示问题修复报告

## 问题描述

仪表盘展示存在以下问题：
1. 展示还是总用例数，并没有将Subsys级用例和TOP级用例分开展示
2. 用例通过数竟然大于总用例数
3. 依然显示已失败，应该修改为待处理
4. 验证阶段管理部分阶段进度概览是%0
5. 阶段详情统计部分，没有显示不同阶段用例个数，也没有显示进度

## 修复方案

### 1. 分级别统计卡片展示

**问题**：仪表盘只显示总用例数，没有分别展示Subsys和TOP级别统计

**修复**：
- 保留原有的总体统计卡片
- 新增分级别统计卡片，分别显示Subsys和TOP级别的统计数据
- 使用不同的图标和颜色区分不同级别

**修改文件**：`plugins/builtin/dashboard_web/templates/dashboard.html`

**关键修改**：
```html
<!-- 分级别统计卡片 -->
<div class="row mb-4">
    <div class="col-lg-6 mb-3">
        <div class="card h-100">
            <div class="card-header bg-light">
                <h6 class="card-title mb-0">
                    <i class="fas fa-microchip me-2"></i>Subsys级别统计
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <h4 class="text-success mb-1" id="subsys-passed">-</h4>
                        <small class="text-muted">通过</small>
                    </div>
                    <!-- 更多统计... -->
                </div>
            </div>
        </div>
    </div>
    <!-- TOP级别统计卡片... -->
</div>
```

### 2. 修复通过数大于总用例数问题

**问题**：由于分级别统计相加导致通过数超过总用例数

**修复**：
- 修正API统计逻辑，计算实际通过的用例数
- 避免同一用例在不同级别被重复计算
- 使用实际通过数计算通过率

**修改文件**：`plugins/builtin/dashboard_web/routes/api.py`

**关键修改**：
```python
# 重新计算实际通过的用例数（一个用例只计算一次）
cursor.execute('''
    SELECT COUNT(*) as actual_passed
    FROM test_cases
    WHERE subsys_status = 'PASS' OR top_status = 'PASS' 
       OR post_subsys_status = 'PASS' OR post_top_status = 'PASS'
''')
actual_passed_cases = cursor.fetchone()['actual_passed'] or 0

return jsonify({
    'cases': {
        'passed': actual_passed_cases,  # 使用实际通过数
        'pass_rate': round(pass_rate, 2),
        # ...
    }
})
```

### 3. 修改"失败"为"待处理"

**问题**：界面显示"失败用例"，应该改为"待处理用例"

**修复**：
- 修改统计卡片标题和图标
- 更新状态图表标签
- 统一状态颜色方案

**修改文件**：`plugins/builtin/dashboard_web/templates/dashboard.html`

**关键修改**：
```html
<!-- 原来的失败用例卡片改为待处理用例 -->
<div class="card metric-card text-white bg-info h-100">
    <div class="card-body">
        <div class="d-flex justify-content-between">
            <div>
                <p class="metric-label text-white-50">待处理用例</p>
                <h2 class="metric-value" id="pending-cases">-</h2>
            </div>
            <div class="align-self-center">
                <i class="fas fa-hourglass-half fa-2x text-white-50"></i>
            </div>
        </div>
    </div>
</div>

<!-- 状态图表标签修正 -->
labels: ['通过', '待处理', '进行中', '未开始'],
backgroundColor: ['#28a745', '#17a2b8', '#ffc107', '#6c757d'],
```

### 4. 修复阶段进度概览显示0%问题

**问题**：验证阶段管理部分阶段进度概览显示0%

**修复**：
- 修正PhaseAnalyzer的查询逻辑
- 改进阶段数据统计方法
- 确保前端正确处理空数据

**修改文件**：`plugins/builtin/dashboard_web/utils/phase_analyzer.py`

**关键修改**：
```python
# 对于普通类型，统计有状态值的用例（不管阶段）
normal_query = f'''
    SELECT
        COUNT(*) as total,
        SUM(CASE WHEN {status_col} = 'PASS' THEN 1 ELSE 0 END) as pass_count,
        SUM(CASE WHEN {status_col} = 'Pending' THEN 1 ELSE 0 END) as pending_count,
        SUM(CASE WHEN {status_col} = 'On-Going' THEN 1 ELSE 0 END) as ongoing_count,
        SUM(CASE WHEN {status_col} = 'N/A' OR {status_col} IS NULL THEN 1 ELSE 0 END) as na_count
    FROM test_cases
    {where_clause}
    {'AND' if where_clause else 'WHERE'} {status_col} IS NOT NULL
'''
```

### 5. 改进阶段详情统计显示

**问题**：阶段详情统计部分没有显示不同阶段用例个数和进度

**修复**：
- 重新设计阶段详情统计表格
- 显示各阶段各类型的详细用例数
- 添加进度条和完成度显示
- 使用图标和颜色区分不同状态

**修改文件**：`plugins/builtin/dashboard_web/templates/dashboard.html`

**关键修改**：
```javascript
const row = `
    <tr>
        <td class="fw-bold">${phase}</td>
        <td>
            <div class="d-flex flex-column">
                <span class="badge bg-primary mb-1">总计: ${subsysStats.total || 0}</span>
                <div class="small">
                    <span class="text-success me-2">✓${subsysStats.pass || 0}</span>
                    <span class="text-info me-2">⏳${subsysStats.pending || 0}</span>
                    <span class="text-warning me-2">⚡${subsysStats.ongoing || 0}</span>
                    <span class="text-muted">N/A${subsysStats.na || 0}</span>
                </div>
            </div>
        </td>
        <!-- 其他列... -->
        <td>
            <div class="progress" style="height: 25px; min-width: 100px;">
                <div class="progress-bar bg-success"
                     style="width: ${progressPercentage}%;">
                    ${progressPercentage}%
                </div>
            </div>
            <div class="small text-center mt-1 text-muted">
                ${totalCompleted}/${totalCases} 完成
            </div>
        </td>
    </tr>
`;
```

## 修改文件清单

1. **plugins/builtin/dashboard_web/templates/dashboard.html**
   - 添加分级别统计卡片
   - 修改"失败"为"待处理"
   - 改进阶段详情统计表格显示
   - 修正状态图表标签和颜色

2. **plugins/builtin/dashboard_web/routes/api.py**
   - 修正通过数计算逻辑
   - 避免重复计算同一用例
   - 使用实际通过数计算通过率

3. **plugins/builtin/dashboard_web/utils/phase_analyzer.py**
   - 修正阶段数据查询逻辑
   - 改进POST类型用例统计
   - 确保正确统计各阶段用例数

## 预期效果

修复后的仪表盘将具备以下特性：

1. **清晰的分级别展示**：分别显示Subsys和TOP级别的统计数据
2. **准确的通过率**：通过数不会超过总用例数，通过率计算准确
3. **统一的状态术语**：使用"待处理"替代"失败"，更符合实际业务
4. **完整的阶段进度**：正确显示各阶段的进度百分比
5. **详细的统计信息**：阶段详情表格显示完整的用例数和进度信息

这些修复将显著提升仪表盘的可用性和数据准确性，为用户提供更好的项目进度监控体验。
