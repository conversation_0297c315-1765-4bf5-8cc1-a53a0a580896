<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导入测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">📁 TestPlan导入测试</h5>
                    </div>
                    <div class="card-body">
                        <form id="import-form" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="file" class="form-label">选择Excel文件</label>
                                <input type="file" class="form-control" id="file" name="file" accept=".xlsx,.xls">
                            </div>
                            <div class="mb-3">
                                <label for="sheet_name" class="form-label">工作表名称</label>
                                <input type="text" class="form-control" id="sheet_name" name="sheet_name" value="TP">
                            </div>
                            <div class="mb-3">
                                <label for="project_id" class="form-label">项目ID</label>
                                <input type="number" class="form-control" id="project_id" name="project_id" value="1">
                            </div>
                            <button type="button" class="btn btn-primary" onclick="importExcel()">
                                <span id="spinner" class="spinner-border spinner-border-sm d-none me-2"></span>
                                开始导入
                            </button>
                        </form>

                        <div id="result" class="mt-4"></div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h6>🔍 调试信息</h6>
                    </div>
                    <div class="card-body">
                        <div id="debug-info"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    function importExcel() {
        const form = document.getElementById('import-form');
        const formData = new FormData(form);
        const button = document.querySelector('button[onclick="importExcel()"]');
        const spinner = document.getElementById('spinner');
        const resultDiv = document.getElementById('result');
        const debugDiv = document.getElementById('debug-info');

        // 检查文件
        if (!formData.get('file') || formData.get('file').size === 0) {
            showResult('danger', '错误', '请选择要导入的Excel文件');
            return;
        }

        // 显示调试信息
        debugDiv.innerHTML = `
            <p><strong>文件信息:</strong></p>
            <ul>
                <li>文件名: ${formData.get('file').name}</li>
                <li>文件大小: ${formData.get('file').size} bytes</li>
                <li>工作表: ${formData.get('sheet_name')}</li>
                <li>项目ID: ${formData.get('project_id')}</li>
            </ul>
            <p><strong>请求URL:</strong> /api/testplan/import</p>
            <p><strong>请求时间:</strong> ${new Date().toLocaleString()}</p>
        `;

        // 显示加载状态
        button.disabled = true;
        spinner.classList.remove('d-none');
        showResult('info', '正在处理', '正在上传和解析Excel文件，请稍候...');

        // 发送请求
        fetch('/api/testplan/import', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            debugDiv.innerHTML += `<p><strong>响应状态:</strong> ${response.status} ${response.statusText}</p>`;
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return response.json();
        })
        .then(data => {
            debugDiv.innerHTML += `<p><strong>响应数据:</strong></p><pre>${JSON.stringify(data, null, 2)}</pre>`;
            
            if (data.success) {
                let message = `
                    <strong>导入处理完成！</strong><br>
                    📊 总解析: ${data.data.total_parsed} 条<br>
                    ✅ 有效用例: ${data.data.valid_cases} 条<br>
                    💾 成功导入: ${data.data.imported_count} 条<br>
                    ❌ 失败: ${data.data.failed_count} 条
                `;

                if (data.import_errors && data.import_errors.length > 0) {
                    message += `<br><br><strong>导入错误:</strong><br>`;
                    data.import_errors.slice(0, 5).forEach(error => {
                        message += `• ${error}<br>`;
                    });
                    if (data.import_errors.length > 5) {
                        message += `... 还有 ${data.import_errors.length - 5} 个错误`;
                    }
                }

                showResult('success', '导入成功', message);
            } else {
                showResult('danger', '导入失败', data.error || '未知错误');
            }
        })
        .catch(error => {
            debugDiv.innerHTML += `<p><strong>错误信息:</strong> ${error.message}</p>`;
            showResult('danger', '请求失败', `网络错误: ${error.message}`);
        })
        .finally(() => {
            button.disabled = false;
            spinner.classList.add('d-none');
        });
    }

    function showResult(type, title, message) {
        const resultDiv = document.getElementById('result');
        resultDiv.innerHTML = `
            <div class="alert alert-${type}">
                <h6>${title}</h6>
                <div>${message}</div>
            </div>
        `;
    }
    </script>
</body>
</html>
