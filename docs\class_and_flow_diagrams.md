# RunSim GUI 类图和流程图

本文档提供了 RunSim GUI 应用程序的类图和流程图，帮助理解代码结构和执行流程。

## 1. 类图

### 1.1 整体类图

```
+------------------+     +------------------+     +------------------+
|  AppController   |<--->| CaseController   |<--->| CaseModel        |
+------------------+     +------------------+     +------------------+
         |                      |                        |
         |                      v                        |
         |               +------------------+            |
         |               | CasePanel        |            |
         |               +------------------+            |
         |                                               |
         |                                               |
         |               +------------------+     +------------------+
         +-------------->| ConfigController |<--->| ConfigModel      |
         |               +------------------+     +------------------+
         |                      |                        |
         |                      v                        |
         |               +------------------+            |
         |               | ConfigPanel      |            |
         |               +------------------+            |
         |                                               |
         |                                               |
         |               +------------------+     +------------------+
         +-------------->| ExecutionController |<--->| CommandModel     |
                         +------------------+     +------------------+
                                |                        |
                                |                        |
                                v                        v
                         +------------------+     +------------------+
                         | ExecutionPanel   |     | HistoryModel     |
                         +------------------+     +------------------+
                                |
                                v
                         +------------------+
                         | LogPanel         |
                         +------------------+
```

### 1.2 控制器类图

```
+------------------+
|    QObject       |
+------------------+
         ^
         |
+------------------+
|  AppController   |
+------------------+
| - main_window    |
| - config_model   |
| - history_model  |
| - case_controller|
| - config_controller|
| - execution_controller|
| - event_bus      |
| - plugin_manager |
+------------------+
| + show()         |
| + save_config()  |
| + load_config()  |
| + load_history() |
| + clear_history()|
+------------------+
         ^
         |
+------------------+     +------------------+     +------------------+
| CaseController   |     | ConfigController |     | ExecutionController |
+------------------+     +------------------+     +------------------+
| - main_window    |     | - main_window    |     | - main_window    |
| - config_model   |     | - config_model   |     | - config_model   |
| - case_panel     |     | - history_model  |     | - history_model  |
| - case_model     |     | - config_panel   |     | - execution_panel|
+------------------+     | - execution_controller|+------------------+
| + load_case_file()|    +------------------+     | + execute_command()|
| + remove_case_file()|  | + on_config_loaded()| | + on_config_loaded()|
| + on_config_loaded()|  | + on_config_changed()|| + close_log_panel()|
+------------------+     | + update_history_view()|+------------------+
                         +------------------+
```

### 1.3 模型类图

```
+------------------+
|    QObject       |
+------------------+
         ^
         |
+------------------+     +------------------+     +------------------+
|   ConfigModel    |     |   CaseModel      |     |   CommandModel   |
+------------------+     +------------------+     +------------------+
| - config_file    |     | - case_files     |     | - command_generator|
| - config         |     +------------------+     +------------------+
| - default_config |     | + add_case_file()|     | + generate_command()|
+------------------+     | + remove_case_file()|  +------------------+
| + load_config()  |     | + get_case_files()|
| + save_config()  |     +------------------+
| + update_config()|
| + get_config()   |     +------------------+
| + get_value()    |     |   HistoryModel   |
+------------------+     +------------------+
                         | - history_file   |
                         | - history        |
                         | - max_history    |
                         +------------------+
                         | + add_history()  |
                         | + get_history()  |
                         | + clear_history()|
                         | + load_history() |
                         | + save_history() |
                         +------------------+
```

### 1.4 视图类图

```
+------------------+
|    QMainWindow   |
+------------------+
         ^
         |
+------------------+
|   MainWindow     |
+------------------+
| - central_widget |
| - main_layout    |
| - status_bar     |
| - tools_menu     |
+------------------+
| + set_left_panel()|
| + set_right_panel()|
| + show_message() |
| + show_error()   |
| + show_warning() |
| + show_info()    |
| + show_question()|
+------------------+

+------------------+     +------------------+     +------------------+
|    QWidget       |     |    QWidget       |     |    QWidget       |
+------------------+     +------------------+     +------------------+
         ^                        ^                        ^
         |                        |                        |
+------------------+     +------------------+     +------------------+
|   CasePanel      |     |   ConfigPanel    |     |   ExecutionPanel |
+------------------+     +------------------+     +------------------+
| - case_tree      |     | - base_input     |     | - tab_widget     |
| - add_button     |     | - block_input    |     | - log_panels     |
| - remove_button  |     | - case_input     |     +------------------+
+------------------+     | - rundir_input   |     | + add_log_panel()|
| + add_case_file()|     | - options_input  |     | + close_tab()    |
| + remove_case_file()|  | - execute_button |     | + get_current_tab()|
| + get_selected_case()| | - history_button |     +------------------+
+------------------+     +------------------+
                         | + set_preview_text()|   +------------------+
                         | + get_config()    |     |    QWidget       |
                         +------------------+     +------------------+
                                                           ^
                                                           |
                                                  +------------------+
                                                  |   LogPanel       |
                                                  +------------------+
                                                  | - log_text       |
                                                  | - process        |
                                                  +------------------+
                                                  | + append_log()   |
                                                  | + clear_log()    |
                                                  | + execute_command()|
                                                  +------------------+
```

### 1.5 工具类图

```
+------------------+     +------------------+     +------------------+
|  EventBus        |     | ResourceMonitor  |     | CaseParser       |
+------------------+     +------------------+     +------------------+
| - _instance      |     | - update_interval|     | + parse_case_file()|
| - _initialized   |     | - timer          |     +------------------+
+------------------+     +------------------+
| + instance()     |     | + start()        |     +------------------+
| + emit_case_selected()|| + stop()         |     | CommandGenerator |
| + emit_command_executed()|| + update()    |     +------------------+
| + emit_config_changed()|| + get_memory_usage()|  | + generate_command()|
| + emit_history_updated()|| + get_cpu_usage()|   +------------------+
+------------------+     +------------------+
                                                  +------------------+
                                                  | PathResolver     |
                                                  +------------------+
                                                  | + resolve_path() |
                                                  | + get_base_block()|
                                                  +------------------+
```

## 2. 流程图

### 2.1 应用程序启动流程

```
+------------------+     +------------------+     +------------------+
| 启动应用程序      |---->| 创建 AppController|---->| 创建模型和视图   |
+------------------+     +------------------+     +------------------+
                                                           |
                                                           v
+------------------+     +------------------+     +------------------+
| 显示主窗口       |<----| 初始化插件系统   |<----| 创建子控制器     |
+------------------+     +------------------+     +------------------+
         |
         v
+------------------+
| 进入事件循环     |
+------------------+
```

### 2.2 用例加载流程

```
+------------------+     +------------------+     +------------------+
| 用户点击添加按钮 |---->| 打开文件对话框   |---->| 选择用例文件     |
+------------------+     +------------------+     +------------------+
                                                           |
                                                           v
+------------------+     +------------------+     +------------------+
| 更新用例树       |<----| 解析用例文件     |<----| 添加用例文件     |
+------------------+     +------------------+     +------------------+
```

### 2.3 命令执行流程

```
+------------------+     +------------------+     +------------------+
| 用户点击执行按钮 |---->| 收集配置参数     |---->| 生成命令         |
+------------------+     +------------------+     +------------------+
                                                           |
                                                           v
+------------------+     +------------------+     +------------------+
| 显示执行结果     |<----| 执行命令         |<----| 创建日志标签页   |
+------------------+     +------------------+     +------------------+
         |
         v
+------------------+     +------------------+
| 更新历史记录     |---->| 保存历史记录     |
+------------------+     +------------------+
```

### 2.4 配置保存和加载流程

```
+------------------+     +------------------+     +------------------+
| 用户点击保存配置 |---->| 收集配置参数     |---->| 保存到文件       |
+------------------+     +------------------+     +------------------+

+------------------+     +------------------+     +------------------+
| 用户点击加载配置 |---->| 从文件加载配置   |---->| 更新配置模型     |
+------------------+     +------------------+     +------------------+
                                                           |
                                                           v
+------------------+     +------------------+
| 更新视图         |<----| 通知子控制器     |
+------------------+     +------------------+
```

### 2.5 插件加载流程

```
+------------------+     +------------------+     +------------------+
| 初始化插件系统   |---->| 创建插件管理器   |---->| 扫描插件目录     |
+------------------+     +------------------+     +------------------+
                                                           |
                                                           v
+------------------+     +------------------+     +------------------+
| 设置插件菜单     |<----| 注册插件         |<----| 加载插件模块     |
+------------------+     +------------------+     +------------------+
```

### 2.6 事件总线通信流程

```
+------------------+     +------------------+     +------------------+
| 发送方触发事件   |---->| 事件总线接收事件 |---->| 发射信号         |
+------------------+     +------------------+     +------------------+
                                                           |
                                                           v
+------------------+     +------------------+
| 接收方处理事件   |<----| 接收方接收信号   |
+------------------+     +------------------+
```
