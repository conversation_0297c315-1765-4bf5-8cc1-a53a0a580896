@echo off
echo Starting RunSim Dashboard Server...
echo.

cd /d "%~dp0"
echo Current directory: %CD%
echo.

echo Testing Python...
python --version
if errorlevel 1 (
    echo ERROR: Python not found!
    pause
    exit /b 1
)

echo.
echo Testing Flask import...
python -c "import flask; print('Flask version:', flask.__version__)"
if errorlevel 1 (
    echo ERROR: Flask not found!
    pause
    exit /b 1
)

echo.
echo Starting Flask application...
python app.py

echo.
echo Server stopped.
pause
