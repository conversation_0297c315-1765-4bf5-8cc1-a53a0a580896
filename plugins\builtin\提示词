开发一款EDA仿真log解析日志插件，用于解析错误日志并汇总合并相似错误日志，方便定位问题。具体要求如下：
1. 使用PyQt5实现GUI界面，默认使用Case tab的仿真文件，如果不存在则弹出对话框有用户选择仿真log
2. 支持VCS和XRUN的仿真log文件解析
3. 读取log文件，解析出错误日志，通用错误日志格式如下：
    - UVM_ERROR：UVM错误日志，格式为：UVM_ERROR @ 100ns: Error Message
    - UVM_FATAL：UVM错误日志，格式为：UVM_FATAL @ 100ns: Error Message
    - UVM_WARNING：UVM警告日志，格式为：UVM_WARNING @ 100ns: Error Message
    - SPRD_ERROR：SPRD错误日志，格式为：SPRD_ERROR @ 100ns: [test] Error Message
    - SPRD_FATAL：SPRD错误日志，格式为：SPRD_FATAL @ 100ns: [test] Error Message
    - SPRD_WARNING：SPRD警告日志，格式为：SPRD_WARNING @ 100ns: [test] Error Message
4. XRUN仿真log专有错误：
    - 断言错误，格式为：*E, Assertion Error, [file:line]
5. VCS仿真log专有错误：
    - 断言错误，格式为：** Error: [file:line] Assertion Error
    - CDC断言错误，格式为：Error: "file" line: Assertion Error
    - SDC错误，格式为：[no.] [FP]-False Path错误类型：@ Time: 发生错误时间（单位是ns/ps/fs）. 接下来几行均为错误信息
                      [no.] [MCP]-Multiple Clock Path错误类型：@ Time: 发生错误时间（单位是ns/ps/fs）. 接下来几行均为错误信息
