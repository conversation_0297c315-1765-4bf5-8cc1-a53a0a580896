"""
BUG管理数据模型

该模块提供BUG相关的数据操作功能，包括：
- BUG的增删改查操作
- BUG统计分析
- BUG状态管理
- BUG趋势分析
"""

import os
import sqlite3
import logging
import importlib.util
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Any, Tuple

logger = logging.getLogger(__name__)

# 动态导入database模块，避免模块冲突
def _get_database_module():
    """获取database模块"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    database_file = os.path.join(current_dir, 'database.py')
    spec = importlib.util.spec_from_file_location("bug_database", database_file)
    database_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(database_module)
    return database_module

# 获取get_db_manager函数
_db_module = _get_database_module()
get_db_manager = _db_module.get_db_manager

class BugModel:
    """BUG数据模型类"""

    def __init__(self, db_path: str):
        """
        初始化BUG模型

        Args:
            db_path: 数据库文件路径
        """
        self.db_manager = get_db_manager(db_path)

    def create_bug(self, bug_data: Dict[str, Any]) -> Optional[int]:
        """
        创建新的BUG记录

        Args:
            bug_data: BUG数据字典

        Returns:
            Optional[int]: 创建成功返回BUG ID，失败返回None
        """
        try:
            logger.info(f"开始创建BUG，数据: {bug_data}")

            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()

                # 验证必填字段
                required_fields = ['bug_id', 'description']
                for field in required_fields:
                    if not bug_data.get(field):
                        error_msg = f"缺少必填字段: {field}"
                        logger.error(error_msg)
                        raise ValueError(error_msg)

                # 检查BUG ID是否已存在
                cursor.execute('SELECT id FROM bugs WHERE bug_id = ?', (bug_data['bug_id'],))
                existing_bug = cursor.fetchone()
                if existing_bug:
                    error_msg = f"BUG ID {bug_data['bug_id']} 已存在"
                    logger.error(error_msg)
                    raise ValueError(error_msg)

                # 验证项目ID是否存在
                project_id = bug_data.get('project_id', 1)
                cursor.execute('SELECT id FROM projects WHERE id = ?', (project_id,))
                if not cursor.fetchone():
                    logger.warning(f"项目ID {project_id} 不存在，使用默认项目")
                    project_id = 1

                # 插入BUG记录
                insert_sql = '''
                    INSERT INTO bugs (
                        project_id, bug_id, bug_type, submit_sys, verification_stage,
                        description, discovery_platform, discovery_case, severity,
                        status, submitter, verifier, submit_date, fix_date
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                '''

                values = (
                    project_id,
                    bug_data['bug_id'].strip(),
                    bug_data.get('bug_type', '') or '',
                    bug_data.get('submit_sys', '') or '',
                    bug_data.get('verification_stage', '') or '',
                    bug_data['description'].strip(),
                    bug_data.get('discovery_platform', '') or '',
                    bug_data.get('discovery_case', '') or '',
                    bug_data.get('severity', 'Medium') or 'Medium',
                    bug_data.get('status', 'Open') or 'Open',
                    bug_data.get('submitter', '') or '',
                    bug_data.get('verifier', '') or '',
                    bug_data.get('submit_date') if bug_data.get('submit_date') else None,
                    bug_data.get('fix_date') if bug_data.get('fix_date') else None
                )

                logger.info(f"执行SQL插入，参数: {values}")
                cursor.execute(insert_sql, values)
                bug_id = cursor.lastrowid
                conn.commit()

                logger.info(f"创建BUG成功: {bug_data['bug_id']} (ID: {bug_id})")
                return bug_id

        except ValueError as ve:
            # 业务逻辑错误，不记录堆栈
            logger.error(f"创建BUG业务逻辑错误: {str(ve)}")
            return None
        except Exception as e:
            # 系统错误，记录完整堆栈
            logger.error(f"创建BUG系统错误: {str(e)}", exc_info=True)
            return None

    def get_bug_by_id(self, bug_id: int) -> Optional[Dict[str, Any]]:
        """
        根据ID获取BUG信息

        Args:
            bug_id: BUG数据库ID

        Returns:
            Optional[Dict[str, Any]]: BUG信息字典，不存在返回None
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM bugs WHERE id = ?', (bug_id,))
                row = cursor.fetchone()

                if row:
                    return dict(row)
                return None

        except Exception as e:
            logger.error(f"获取BUG信息失败: {str(e)}")
            return None

    def get_bug_by_bug_id(self, bug_id_str: str) -> Optional[Dict[str, Any]]:
        """
        根据BUG ID字符串获取BUG信息

        Args:
            bug_id_str: BUG ID字符串

        Returns:
            Optional[Dict[str, Any]]: BUG信息字典，不存在返回None
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM bugs WHERE bug_id = ?', (bug_id_str,))
                row = cursor.fetchone()

                if row:
                    return dict(row)
                return None

        except Exception as e:
            logger.error(f"获取BUG信息失败: {str(e)}")
            return None

    def update_bug(self, bug_id: int, update_data: Dict[str, Any]) -> bool:
        """
        更新BUG信息

        Args:
            bug_id: BUG数据库ID
            update_data: 更新数据字典

        Returns:
            bool: 更新是否成功
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()

                # 构建更新SQL
                update_fields = []
                values = []

                allowed_fields = [
                    'bug_type', 'submit_sys', 'verification_stage', 'description',
                    'discovery_platform', 'discovery_case', 'severity', 'status',
                    'submitter', 'verifier', 'submit_date', 'fix_date'
                ]

                for field in allowed_fields:
                    if field in update_data:
                        update_fields.append(f"{field} = ?")
                        values.append(update_data[field])

                if not update_fields:
                    logger.warning("没有可更新的字段")
                    return False

                # 添加更新时间
                update_fields.append("updated_at = CURRENT_TIMESTAMP")
                values.append(bug_id)

                update_sql = f"UPDATE bugs SET {', '.join(update_fields)} WHERE id = ?"
                cursor.execute(update_sql, values)

                if cursor.rowcount > 0:
                    conn.commit()
                    logger.info(f"更新BUG成功: ID {bug_id}")
                    return True
                else:
                    logger.warning(f"BUG不存在: ID {bug_id}")
                    return False

        except Exception as e:
            logger.error(f"更新BUG失败: {str(e)}")
            return False

    def delete_bug(self, bug_id: int) -> bool:
        """
        删除BUG记录

        Args:
            bug_id: BUG数据库ID

        Returns:
            bool: 删除是否成功
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM bugs WHERE id = ?', (bug_id,))

                if cursor.rowcount > 0:
                    conn.commit()
                    logger.info(f"删除BUG成功: ID {bug_id}")
                    return True
                else:
                    logger.warning(f"BUG不存在: ID {bug_id}")
                    return False

        except Exception as e:
            logger.error(f"删除BUG失败: {str(e)}")
            return False

    def get_bugs_list(self, project_id: Optional[int] = None, status: Optional[str] = None,
                     page: int = 1, page_size: int = 20) -> Tuple[List[Dict[str, Any]], int]:
        """
        获取BUG列表（分页）

        Args:
            project_id: 项目ID过滤
            status: 状态过滤
            page: 页码（从1开始）
            page_size: 每页大小

        Returns:
            Tuple[List[Dict[str, Any]], int]: (BUG列表, 总数量)
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()

                # 构建查询条件
                where_conditions = []
                params = []

                if project_id:
                    where_conditions.append("project_id = ?")
                    params.append(project_id)

                if status:
                    where_conditions.append("status = ?")
                    params.append(status)

                where_clause = ""
                if where_conditions:
                    where_clause = "WHERE " + " AND ".join(where_conditions)

                # 获取总数量
                count_sql = f"SELECT COUNT(*) FROM bugs {where_clause}"
                cursor.execute(count_sql, params)
                total_count = cursor.fetchone()[0]

                # 获取分页数据
                offset = (page - 1) * page_size
                list_sql = f'''
                    SELECT * FROM bugs {where_clause}
                    ORDER BY created_at DESC
                    LIMIT ? OFFSET ?
                '''
                cursor.execute(list_sql, params + [page_size, offset])
                rows = cursor.fetchall()

                bugs = [dict(row) for row in rows]
                return bugs, total_count

        except Exception as e:
            logger.error(f"获取BUG列表失败: {str(e)}")
            return [], 0

    def get_bug_statistics(self, project_id: Optional[int] = None) -> Dict[str, Any]:
        """
        获取BUG统计信息

        Args:
            project_id: 项目ID过滤

        Returns:
            Dict[str, Any]: 统计信息字典
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()

                where_clause = ""
                params = []
                if project_id:
                    where_clause = "WHERE project_id = ?"
                    params = [project_id]

                # 总BUG数量
                cursor.execute(f"SELECT COUNT(*) FROM bugs {where_clause}", params)
                total_bugs = cursor.fetchone()[0]

                # 按状态统计
                status_sql = f'''
                    SELECT status, COUNT(*) as count
                    FROM bugs {where_clause}
                    GROUP BY status
                '''
                cursor.execute(status_sql, params)
                status_stats = {row[0]: row[1] for row in cursor.fetchall()}

                # 按严重程度统计
                severity_sql = f'''
                    SELECT severity, COUNT(*) as count
                    FROM bugs {where_clause}
                    GROUP BY severity
                '''
                cursor.execute(severity_sql, params)
                severity_stats = {row[0]: row[1] for row in cursor.fetchall()}

                # 按类型统计
                type_sql = f'''
                    SELECT bug_type, COUNT(*) as count
                    FROM bugs {where_clause}
                    GROUP BY bug_type
                '''
                cursor.execute(type_sql, params)
                type_stats = {row[0] or '未分类': row[1] for row in cursor.fetchall()}

                return {
                    'total_bugs': total_bugs,
                    'status_distribution': status_stats,
                    'severity_distribution': severity_stats,
                    'type_distribution': type_stats,
                    'open_bugs': status_stats.get('Open', 0),
                    'fixed_bugs': status_stats.get('Fixed', 0),
                    'closed_bugs': status_stats.get('Closed', 0)
                }

        except Exception as e:
            logger.error(f"获取BUG统计失败: {str(e)}")
            return {
                'total_bugs': 0,
                'status_distribution': {},
                'severity_distribution': {},
                'type_distribution': {},
                'open_bugs': 0,
                'fixed_bugs': 0,
                'closed_bugs': 0
            }

    def get_bug_trend_data(self, days: int = 30, project_id: Optional[int] = None, unit: str = 'day') -> Dict[str, Any]:
        """
        获取BUG趋势数据

        Args:
            days: 统计天数
            project_id: 项目ID过滤
            unit: 统计单位 ('day' 或 'week')

        Returns:
            Dict[str, Any]: 趋势数据
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()

                # 计算日期范围
                end_date = datetime.now().date()
                start_date = end_date - timedelta(days=days-1)

                where_clause = "WHERE submit_date >= ? AND submit_date <= ?"
                params = [start_date.isoformat(), end_date.isoformat()]

                if project_id:
                    where_clause += " AND project_id = ?"
                    params.append(project_id)

                if unit == 'week':
                    # 按周统计
                    new_bugs_sql = f'''
                        SELECT
                            strftime('%Y-W%W', submit_date) as week_label,
                            COUNT(*) as count
                        FROM bugs {where_clause}
                        GROUP BY strftime('%Y-W%W', submit_date)
                        ORDER BY week_label
                    '''
                    cursor.execute(new_bugs_sql, params)
                    new_bugs_data = {row[0]: int(row[1]) for row in cursor.fetchall()}

                    # 按周统计修复BUG
                    fixed_where = where_clause.replace("submit_date", "fix_date")
                    fixed_bugs_sql = f'''
                        SELECT
                            strftime('%Y-W%W', fix_date) as week_label,
                            COUNT(*) as count
                        FROM bugs {fixed_where} AND fix_date IS NOT NULL
                        GROUP BY strftime('%Y-W%W', fix_date)
                        ORDER BY week_label
                    '''
                    cursor.execute(fixed_bugs_sql, params)
                    fixed_bugs_data = {row[0]: int(row[1]) for row in cursor.fetchall()}

                    # 生成周标签序列
                    labels = []
                    new_bugs = []
                    fixed_bugs = []

                    current_date = start_date
                    while current_date <= end_date:
                        week_label = current_date.strftime('%Y-W%W')
                        if week_label not in labels:
                            labels.append(week_label)
                            new_bugs.append(new_bugs_data.get(week_label, 0))
                            fixed_bugs.append(fixed_bugs_data.get(week_label, 0))
                        current_date += timedelta(days=7)

                else:
                    # 按日期统计新增BUG
                    new_bugs_sql = f'''
                        SELECT submit_date, COUNT(*) as count
                        FROM bugs {where_clause}
                        GROUP BY submit_date
                        ORDER BY submit_date
                    '''
                    cursor.execute(new_bugs_sql, params)
                    new_bugs_data = {row[0]: int(row[1]) for row in cursor.fetchall()}

                    # 按日期统计修复BUG
                    fixed_where = where_clause.replace("submit_date", "fix_date")
                    fixed_bugs_sql = f'''
                        SELECT fix_date, COUNT(*) as count
                        FROM bugs {fixed_where} AND fix_date IS NOT NULL
                        GROUP BY fix_date
                        ORDER BY fix_date
                    '''
                    cursor.execute(fixed_bugs_sql, params)
                    fixed_bugs_data = {row[0]: int(row[1]) for row in cursor.fetchall()}

                    # 生成完整的日期序列
                    labels = []
                    new_bugs = []
                    fixed_bugs = []

                    current_date = start_date
                    while current_date <= end_date:
                        date_str = current_date.isoformat()
                        labels.append(date_str)
                        new_bugs.append(new_bugs_data.get(date_str, 0))
                        fixed_bugs.append(fixed_bugs_data.get(date_str, 0))
                        current_date += timedelta(days=1)

                return {
                    'labels': labels,
                    'new_bugs': new_bugs,
                    'fixed_bugs': fixed_bugs,
                    'unit': unit
                }

        except Exception as e:
            logger.error(f"获取BUG趋势数据失败: {str(e)}")
            return {
                'labels': [],
                'new_bugs': [],
                'fixed_bugs': [],
                'unit': unit
            }
