@echo off
setlocal enabledelayedexpansion

REM Set UTF-8 code page for proper character encoding
chcp 65001 >nul 2>&1

REM RunSim Simulation Script - Windows Batch File (Production Version)
REM Call Python-based runsim simulator

REM Get script directory
set "SCRIPT_DIR=%~dp0"
set "PYTHON_SCRIPT=%SCRIPT_DIR%runsim.py"

REM Check if runsim.py exists
if not exist "%PYTHON_SCRIPT%" (
    echo Error: runsim.py not found in %SCRIPT_DIR%
    exit /b 1
)

REM Try different Python commands in order of preference
set "PYTHON_FOUND=0"

REM Method 1: Try 'python' command
where python >nul 2>&1
if !errorlevel! equ 0 (
    python "%PYTHON_SCRIPT%" %*
    set "PYTHON_FOUND=1"
    goto :end
)

REM Method 2: Try 'python3' command
where python3 >nul 2>&1
if !errorlevel! equ 0 (
    python3 "%PYTHON_SCRIPT%" %*
    set "PYTHON_FOUND=1"
    goto :end
)

REM Method 3: Try 'py' launcher
where py >nul 2>&1
if !errorlevel! equ 0 (
    py "%PYTHON_SCRIPT%" %*
    set "PYTHON_FOUND=1"
    goto :end
)

REM Method 4: Try common Python installation paths
set "PYTHON_PATHS=C:\Program Files\Python312\python.exe"
set "PYTHON_PATHS=!PYTHON_PATHS! C:\Program Files\Python311\python.exe"
set "PYTHON_PATHS=!PYTHON_PATHS! C:\Program Files\Python310\python.exe"
set "PYTHON_PATHS=!PYTHON_PATHS! C:\Program Files\Python39\python.exe"
set "PYTHON_PATHS=!PYTHON_PATHS! C:\Program Files\Python38\python.exe"
set "PYTHON_PATHS=!PYTHON_PATHS! C:\Python312\python.exe"
set "PYTHON_PATHS=!PYTHON_PATHS! C:\Python311\python.exe"
set "PYTHON_PATHS=!PYTHON_PATHS! C:\Python310\python.exe"
set "PYTHON_PATHS=!PYTHON_PATHS! C:\Python39\python.exe"
set "PYTHON_PATHS=!PYTHON_PATHS! C:\Python38\python.exe"

for %%P in (!PYTHON_PATHS!) do (
    if exist "%%P" (
        "%%P" "%PYTHON_SCRIPT%" %*
        set "PYTHON_FOUND=1"
        goto :end
    )
)

REM If no Python found, show error message
echo ERROR: Python interpreter not found!
echo Please ensure Python is installed and added to PATH.
echo Install Python from: https://www.python.org/downloads/
exit /b 1

:end
endlocal
