"""
仪表盘状态更新器

该模块负责将仿真执行状态更新到仪表盘数据库中。
主要功能包括：
1. 更新用例开始时间和状态
2. 更新用例结束时间和状态
3. 根据命令参数确定更新的状态列
4. 处理不同类型用例的状态映射
"""

import os
import logging
import requests
import sqlite3
from datetime import datetime
from typing import Dict, Optional, Any
from utils.simulation_monitor import CommandParser

# 配置日志
logger = logging.getLogger(__name__)


class DashboardUpdater:
    """仪表盘状态更新器"""
    
    def __init__(self, dashboard_port: int = 5001):
        """
        初始化仪表盘更新器
        
        Args:
            dashboard_port: 仪表盘服务端口
        """
        self.dashboard_port = dashboard_port
        self.base_url = f"http://127.0.0.1:{dashboard_port}"
        
        # 状态映射
        self.status_mapping = {
            'started': 'On-Going',
            'passed': 'PASS',
            'failed': 'FAIL'
        }
        
        # 列映射 - 根据用例类型确定更新哪个状态列
        self.column_mapping = {
            'subsys': 'subsys_status',      # N列 - Subsys级用例状态
            'top': 'top_status',            # P列 - TOP级用例状态  
            'post_subsys': 'post_subsys_status',  # R列 - Subsys级后仿用例状态
            'post_top': 'post_top_status'   # T列 - TOP级后仿用例状态
        }
    
    def update_case_start(self, case_name: str, command: str, params: Dict[str, str]):
        """
        更新用例开始状态
        
        Args:
            case_name: 用例名称
            command: 执行命令
            params: 命令参数字典
        """
        try:
            # 确定用例类型
            case_type = CommandParser.determine_case_type(params)
            
            # 准备更新数据
            update_data = {
                'case_name': case_name,
                'case_type': case_type,
                'status': self.status_mapping['started'],
                'start_time': datetime.now().isoformat(),
                'command': command,
                'updated_by': 'runsim_gui'
            }
            
            # 通过API更新状态
            success = self._update_via_api(update_data)
            
            if not success:
                # API更新失败，尝试直接数据库更新
                self._update_via_database(update_data)
            
            logger.info(f"用例开始状态更新成功: {case_name} -> {self.status_mapping['started']}")
            
        except Exception as e:
            logger.error(f"更新用例开始状态失败: {case_name}, 错误: {e}")
    
    def update_case_finish(self, case_name: str, success: bool, message: str):
        """
        更新用例结束状态
        
        Args:
            case_name: 用例名称
            success: 是否成功
            message: 结果消息
        """
        try:
            # 确定最终状态
            final_status = self.status_mapping['passed'] if success else self.status_mapping['failed']
            
            # 准备更新数据
            update_data = {
                'case_name': case_name,
                'status': final_status,
                'message': message,
                'updated_by': 'runsim_gui'
            }
            
            # 只有成功时才更新结束时间
            if success:
                update_data['end_time'] = datetime.now().isoformat()
            
            # 通过API更新状态
            success_api = self._update_via_api(update_data)
            
            if not success_api:
                # API更新失败，尝试直接数据库更新
                self._update_via_database(update_data)
            
            logger.info(f"用例结束状态更新成功: {case_name} -> {final_status}")
            
        except Exception as e:
            logger.error(f"更新用例结束状态失败: {case_name}, 错误: {e}")
    
    def _update_via_api(self, update_data: Dict[str, Any]) -> bool:
        """
        通过API更新用例状态
        
        Args:
            update_data: 更新数据
            
        Returns:
            bool: 是否更新成功
        """
        try:
            # 检查仪表盘服务是否可用
            if not self._check_dashboard_service():
                return False
            
            # 发送更新请求
            url = f"{self.base_url}/api/testplan/update_from_runsim"
            response = requests.post(url, json=update_data, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    logger.debug(f"API更新成功: {update_data['case_name']}")
                    return True
                else:
                    logger.warning(f"API更新失败: {result.get('error', '未知错误')}")
                    return False
            else:
                logger.warning(f"API请求失败: HTTP {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            logger.warning(f"API请求异常: {e}")
            return False
        except Exception as e:
            logger.error(f"API更新异常: {e}")
            return False
    
    def _update_via_database(self, update_data: Dict[str, Any]):
        """
        直接通过数据库更新用例状态
        
        Args:
            update_data: 更新数据
        """
        try:
            # 获取数据库路径
            db_path = self._get_database_path()
            if not db_path or not os.path.exists(db_path):
                logger.warning("数据库文件不存在，跳过直接数据库更新")
                return
            
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                
                # 查找用例
                cursor.execute(
                    "SELECT id FROM test_cases WHERE case_name = ?",
                    (update_data['case_name'],)
                )
                result = cursor.fetchone()
                
                if not result:
                    logger.warning(f"数据库中未找到用例: {update_data['case_name']}")
                    return
                
                case_id = result[0]
                
                # 构建更新SQL
                update_fields = []
                update_values = []
                
                # 根据用例类型确定更新的状态列
                case_type = update_data.get('case_type')
                if case_type and case_type in self.column_mapping:
                    status_column = self.column_mapping[case_type]
                    update_fields.append(f"{status_column} = ?")
                    update_values.append(update_data['status'])
                
                # 更新时间字段
                if 'start_time' in update_data:
                    update_fields.append("start_time = ?")
                    update_values.append(update_data['start_time'])
                
                if 'end_time' in update_data:
                    update_fields.append("end_time = ?")
                    update_values.append(update_data['end_time'])
                
                # 更新修改时间
                update_fields.append("updated_at = ?")
                update_values.append(datetime.now().isoformat())
                
                # 执行更新
                if update_fields:
                    update_values.append(case_id)
                    sql = f"UPDATE test_cases SET {', '.join(update_fields)} WHERE id = ?"
                    cursor.execute(sql, update_values)
                    conn.commit()
                    
                    logger.info(f"数据库直接更新成功: {update_data['case_name']}")
                
        except Exception as e:
            logger.error(f"数据库直接更新失败: {e}")
    
    def _check_dashboard_service(self) -> bool:
        """
        检查仪表盘服务是否可用
        
        Returns:
            bool: 服务是否可用
        """
        try:
            response = requests.get(f"{self.base_url}/api/health", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def _get_database_path(self) -> Optional[str]:
        """
        获取数据库文件路径
        
        Returns:
            Optional[str]: 数据库路径
        """
        try:
            # 尝试从仪表盘配置中获取数据库路径
            current_dir = os.getcwd()
            db_path = os.path.join(current_dir, 'dashboard.db')
            
            if os.path.exists(db_path):
                return db_path
            
            # 尝试其他可能的路径
            possible_paths = [
                os.path.join(current_dir, 'plugins', 'builtin', 'dashboard_web', 'data', 'dashboard.db'),
                os.path.join(current_dir, 'data', 'dashboard.db')
            ]
            
            for path in possible_paths:
                if os.path.exists(path):
                    return path
            
            return None
            
        except Exception as e:
            logger.error(f"获取数据库路径失败: {e}")
            return None
    
    def test_connection(self) -> bool:
        """
        测试与仪表盘的连接
        
        Returns:
            bool: 连接是否正常
        """
        try:
            # 测试API连接
            api_available = self._check_dashboard_service()
            
            # 测试数据库连接
            db_path = self._get_database_path()
            db_available = db_path is not None and os.path.exists(db_path)
            
            logger.info(f"仪表盘连接测试 - API: {'可用' if api_available else '不可用'}, "
                       f"数据库: {'可用' if db_available else '不可用'}")
            
            return api_available or db_available
            
        except Exception as e:
            logger.error(f"测试仪表盘连接失败: {e}")
            return False


# 全局实例
_dashboard_updater = None


def get_dashboard_updater() -> DashboardUpdater:
    """获取仪表盘更新器实例"""
    global _dashboard_updater
    if _dashboard_updater is None:
        _dashboard_updater = DashboardUpdater()
    return _dashboard_updater


def initialize_dashboard_updater(dashboard_port: int = 5001) -> DashboardUpdater:
    """
    初始化仪表盘更新器
    
    Args:
        dashboard_port: 仪表盘服务端口
        
    Returns:
        DashboardUpdater: 更新器实例
    """
    global _dashboard_updater
    _dashboard_updater = DashboardUpdater(dashboard_port)
    return _dashboard_updater
