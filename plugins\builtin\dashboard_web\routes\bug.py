"""
BUG管理API路由

该模块提供BUG管理相关的API接口，包括：
- BUG的增删改查操作
- BUG列表查询和分页
- BUG统计分析
- BUG趋势数据
"""

import os
import sys
import logging
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app

# 确保能找到模块
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# 尝试导入，如果失败则使用绝对路径
try:
    from models.bug import BugModel
except ImportError:
    sys.path.insert(0, os.path.join(parent_dir, 'models'))
    from bug import BugModel

logger = logging.getLogger(__name__)

# 创建蓝图
bug_bp = Blueprint('bug', __name__)

def get_bug_model():
    """获取BUG模型实例"""
    return BugModel(current_app.config['DATABASE_PATH'])

@bug_bp.route('/bugs', methods=['GET'])
def get_bugs():
    """
    获取BUG列表

    Query Parameters:
        - page: 页码（默认1）
        - page_size: 每页大小（默认20）
        - status: 状态过滤
        - project_id: 项目ID过滤
    """
    try:
        # 获取查询参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))
        status = request.args.get('status')
        project_id = request.args.get('project_id')

        if project_id:
            project_id = int(project_id)

        # 参数验证
        if page < 1:
            page = 1
        if page_size < 1 or page_size > 100:
            page_size = 20

        bug_model = get_bug_model()
        bugs, total_count = bug_model.get_bugs_list(
            project_id=project_id,
            status=status,
            page=page,
            page_size=page_size
        )

        # 计算分页信息
        total_pages = (total_count + page_size - 1) // page_size

        return jsonify({
            'success': True,
            'data': {
                'bugs': bugs,
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total_count': total_count,
                    'total_pages': total_pages,
                    'has_next': page < total_pages,
                    'has_prev': page > 1
                }
            }
        })

    except Exception as e:
        logger.error(f"获取BUG列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取BUG列表失败: {str(e)}'
        }), 500

@bug_bp.route('/bugs', methods=['POST'])
def create_bug():
    """创建新的BUG记录"""
    try:
        data = request.get_json()
        logger.info(f"收到创建BUG请求，数据: {data}")

        if not data:
            logger.warning("创建BUG失败: 未提供数据")
            return jsonify({
                'success': False,
                'message': '请提供BUG数据'
            }), 400

        # 验证必填字段
        required_fields = ['bug_id', 'description']
        for field in required_fields:
            if not data.get(field) or data.get(field).strip() == '':
                logger.warning(f"创建BUG失败: 缺少必填字段 {field}")
                return jsonify({
                    'success': False,
                    'message': f'缺少必填字段: {field}'
                }), 400

        # 处理日期字段 - 允许null值
        for date_field in ['submit_date', 'fix_date']:
            date_value = data.get(date_field)
            if date_value and date_value != '':
                try:
                    datetime.strptime(date_value, '%Y-%m-%d')
                except ValueError:
                    logger.warning(f"创建BUG失败: {date_field}格式错误 - {date_value}")
                    return jsonify({
                        'success': False,
                        'message': f'{date_field}格式错误，应为YYYY-MM-DD'
                    }), 400
            elif date_value == '':
                # 将空字符串转换为None
                data[date_field] = None

        # 清理数据 - 移除空字符串字段（除了必填字段）
        cleaned_data = {}
        for key, value in data.items():
            if key in required_fields:
                cleaned_data[key] = value.strip() if isinstance(value, str) else value
            elif value is not None and value != '':
                cleaned_data[key] = value.strip() if isinstance(value, str) else value
            elif key in ['submit_date', 'fix_date']:
                cleaned_data[key] = None

        logger.info(f"清理后的BUG数据: {cleaned_data}")

        bug_model = get_bug_model()
        bug_id = bug_model.create_bug(cleaned_data)

        if bug_id:
            logger.info(f"BUG创建成功，ID: {bug_id}")
            return jsonify({
                'success': True,
                'message': 'BUG创建成功',
                'data': {'id': bug_id}
            })
        else:
            logger.error("BUG创建失败: create_bug返回None")
            return jsonify({
                'success': False,
                'message': 'BUG创建失败，请检查数据格式或联系管理员'
            }), 500

    except Exception as e:
        logger.error(f"创建BUG异常: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'创建BUG失败: {str(e)}'
        }), 500

@bug_bp.route('/bugs/<int:bug_id>', methods=['GET'])
def get_bug(bug_id):
    """获取单个BUG信息"""
    try:
        bug_model = get_bug_model()
        bug = bug_model.get_bug_by_id(bug_id)

        if bug:
            return jsonify({
                'success': True,
                'data': bug
            })
        else:
            return jsonify({
                'success': False,
                'message': 'BUG不存在'
            }), 404

    except Exception as e:
        logger.error(f"获取BUG信息失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取BUG信息失败: {str(e)}'
        }), 500

@bug_bp.route('/bugs/<int:bug_id>', methods=['PUT'])
def update_bug(bug_id):
    """更新BUG信息"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'message': '请提供更新数据'
            }), 400

        # 处理日期字段
        for date_field in ['submit_date', 'fix_date']:
            if data.get(date_field):
                try:
                    datetime.strptime(data[date_field], '%Y-%m-%d')
                except ValueError:
                    return jsonify({
                        'success': False,
                        'message': f'{date_field}格式错误，应为YYYY-MM-DD'
                    }), 400

        bug_model = get_bug_model()
        success = bug_model.update_bug(bug_id, data)

        if success:
            return jsonify({
                'success': True,
                'message': 'BUG更新成功'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'BUG更新失败或不存在'
            }), 404

    except Exception as e:
        logger.error(f"更新BUG失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'更新BUG失败: {str(e)}'
        }), 500

@bug_bp.route('/bugs/<int:bug_id>', methods=['DELETE'])
def delete_bug(bug_id):
    """删除BUG记录"""
    try:
        bug_model = get_bug_model()
        success = bug_model.delete_bug(bug_id)

        if success:
            return jsonify({
                'success': True,
                'message': 'BUG删除成功'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'BUG删除失败或不存在'
            }), 404

    except Exception as e:
        logger.error(f"删除BUG失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'删除BUG失败: {str(e)}'
        }), 500

@bug_bp.route('/bugs/statistics', methods=['GET'])
def get_bug_statistics():
    """获取BUG统计信息"""
    try:
        project_id = request.args.get('project_id')
        if project_id:
            project_id = int(project_id)

        bug_model = get_bug_model()
        stats = bug_model.get_bug_statistics(project_id)

        return jsonify({
            'success': True,
            'data': stats
        })

    except Exception as e:
        logger.error(f"获取BUG统计失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取BUG统计失败: {str(e)}'
        }), 500

@bug_bp.route('/bugs/trend', methods=['GET'])
def get_bug_trend():
    """获取BUG趋势数据"""
    try:
        days = int(request.args.get('days', 30))
        project_id = request.args.get('project_id')
        unit = request.args.get('unit', 'day')  # 新增：统计单位参数

        if project_id:
            project_id = int(project_id)

        if days < 1 or days > 365:
            days = 30

        # 验证统计单位
        if unit not in ['day', 'week']:
            unit = 'day'

        bug_model = get_bug_model()
        trend_data = bug_model.get_bug_trend_data(days, project_id, unit)

        return jsonify({
            'success': True,
            'data': trend_data
        })

    except Exception as e:
        logger.error(f"获取BUG趋势数据失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取BUG趋势数据失败: {str(e)}'
        }), 500
