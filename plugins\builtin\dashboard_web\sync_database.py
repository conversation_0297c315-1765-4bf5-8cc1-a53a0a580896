#!/usr/bin/env python
"""
数据库同步脚本
确保独立启动和插件启动使用相同的数据库数据
"""

import os
import sys
import sqlite3
import shutil
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def sync_database():
    """同步数据库"""
    print("🔄 数据库同步工具")
    print("=" * 50)

    # 导入配置模块获取动态数据库路径
    try:
        from config import get_database_path
        db_path = get_database_path()
    except ImportError:
        # 向后兼容：如果无法导入配置模块，使用默认路径
        db_path = os.path.join(current_dir, 'data', 'dashboard.db')
    
    print(f"📁 数据库文件路径: {db_path}")
    
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在，需要先创建")
        return False
    
    # 检查数据库内容
    try:
        with sqlite3.connect(db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 检查用例数量
            cursor.execute("SELECT COUNT(*) as count FROM test_cases")
            case_count = cursor.fetchone()['count']
            
            # 检查BUG数量
            cursor.execute("SELECT COUNT(*) as count FROM bugs")
            bug_count = cursor.fetchone()['count']
            
            print(f"📊 当前数据库状态:")
            print(f"   用例数量: {case_count}")
            print(f"   BUG数量: {bug_count}")
            
            if case_count == 0:
                print("\n⚠️ 数据库中没有用例数据")
                print("建议先通过独立启动方式导入TestPlan数据")
                return False
            
            # 显示一些示例用例
            cursor.execute("SELECT case_name, subsys_status, top_status FROM test_cases LIMIT 5")
            cases = cursor.fetchall()
            
            print(f"\n📋 示例用例:")
            for case in cases:
                print(f"   {case['case_name']} - subsys:{case['subsys_status']}, top:{case['top_status']}")
            
            print(f"\n✅ 数据库同步完成!")
            print(f"现在独立启动和插件启动都会使用相同的数据库文件")
            return True
            
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")
        return False

def backup_database():
    """备份数据库"""
    # 获取动态数据库路径
    try:
        from config import get_database_path
        db_path = get_database_path()
    except ImportError:
        db_path = os.path.join(current_dir, 'data', 'dashboard.db')
    
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在，无法备份")
        return False
    
    # 创建备份文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_path = os.path.join(current_dir, 'data', f'dashboard.db.backup_{timestamp}')
    
    try:
        shutil.copy2(db_path, backup_path)
        print(f"✅ 数据库备份成功: {os.path.basename(backup_path)}")
        return True
    except Exception as e:
        print(f"❌ 数据库备份失败: {e}")
        return False

def reset_database():
    """重置数据库"""
    print("\n🗑️ 重置数据库")
    print("⚠️ 这将删除所有现有数据!")
    
    response = input("确认重置数据库? (y/N): ")
    if response.lower() != 'y':
        print("取消重置操作")
        return False
    
    # 先备份
    if backup_database():
        print("已创建备份，继续重置...")
    
    try:
        from models.database import init_database

        # 获取动态数据库路径
        try:
            from config import get_database_path
            db_path = get_database_path()
        except ImportError:
            db_path = os.path.join(current_dir, 'data', 'dashboard.db')
        
        # 删除现有数据库
        if os.path.exists(db_path):
            os.remove(db_path)
            print("✅ 删除现有数据库文件")
        
        # 重新初始化
        if init_database(db_path):
            print("✅ 数据库重置成功")
            return True
        else:
            print("❌ 数据库重置失败")
            return False
            
    except Exception as e:
        print(f"❌ 重置数据库失败: {e}")
        return False

def import_testplan():
    """导入TestPlan数据"""
    print("\n📥 导入TestPlan数据")
    
    template_path = os.path.join(current_dir, '..', '..', '..', 'TestPlan_Template.xlsx')
    template_path = os.path.abspath(template_path)
    
    if not os.path.exists(template_path):
        print(f"❌ TestPlan模板文件不存在: {template_path}")
        return False
    
    try:
        from utils.excel_parser import TestPlanParser
        from models.testplan import TestCaseManager
        
        # 解析Excel文件
        parser = TestPlanParser()
        test_cases, parse_info = parser.parse_file(template_path, 'TP')
        
        print(f"📊 解析结果: {len(test_cases)} 条用例")
        
        # 验证数据
        valid_cases, validation_errors = TestPlanParser.validate_test_cases(test_cases)
        
        if validation_errors:
            print(f"⚠️ 验证错误: {len(validation_errors)} 个")
            for error in validation_errors[:3]:
                print(f"   {error}")
        
        # 批量导入
        success_count, import_errors = TestCaseManager.batch_import_cases(valid_cases, 1)
        
        print(f"✅ 导入完成:")
        print(f"   成功: {success_count} 条")
        print(f"   失败: {len(import_errors)} 条")
        
        if import_errors:
            print(f"❌ 导入错误:")
            for error in import_errors[:3]:
                print(f"   {error}")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 导入TestPlan失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🛠️ RunSim Dashboard 数据库管理工具")
    print("=" * 60)
    
    while True:
        print("\n请选择操作:")
        print("1. 检查数据库状态")
        print("2. 备份数据库")
        print("3. 重置数据库")
        print("4. 导入TestPlan数据")
        print("5. 退出")
        
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == '1':
            sync_database()
        elif choice == '2':
            backup_database()
        elif choice == '3':
            reset_database()
        elif choice == '4':
            import_testplan()
        elif choice == '5':
            print("👋 再见!")
            break
        else:
            print("❌ 无效选择，请重试")

if __name__ == '__main__':
    main()
