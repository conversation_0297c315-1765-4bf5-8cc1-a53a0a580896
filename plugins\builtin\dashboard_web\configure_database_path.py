#!/usr/bin/env python3
"""
数据库路径配置工具

该工具帮助用户配置RunSim仪表板的数据库存储路径，
解决多用户环境下的权限问题。
"""

import os
import sys
import json
from pathlib import Path

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def show_current_config():
    """显示当前数据库路径配置"""
    print("📋 当前数据库路径配置")
    print("=" * 50)
    
    try:
        from config import get_database_path
        current_path = get_database_path()
        print(f"📁 当前数据库路径: {current_path}")
        print(f"📁 文件存在: {os.path.exists(current_path)}")
        print(f"📁 目录可写: {os.access(os.path.dirname(current_path), os.W_OK)}")
        
        # 显示路径来源
        env_path = os.environ.get('RUNSIM_DB_PATH')
        if env_path:
            print(f"🔧 路径来源: 环境变量 RUNSIM_DB_PATH")
        else:
            config_file = os.path.join(current_dir, 'database_config.json')
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                    config_path = config_data.get('database', {}).get('path', '').strip()
                    if config_path:
                        print(f"🔧 路径来源: 配置文件 database_config.json")
                    else:
                        print(f"🔧 路径来源: 当前工作目录")
                except:
                    print(f"🔧 路径来源: 当前工作目录")
            else:
                print(f"🔧 路径来源: 当前工作目录")
        
    except Exception as e:
        print(f"❌ 获取当前配置失败: {e}")

def set_database_path():
    """设置数据库路径"""
    print("\n📝 设置数据库路径")
    print("=" * 30)
    
    print("请选择配置方式:")
    print("1. 使用环境变量 (推荐)")
    print("2. 使用配置文件")
    print("3. 返回主菜单")
    
    choice = input("\n请输入选择 (1-3): ").strip()
    
    if choice == '1':
        set_environment_variable()
    elif choice == '2':
        set_config_file()
    elif choice == '3':
        return
    else:
        print("❌ 无效选择")

def set_environment_variable():
    """设置环境变量"""
    print("\n🔧 设置环境变量")
    print("=" * 30)
    
    print("请输入数据库路径 (可以是文件路径或目录路径):")
    print("示例:")
    print("  文件路径: C:/Users/<USER>/runsim_data/dashboard.db")
    print("  目录路径: C:/Users/<USER>/runsim_data/")
    print("  留空取消设置")
    
    path = input("\n数据库路径: ").strip()
    
    if not path:
        print("取消设置")
        return
    
    # 验证路径
    if not os.path.isabs(path):
        path = os.path.abspath(path)
    
    # 检查目录权限
    if os.path.isfile(path):
        dir_path = os.path.dirname(path)
    else:
        dir_path = path
    
    if not os.path.exists(dir_path):
        try:
            os.makedirs(dir_path, exist_ok=True)
            print(f"✅ 创建目录: {dir_path}")
        except Exception as e:
            print(f"❌ 无法创建目录: {e}")
            return
    
    if not os.access(dir_path, os.W_OK):
        print(f"❌ 目录没有写权限: {dir_path}")
        return
    
    print(f"\n✅ 路径验证通过: {path}")
    print("\n请手动设置环境变量:")
    print(f"Windows: set RUNSIM_DB_PATH={path}")
    print(f"Linux/Mac: export RUNSIM_DB_PATH={path}")
    print("\n或者将以下内容添加到系统环境变量中:")
    print(f"变量名: RUNSIM_DB_PATH")
    print(f"变量值: {path}")

def set_config_file():
    """设置配置文件"""
    print("\n📄 设置配置文件")
    print("=" * 30)
    
    config_file = os.path.join(current_dir, 'database_config.json')
    
    # 读取现有配置
    config_data = {
        "database": {
            "path": "",
            "description": "数据库文件路径配置。留空则使用默认规则：当前工作目录 > 默认路径",
            "examples": [
                "C:/Users/<USER>/runsim_data/dashboard.db",
                "/home/<USER>/runsim_data/dashboard.db",
                "./data/dashboard.db"
            ]
        },
        "backup": {
            "enabled": True,
            "max_backups": 10,
            "description": "数据库备份配置"
        },
        "permissions": {
            "check_write_access": True,
            "fallback_to_default": True,
            "description": "权限检查配置"
        }
    }
    
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
        except Exception as e:
            print(f"⚠️ 读取现有配置文件失败: {e}")
    
    print("请输入数据库路径 (可以是文件路径或目录路径):")
    print("示例:")
    print("  文件路径: C:/Users/<USER>/runsim_data/dashboard.db")
    print("  目录路径: C:/Users/<USER>/runsim_data/")
    print("  相对路径: ./data/dashboard.db")
    print("  留空使用默认规则")
    
    path = input("\n数据库路径: ").strip()
    
    # 更新配置
    config_data['database']['path'] = path
    
    # 保存配置文件
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=4, ensure_ascii=False)
        print(f"✅ 配置已保存到: {config_file}")
        
        if path:
            print(f"✅ 数据库路径已设置为: {path}")
        else:
            print("✅ 已清除自定义路径，将使用默认规则")
            
    except Exception as e:
        print(f"❌ 保存配置文件失败: {e}")

def test_database_access():
    """测试数据库访问"""
    print("\n🧪 测试数据库访问")
    print("=" * 30)
    
    try:
        from config import get_database_path
        db_path = get_database_path()
        
        print(f"📁 测试路径: {db_path}")
        
        # 测试目录创建
        db_dir = os.path.dirname(db_path)
        if not os.path.exists(db_dir):
            try:
                os.makedirs(db_dir, exist_ok=True)
                print(f"✅ 目录创建成功: {db_dir}")
            except Exception as e:
                print(f"❌ 目录创建失败: {e}")
                return False
        
        # 测试文件写入
        try:
            test_file = db_path + '.test'
            with open(test_file, 'w') as f:
                f.write('test')
            os.remove(test_file)
            print(f"✅ 文件写入测试成功")
        except Exception as e:
            print(f"❌ 文件写入测试失败: {e}")
            return False
        
        # 测试数据库初始化
        try:
            from models.database import init_database
            if init_database(db_path):
                print(f"✅ 数据库初始化测试成功")
                return True
            else:
                print(f"❌ 数据库初始化测试失败")
                return False
        except Exception as e:
            print(f"❌ 数据库初始化测试失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🛠️ RunSim Dashboard 数据库路径配置工具")
    print("=" * 60)
    print("该工具帮助您配置数据库存储路径，解决多用户环境下的权限问题。")
    
    while True:
        print("\n请选择操作:")
        print("1. 查看当前配置")
        print("2. 设置数据库路径")
        print("3. 测试数据库访问")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == '1':
            show_current_config()
        elif choice == '2':
            set_database_path()
        elif choice == '3':
            test_database_access()
        elif choice == '4':
            print("👋 再见!")
            break
        else:
            print("❌ 无效选择，请重试")

if __name__ == '__main__':
    main()
