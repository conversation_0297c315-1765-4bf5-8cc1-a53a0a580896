"""
多选下拉框组件
支持预定义选项和用户自定义输入
"""
from PyQt5.QtWidgets import (
    QComboBox, QWidget, QVBoxLayout, QHBoxLayout, 
    QCheckBox, QLineEdit, QPushButton, QFrame,
    QScrollArea, QLabel, QDialog, QDialogButtonBox
)
from PyQt5.QtCore import Qt, pyqtSignal, QEvent, QTimer
from PyQt5.QtGui import QIcon

from .memory_optimizer import get_memory_optimizer

class MultiSelectComboBox(QComboBox):
    """
    多选下拉框组件
    支持预定义选项的多选和用户自定义输入
    """
    
    # 定义信号
    selectionChanged = pyqtSignal(list)  # 选择变化信号，传递选中的选项列表
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 预定义选项
        self.predefined_options = [
            "+fsdb+autoflush",
            "+fsdb+skip_cell_instance=0", 
            "+mda=on"
        ]
        
        # 用户自定义选项
        self.custom_option_text = "用户输入"
        self.custom_input = ""
        
        # 选中状态
        self.selected_options = []
        self.custom_selected = False
        
        # 对话框引用
        self.selection_dialog = None
        
        # 设置下拉框属性
        self.setEditable(False)
        self.setMaxVisibleItems(10)
        
        # 创建下拉内容
        self.setup_dropdown()
        
        # 连接信号
        self.activated.connect(self.on_item_activated)
        
        # 添加事件过滤器
        self.installEventFilter(self)
        
        # 初始化防抖定时器
        self.debounce_timer = QTimer(self)
        self.debounce_timer.setSingleShot(True)
        self.debounce_timer.setInterval(100)  # 100ms防抖
        self.debounce_timer.timeout.connect(self.show_selection_dialog)
        
        # 注册到内存优化器
        self.memory_optimizer = get_memory_optimizer()
        if self.memory_optimizer:
            self.memory_optimizer.register_object(self, self.cleanup)
            
    def cleanup(self):
        """清理资源"""
        if self.selection_dialog:
            self.selection_dialog.deleteLater()
            self.selection_dialog = None
    
    def eventFilter(self, obj, event):
        """事件过滤器"""
        if obj is self and event.type() == QEvent.MouseButtonPress:
            # 使用防抖定时器处理点击事件
            if not self.debounce_timer.isActive():
                self.debounce_timer.start()
            return True
        return super().eventFilter(obj, event)
    
    def showPopup(self):
        """重写showPopup，改用自定义对话框"""
        if not self.debounce_timer.isActive():
            self.debounce_timer.start()
    
    def setup_dropdown(self):
        """设置下拉框内容"""
        # 创建自定义下拉内容
        self.dropdown_widget = QWidget()
        self.dropdown_layout = QVBoxLayout(self.dropdown_widget)
        self.dropdown_layout.setContentsMargins(5, 5, 5, 5)
        self.dropdown_layout.setSpacing(3)
        
        # 添加预定义选项的复选框
        self.option_checkboxes = []
        for option in self.predefined_options:
            checkbox = QCheckBox(option)
            checkbox.stateChanged.connect(self.on_option_changed)
            self.option_checkboxes.append(checkbox)
            self.dropdown_layout.addWidget(checkbox)
        
        # 添加分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        self.dropdown_layout.addWidget(separator)
        
        # 添加用户自定义选项
        self.custom_checkbox = QCheckBox(self.custom_option_text)
        self.custom_checkbox.stateChanged.connect(self.on_custom_option_changed)
        self.dropdown_layout.addWidget(self.custom_checkbox)
        
        # 添加用户输入框
        self.custom_input_widget = QLineEdit()
        self.custom_input_widget.setPlaceholderText("输入自定义dump_mem选项...")
        self.custom_input_widget.setEnabled(False)
        self.custom_input_widget.textChanged.connect(self.on_custom_input_changed)
        self.dropdown_layout.addWidget(self.custom_input_widget)
        
        # 添加确认按钮
        button_layout = QHBoxLayout()
        self.confirm_btn = QPushButton("确认")
        self.confirm_btn.clicked.connect(self.confirm_selection)
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.cancel_selection)
        button_layout.addWidget(self.confirm_btn)
        button_layout.addWidget(self.cancel_btn)
        self.dropdown_layout.addLayout(button_layout)
        
        # 更新显示文本
        self.update_display_text()
        
    def on_item_activated(self, index):
        """处理下拉框激活事件"""
        # 显示自定义选择对话框
        self.show_selection_dialog()
        
    def show_selection_dialog(self):
        """显示选择对话框"""
        # 如果对话框已存在且可见，则返回
        if self.selection_dialog and self.selection_dialog.isVisible():
            return
            
        # 如果对话框已存在但不可见，则删除重建
        if self.selection_dialog:
            self.selection_dialog.deleteLater()
            
        self.selection_dialog = QDialog(self)
        self.selection_dialog.setWindowTitle("选择Dump Memory选项")
        self.selection_dialog.setModal(True)
        self.selection_dialog.resize(400, 300)
        
        layout = QVBoxLayout(self.selection_dialog)
        
        # 添加说明标签
        info_label = QLabel("请选择需要的dump_mem选项（可多选）：")
        layout.addWidget(info_label)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # 添加预定义选项的复选框
        dialog_checkboxes = []
        for i, option in enumerate(self.predefined_options):
            checkbox = QCheckBox(option)
            checkbox.setChecked(option in self.selected_options)
            dialog_checkboxes.append(checkbox)
            scroll_layout.addWidget(checkbox)
        
        # 添加分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        scroll_layout.addWidget(separator)
        
        # 添加用户自定义选项
        custom_checkbox = QCheckBox(self.custom_option_text)
        custom_checkbox.setChecked(self.custom_selected)
        scroll_layout.addWidget(custom_checkbox)
        
        # 添加用户输入框
        custom_input = QLineEdit()
        custom_input.setPlaceholderText("输入自定义dump_mem选项...")
        custom_input.setText(self.custom_input)
        custom_input.setEnabled(self.custom_selected)
        scroll_layout.addWidget(custom_input)
        
        # 连接自定义选项的信号
        def on_custom_toggled(checked):
            custom_input.setEnabled(checked)
            if not checked:
                custom_input.clear()
        
        custom_checkbox.toggled.connect(on_custom_toggled)
        
        scroll_widget.setLayout(scroll_layout)
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
        
        # 添加按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.selection_dialog.accept)
        button_box.rejected.connect(self.selection_dialog.reject)
        layout.addWidget(button_box)
        
        # 显示对话框
        if self.selection_dialog.exec_() == QDialog.Accepted:
            # 更新选择状态
            self.selected_options = []
            for i, checkbox in enumerate(dialog_checkboxes):
                if checkbox.isChecked():
                    self.selected_options.append(self.predefined_options[i])
            
            self.custom_selected = custom_checkbox.isChecked()
            self.custom_input = custom_input.text().strip()
            
            # 更新显示文本
            self.update_display_text()
            
            # 发出信号
            self.emit_selection_changed()
        
        # 设置对话框为 None，以便于垃圾回收
        self.selection_dialog = None

    def on_option_changed(self):
        """处理预定义选项变化"""
        self.selected_options = []
        for i, checkbox in enumerate(self.option_checkboxes):
            if checkbox.isChecked():
                self.selected_options.append(self.predefined_options[i])
        
        self.update_display_text()
        self.emit_selection_changed()
    
    def on_custom_option_changed(self, checked):
        """处理用户自定义选项变化"""
        self.custom_selected = checked
        self.custom_input_widget.setEnabled(checked)
        
        if not checked:
            self.custom_input_widget.clear()
            self.custom_input = ""
        
        self.update_display_text()
        self.emit_selection_changed()
    
    def on_custom_input_changed(self, text):
        """处理用户输入变化"""
        self.custom_input = text.strip()
        self.emit_selection_changed()
    
    def confirm_selection(self):
        """确认选择"""
        self.update_display_text()
        self.emit_selection_changed()
    
    def cancel_selection(self):
        """取消选择"""
        pass
    
    def update_display_text(self):
        """更新显示文本"""
        display_parts = []
        
        # 添加选中的预定义选项
        if self.selected_options:
            display_parts.extend(self.selected_options)
        
        # 添加用户自定义选项
        if self.custom_selected and self.custom_input:
            display_parts.append(self.custom_input)
        
        if display_parts:
            display_text = f"已选择 {len(display_parts)} 项"
        else:
            display_text = "请选择dump_mem选项..."
        
        # 设置下拉框显示文本
        self.clear()
        self.addItem(display_text)
        self.setCurrentIndex(0)
    
    def emit_selection_changed(self):
        """发出选择变化信号"""
        all_selections = []
        
        # 添加选中的预定义选项
        all_selections.extend(self.selected_options)
        
        # 添加用户自定义选项
        if self.custom_selected and self.custom_input:
            all_selections.append(self.custom_input)
        
        self.selectionChanged.emit(all_selections)
    
    def get_selected_options(self):
        """获取选中的选项列表"""
        all_selections = []
        
        # 添加选中的预定义选项
        all_selections.extend(self.selected_options)
        
        # 添加用户自定义选项
        if self.custom_selected and self.custom_input:
            all_selections.append(self.custom_input)
        
        return all_selections
    
    def get_dump_mem_value(self):
        """获取dump_mem参数值"""
        options = self.get_selected_options()
        if options:
            return " ".join(options)
        return ""
    
    def set_selected_options(self, options):
        """设置选中的选项"""
        if not isinstance(options, list):
            options = []
        
        # 重置状态
        self.selected_options = []
        self.custom_selected = False
        self.custom_input = ""
        
        # 处理选项
        for option in options:
            if option in self.predefined_options:
                self.selected_options.append(option)
            else:
                # 用户自定义选项
                self.custom_selected = True
                self.custom_input = option
        
        # 更新显示
        self.update_display_text()
    
    def clear_selection(self):
        """清空选择"""
        self.selected_options = []
        self.custom_selected = False
        self.custom_input = ""
        self.update_display_text()
