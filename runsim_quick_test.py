#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RunSim 快速测试版本 - 大量日志输出，短时间完成
专门用于测试日志刷新和编码功能
"""
import sys
import os
import time
import argparse
import random
from datetime import datetime

# 设置Windows控制台编码
if sys.platform == 'win32':
    try:
        os.system('chcp 65001 >nul 2>&1')
        import io
        if hasattr(sys.stdout, 'buffer'):
            sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
        if hasattr(sys.stderr, 'buffer'):
            sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')
        sys.stdout.reconfigure(line_buffering=True)
        sys.stderr.reconfigure(line_buffering=True)
    except Exception as e:
        print(f"Warning: Failed to set UTF-8 encoding: {e}", file=sys.stderr)

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='RunSim 快速测试脚本')
    parser.add_argument('-case', type=str, help='测试案例名称')
    parser.add_argument('-cov', action='store_true', help='覆盖率收集')
    parser.add_argument('-fsdb', action='store_true', help='生成FSDB波形文件')
    parser.add_argument('-fast', action='store_true', help='快速模式')
    return parser.parse_args()

def quick_test_simulation(case_name, args):
    """快速测试仿真，大量日志输出"""
    print(f"🚀 开始快速测试仿真: {case_name}")
    print("=" * 50)
    
    # 快速初始化
    print("⚡ 快速初始化...")
    for i in range(5):
        print(f"  初始化步骤 {i+1}/5: 配置{['内核', '内存', '时钟', '信号', '监控'][i]}...")
        time.sleep(0.1)
    
    # 大量快速日志输出
    total_cycles = 100
    print(f"\n📊 开始 {total_cycles} 个周期的快速仿真...")
    print("-" * 40)
    
    for cycle in range(total_cycles):
        current_time = cycle * 10  # 每个周期10ns
        
        # 基础时钟信息
        print(f"@{current_time:4d}ns: 周期 {cycle:3d} | clk={'↑' if cycle%2==0 else '↓'}")
        
        # 模块活动 (每3个周期)
        if cycle % 3 == 0:
            modules = ['CPU核心', '内存控制器', '总线仲裁器', '中断控制器', '缓存单元', 'DMA控制器']
            module = random.choice(modules)
            actions = ['数据处理', '状态更新', '事务处理', '缓存刷新', '中断响应']
            action = random.choice(actions)
            print(f"@{current_time:4d}ns: {module} -> {action}")
        
        # 信号变化 (每2个周期)
        if cycle % 2 == 0:
            signals = [
                ('数据总线[31:0]', f'0x{random.randint(0, 0xFFFFFFFF):08X}'),
                ('地址总线[31:0]', f'0x{random.randint(0x1000, 0xFFFF):04X}'),
                ('控制信号', random.choice(['有效', '无效', '等待', '就绪'])),
                ('状态寄存器', f'0b{random.randint(0, 255):08b}')
            ]
            signal, value = random.choice(signals)
            print(f"@{current_time:4d}ns: {signal} = {value}")
        
        # 协议事务 (每5个周期)
        if cycle % 5 == 0:
            protocols = [
                'AXI4读突发事务开始',
                'AXI4写事务完成',
                'APB外设配置访问',
                'AHB主控器请求总线',
                'PCIe TLP包传输',
                'USB事务处理'
            ]
            protocol = random.choice(protocols)
            print(f"@{current_time:4d}ns: 协议 -> {protocol}")
        
        # 统计信息 (每10个周期)
        if cycle % 10 == 0:
            stats = [
                f'指令执行: {random.randint(100, 999)}条',
                f'内存访问: {random.randint(50, 200)}次',
                f'缓存命中率: {random.randint(85, 99)}%',
                f'总线利用率: {random.randint(60, 95)}%',
                f'功耗: {random.uniform(0.8, 2.5):.2f}W'
            ]
            stat = random.choice(stats)
            print(f"@{current_time:4d}ns: 统计 -> {stat}")
        
        # 事件和警告 (随机)
        if random.random() < 0.2:  # 20%概率
            events = [
                ('信息', ['流水线满载', '缓存预取命中', '分支预测正确', '指令对齐优化']),
                ('警告', ['时序裕量不足', '功耗接近上限', '温度升高', '总线争用']),
                ('调试', ['断点触发', '观察点命中', '跟踪缓冲区满', '性能计数器溢出'])
            ]
            event_type, messages = random.choice(events)
            message = random.choice(messages)
            print(f"@{current_time:4d}ns: {event_type} -> {message}")
        
        # 检查点 (每20个周期)
        if cycle % 20 == 0 and cycle > 0:
            progress = (cycle / total_cycles) * 100
            print(f"@{current_time:4d}ns: *** 检查点 {cycle//20} | 进度: {progress:.1f}% ***")
            
            # 详细状态信息
            print(f"@{current_time:4d}ns:   系统状态: 正常运行")
            print(f"@{current_time:4d}ns:   活跃模块: {random.randint(8, 15)}个")
            print(f"@{current_time:4d}ns:   待处理事务: {random.randint(5, 25)}个")
        
        # 控制输出速度
        if args.fast:
            time.sleep(0.05)  # 快速模式
        else:
            time.sleep(0.1)   # 正常模式
    
    # 仿真结束
    final_time = total_cycles * 10
    print(f"\n@{final_time}ns: 🏁 仿真执行完成")
    print("=" * 50)
    
    # 详细结果
    success = random.choice([True, True, True, False])
    
    print("📋 仿真结果报告:")
    print("-" * 30)
    print(f"状态: {'✅ 通过' if success else '❌ 失败'}")
    print(f"总周期数: {total_cycles}")
    print(f"仿真时间: {final_time}ns")
    print(f"错误数: {0 if success else random.randint(1, 3)}")
    print(f"警告数: {random.randint(2, 8)}")
    print(f"信息数: {random.randint(15, 30)}")
    
    if args.cov:
        print(f"\n📊 覆盖率报告:")
        print(f"  行覆盖率: {random.uniform(88, 97):.1f}%")
        print(f"  分支覆盖率: {random.uniform(82, 94):.1f}%")
        print(f"  状态覆盖率: {random.uniform(75, 89):.1f}%")
    
    if args.fsdb:
        print(f"\n📈 波形文件:")
        print(f"  FSDB文件: {case_name}_waves.fsdb")
        print(f"  文件大小: {random.randint(25, 80)}MB")
        print(f"  信号数量: {random.randint(500, 1500)}")
    
    return success

def main():
    """主函数"""
    print("⚡ RunSim 快速测试脚本 v1.0")
    print("专为日志刷新和编码测试设计")
    print("=" * 50)
    
    args = parse_arguments()
    case_name = args.case if args.case else "quick_test"
    
    print(f"测试案例: {case_name}")
    print(f"开始时间: {datetime.now().strftime('%H:%M:%S')}")
    print(f"快速模式: {'启用' if args.fast else '禁用'}")
    
    start_time = time.time()
    
    try:
        success = quick_test_simulation(case_name, args)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print("\n" + "=" * 50)
        print("🎯 测试完成")
        print(f"耗时: {duration:.1f}秒")
        print(f"结果: {'✅ 成功' if success else '❌ 失败'}")
        print(f"结束时间: {datetime.now().strftime('%H:%M:%S')}")
        print("=" * 50)
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断测试")
        return 130
    except Exception as e:
        print(f"\n❌ 测试出错: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
