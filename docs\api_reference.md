# RunSim GUI API 文档

本文档描述了 RunSim GUI 应用程序的主要 API，包括各个模块的公共接口和使用方法。

## 1. 控制器 API

### 1.1 AppController

`AppController` 是应用程序的主控制器，负责协调各个子控制器和视图。

#### 1.1.1 构造函数

```python
AppController()
```

创建应用程序控制器实例，初始化模型、视图和子控制器。

#### 1.1.2 公共方法

```python
show()
```

显示主窗口。

```python
save_config()
```

保存当前配置到配置文件。

```python
load_config()
```

从配置文件加载配置。

```python
load_history()
```

加载历史记录。

```python
clear_history()
```

清空历史记录。

### 1.2 CaseController

`CaseController` 负责管理用例相关操作。

#### 1.2.1 构造函数

```python
CaseController(main_window, config_model)
```

创建用例控制器实例。

- `main_window`: 主窗口实例
- `config_model`: 配置数据模型实例

#### 1.2.2 公共方法

```python
load_case_file(file_path)
```

加载用例文件。

- `file_path`: 用例文件路径

```python
remove_case_file(file_path)
```

移除用例文件。

- `file_path`: 用例文件路径

```python
on_config_loaded(config)
```

处理配置加载事件。

- `config`: 加载的配置

### 1.3 ConfigController

`ConfigController` 负责管理配置相关操作。

#### 1.3.1 构造函数

```python
ConfigController(main_window, config_model, history_model)
```

创建配置控制器实例。

- `main_window`: 主窗口实例
- `config_model`: 配置数据模型实例
- `history_model`: 历史记录模型实例

#### 1.3.2 公共方法

```python
set_execution_controller(execution_controller)
```

设置执行控制器引用。

- `execution_controller`: 执行控制器实例

```python
on_config_loaded(config)
```

处理配置加载事件。

- `config`: 加载的配置

```python
on_config_changed(config)
```

处理配置变更事件。

- `config`: 变更后的配置

```python
update_history_view(history)
```

更新历史记录视图。

- `history`: 历史记录列表

### 1.4 ExecutionController

`ExecutionController` 负责管理执行相关操作。

#### 1.4.1 构造函数

```python
ExecutionController(main_window, config_model, history_model)
```

创建执行控制器实例。

- `main_window`: 主窗口实例
- `config_model`: 配置数据模型实例
- `history_model`: 历史记录模型实例

#### 1.4.2 公共方法

```python
execute_command(command, case_name)
```

执行命令。

- `command`: 命令字符串
- `case_name`: 用例名称

```python
on_config_loaded(config)
```

处理配置加载事件。

- `config`: 加载的配置

## 2. 模型 API

### 2.1 ConfigModel

`ConfigModel` 负责管理应用程序配置。

#### 2.1.1 构造函数

```python
ConfigModel(config_file="runsim_config.json")
```

创建配置模型实例。

- `config_file`: 配置文件路径

#### 2.1.2 公共方法

```python
load_config()
```

从文件加载配置。

返回值：加载的配置字典

```python
save_config()
```

保存配置到文件。

返回值：保存是否成功

```python
update_config(new_config)
```

更新配置。

- `new_config`: 新的配置字典

返回值：更新后的配置字典

```python
get_config()
```

获取当前配置。

返回值：当前配置字典

```python
get_value(key, default=None)
```

获取配置项的值。

- `key`: 配置项的键
- `default`: 默认值

返回值：配置项的值

### 2.2 CaseModel

`CaseModel` 负责管理用例数据。

#### 2.2.1 构造函数

```python
CaseModel()
```

创建用例模型实例。

#### 2.2.2 公共方法

```python
add_case_file(file_path)
```

添加用例文件。

- `file_path`: 用例文件路径

返回值：添加是否成功

```python
remove_case_file(file_path)
```

移除用例文件。

- `file_path`: 用例文件路径

返回值：移除是否成功

```python
get_case_files()
```

获取所有用例文件。

返回值：用例文件列表

### 2.3 CommandModel

`CommandModel` 负责生成执行命令。

#### 2.3.1 构造函数

```python
CommandModel()
```

创建命令模型实例。

#### 2.3.2 公共方法

```python
generate_command(config)
```

根据配置生成命令。

- `config`: 配置字典

返回值：生成的命令字符串

### 2.4 HistoryModel

`HistoryModel` 负责管理历史记录。

#### 2.4.1 构造函数

```python
HistoryModel(history_file="runsim_command_history.json")
```

创建历史记录模型实例。

- `history_file`: 历史记录文件路径

#### 2.4.2 公共方法

```python
add_history(command)
```

添加历史记录。

- `command`: 命令字符串

返回值：添加是否成功

```python
get_history()
```

获取历史记录。

返回值：历史记录列表

```python
clear_history()
```

清空历史记录。

返回值：清空是否成功

```python
load_history()
```

从文件加载历史记录。

返回值：加载的历史记录列表

```python
save_history()
```

保存历史记录到文件。

返回值：保存是否成功

## 3. 视图 API

### 3.1 MainWindow

`MainWindow` 是应用程序的主窗口。

#### 3.1.1 构造函数

```python
MainWindow()
```

创建主窗口实例。

#### 3.1.2 公共方法

```python
set_left_panel(panel)
```

设置左侧面板。

- `panel`: 左侧面板实例

```python
set_right_panel(panel)
```

设置右侧面板。

- `panel`: 右侧面板实例

```python
show_message(message, duration=3000)
```

在状态栏显示消息。

- `message`: 消息内容
- `duration`: 显示时间（毫秒）

```python
show_error(title, message)
```

显示错误对话框。

- `title`: 对话框标题
- `message`: 错误消息

```python
show_warning(title, message)
```

显示警告对话框。

- `title`: 对话框标题
- `message`: 警告消息

```python
show_info(title, message)
```

显示信息对话框。

- `title`: 对话框标题
- `message`: 信息内容

```python
show_question(title, message)
```

显示问题对话框。

- `title`: 对话框标题
- `message`: 问题内容

返回值：用户选择的按钮

## 4. 工具类 API

### 4.1 EventBus

`EventBus` 是事件总线，用于模块间的通信。

#### 4.1.1 获取实例

```python
EventBus.instance()
```

获取事件总线单例实例。

#### 4.1.2 公共方法

```python
emit_case_selected(case_name)
```

发射用例选择信号。

- `case_name`: 用例名称

```python
emit_command_executed(command, case_name)
```

发射命令执行信号。

- `command`: 命令字符串
- `case_name`: 用例名称

```python
emit_config_changed(config)
```

发射配置变更信号。

- `config`: 配置字典

```python
emit_history_updated(history)
```

发射历史记录更新信号。

- `history`: 历史记录列表
