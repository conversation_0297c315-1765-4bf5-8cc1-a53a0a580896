# runsim_gui.py 架构重构设计文档

## 1. 当前代码结构分析

### 1.1 概述

当前的 `runsim_gui.py` 是一个基于 PyQt5 的 GUI 应用程序，用于控制和管理仿真运行。该程序已经实现了插件系统来扩展功能，但主程序本身仍然是一个大型的单体文件，包含了多个功能模块混合在一起。

### 1.2 主要组件

当前代码包含以下主要组件：

1. **ResourceMonitor 类**：系统资源监控
2. **CaseTab 类**：单个用例执行的标签页
3. **RunSimGUI 类**：主窗口类，包含所有 GUI 组件和业务逻辑

### 1.3 辅助模块

程序依赖以下辅助模块：

1. **AsyncLogger**：异步日志记录器
2. **CacheManager**：缓存管理器
3. **AsyncTaskManager**：异步任务管理器
4. **common_widgets**：通用 UI 组件
5. **plugins**：插件系统

### 1.4 问题分析

当前代码存在以下问题：

1. **职责混合**：RunSimGUI 类承担了太多职责，包括 UI 创建、事件处理、命令生成、文件解析等
2. **代码冗长**：单个文件超过 3500 行，难以维护和理解
3. **耦合度高**：UI 和业务逻辑紧密耦合，难以单独测试或修改
4. **重复代码**：存在多处相似的代码片段
5. **缺乏模块化**：虽然有插件系统，但主程序本身缺乏模块化设计

## 2. 拟划分的模块及其职责

### 2.1 核心模块

1. **app.py**：应用程序入口点，负责初始化和启动应用
2. **models/**：数据模型和业务逻辑
   - **config_model.py**：配置数据模型
   - **case_model.py**：用例数据模型
   - **command_model.py**：命令生成模型
   - **history_model.py**：历史记录模型
3. **views/**：UI 视图组件
   - **main_window.py**：主窗口框架
   - **case_panel.py**：用例管理面板
   - **config_panel.py**：配置面板
   - **execution_panel.py**：执行面板
   - **log_panel.py**：日志面板
4. **controllers/**：控制器，连接模型和视图
   - **app_controller.py**：应用程序控制器
   - **case_controller.py**：用例控制器
   - **config_controller.py**：配置控制器
   - **execution_controller.py**：执行控制器
5. **utils/**：工具类
   - **resource_monitor.py**：资源监控
   - **case_parser.py**：用例文件解析器
   - **command_generator.py**：命令生成器
   - **path_resolver.py**：路径解析器

### 2.2 现有辅助模块（保持不变）

1. **async_logger.py**
2. **cache_manager.py**
3. **async_task_manager.py**
4. **common_widgets.py**
5. **plugins/**：插件系统

## 3. 模块间的依赖关系

```
app.py
  ├── controllers/app_controller.py
  │     ├── controllers/case_controller.py
  │     │     ├── models/case_model.py
  │     │     └── views/case_panel.py
  │     ├── controllers/config_controller.py
  │     │     ├── models/config_model.py
  │     │     └── views/config_panel.py
  │     ├── controllers/execution_controller.py
  │     │     ├── models/command_model.py
  │     │     ├── models/history_model.py
  │     │     ├── views/execution_panel.py
  │     │     └── views/log_panel.py
  │     └── views/main_window.py
  ├── utils/resource_monitor.py
  ├── utils/case_parser.py
  ├── utils/command_generator.py
  ├── utils/path_resolver.py
  ├── async_logger.py
  ├── cache_manager.py
  ├── async_task_manager.py
  └── plugins/manager.py
```

## 4. 重构步骤和实施计划

### 4.1 阶段一：准备工作

1. 创建新的目录结构
2. 创建基本的模块文件
3. 定义模块间的接口

### 4.2 阶段二：提取工具类

1. 从 RunSimGUI 类中提取 ResourceMonitor 类到 utils/resource_monitor.py
2. 创建 utils/case_parser.py，提取用例解析相关功能
3. 创建 utils/command_generator.py，提取命令生成相关功能
4. 创建 utils/path_resolver.py，提取路径解析相关功能

### 4.3 阶段三：创建数据模型

1. 创建 models/config_model.py，实现配置数据模型
2. 创建 models/case_model.py，实现用例数据模型
3. 创建 models/command_model.py，实现命令生成模型
4. 创建 models/history_model.py，实现历史记录模型

### 4.4 阶段四：创建视图组件

1. 创建 views/main_window.py，实现主窗口框架
2. 创建 views/case_panel.py，实现用例管理面板
3. 创建 views/config_panel.py，实现配置面板
4. 创建 views/execution_panel.py，实现执行面板
5. 创建 views/log_panel.py，实现日志面板

### 4.5 阶段五：创建控制器

1. 创建 controllers/app_controller.py，实现应用程序控制器
2. 创建 controllers/case_controller.py，实现用例控制器
3. 创建 controllers/config_controller.py，实现配置控制器
4. 创建 controllers/execution_controller.py，实现执行控制器

### 4.6 阶段六：创建应用程序入口

1. 创建 app.py，实现应用程序入口点

### 4.7 阶段七：测试和优化

1. 测试各个模块的功能
2. 优化模块间的接口
3. 修复潜在的问题

## 5. 潜在风险及应对措施

### 5.1 功能丢失风险

**风险**：在重构过程中可能会丢失一些功能或引入新的错误。

**应对措施**：
- 采用增量式重构，每次只重构一小部分代码
- 为每个模块编写单元测试
- 在重构过程中保持原有的功能测试

### 5.2 插件兼容性风险

**风险**：重构后的代码可能与现有插件系统不兼容。

**应对措施**：
- 保持插件接口不变
- 在重构过程中测试现有插件的功能
- 如需修改插件接口，提供适配层

### 5.3 性能风险

**风险**：重构后的代码可能会引入性能问题。

**应对措施**：
- 在重构过程中进行性能测试
- 优化关键路径上的代码
- 保持异步处理机制

### 5.4 用户体验风险

**风险**：重构可能会改变用户界面或操作流程，影响用户体验。

**应对措施**：
- 保持用户界面的一致性
- 确保关键操作流程不变
- 在重构完成后进行用户体验测试

## 6. 重构后的优势

1. **代码结构清晰**：模块化设计使代码结构更加清晰，便于理解和维护
2. **职责分离**：每个模块只负责特定的功能，降低了耦合度
3. **可测试性提高**：模块化设计使得单元测试更加容易
4. **可扩展性增强**：清晰的接口定义使得添加新功能更加容易
5. **团队协作改善**：不同的开发人员可以同时在不同的模块上工作
6. **代码复用**：通用功能被提取到独立的模块中，可以在多处复用

## 7. 后续优化方向

1. **国际化支持**：添加多语言支持
2. **主题支持**：添加多主题支持
3. **配置文件格式优化**：使用更标准的配置文件格式
4. **插件系统增强**：提供更丰富的插件 API
5. **自动化测试**：建立完整的自动化测试系统
6. **文档完善**：编写详细的开发文档和用户手册
