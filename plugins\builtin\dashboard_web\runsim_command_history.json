[{"command": "runsim -base top -block udtb/top/top_clk -case top_clk_sel_test", "timestamp": "2025-05-23 10:58:16"}, {"command": "runsim -base top -block udtb/top/top_clk -case top_clk_sel_test -rundir aaa", "timestamp": "2025-05-29 16:59:52"}, {"command": "runsim -block top -case top_clk_bist_test", "timestamp": "2025-05-27 10:30:04"}, {"command": "runsim -block top -case top_clk_scan_test", "timestamp": "2025-05-27 10:30:02"}, {"command": "runsim -block top -case top_clk_sel_test", "timestamp": "2025-05-27 10:29:59"}, {"command": "runsim -block top -case top_bus_mini_test", "timestamp": "2025-05-27 10:29:57"}, {"command": "runsim -block top -case top_passive_test", "timestamp": "2025-05-27 10:29:54"}, {"command": "runsim -block top -case top_stress_test", "timestamp": "2025-05-27 10:29:52"}, {"command": "runsim -base top -block udtb/usvp -case top_clk_scan_test", "timestamp": "2025-05-27 10:00:19"}, {"command": "runsim -base top -block udtb/usvp -case top_clk_sel_test", "timestamp": "2025-05-27 10:00:17"}, {"command": "runsim -base top -block udtb/usvp -case top_bus_mini_test", "timestamp": "2025-05-27 10:00:14"}, {"command": "runsim -base top -block udtb/usvp -case top_passive_test", "timestamp": "2025-05-27 10:00:12"}, {"command": "runsim -base top -block udtb/usvp -case top_stress_test", "timestamp": "2025-05-27 10:00:10"}, {"command": "runsim -base top -block udtb/top/top_clk -case top_stress_test", "timestamp": "2025-05-27 09:46:53"}, {"command": "runsim -base top -block udtb/top/top_clk -case top_clk_bist_test", "timestamp": "2025-05-23 10:58:17"}, {"command": "runsim -base top -block udtb/top/top_clk -case top_clk_scan_test", "timestamp": "2025-05-23 10:58:17"}, {"command": "test_command", "timestamp": "2025-05-22 15:46:17"}]