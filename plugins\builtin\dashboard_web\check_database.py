#!/usr/bin/env python
"""
检查数据库状态
"""

import os
import sys
import sqlite3

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def check_database():
    """检查数据库状态"""
    print("🔍 检查数据库状态")
    print("=" * 30)

    # 获取动态数据库路径
    try:
        from config import get_database_path
        db_path = get_database_path()
    except ImportError:
        # 向后兼容：如果无法导入配置模块，使用默认路径
        db_path = os.path.join(current_dir, 'data', 'dashboard.db')
    
    print(f"📁 数据库路径: {db_path}")
    print(f"📁 文件存在: {os.path.exists(db_path)}")
    
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return False
    
    try:
        with sqlite3.connect(db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row['name'] for row in cursor.fetchall()]
            print(f"📋 数据库表: {tables}")
            
            # 检查用例数量
            if 'test_cases' in tables:
                cursor.execute("SELECT COUNT(*) as count FROM test_cases")
                case_count = cursor.fetchone()['count']
                print(f"📊 用例数量: {case_count}")
                
                if case_count > 0:
                    cursor.execute("SELECT case_name, subsys_status FROM test_cases LIMIT 3")
                    cases = cursor.fetchall()
                    print("📋 示例用例:")
                    for case in cases:
                        print(f"   {case['case_name']} - {case['subsys_status']}")
            else:
                print("❌ test_cases表不存在")
            
            # 检查BUG数量
            if 'bugs' in tables:
                cursor.execute("SELECT COUNT(*) as count FROM bugs")
                bug_count = cursor.fetchone()['count']
                print(f"🐛 BUG数量: {bug_count}")
            else:
                print("⚠️ bugs表不存在")
            
            return True
            
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")
        return False

if __name__ == '__main__':
    check_database()
