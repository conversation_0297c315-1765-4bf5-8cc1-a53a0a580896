"""
仿真状态监听器模块

该模块负责监听RunSim GUI的仿真执行事件，并自动更新仪表盘数据库中的用例状态。
主要功能包括：
1. 监听仿真开始和结束事件
2. 解析仿真命令参数
3. 更新仪表盘数据库中的用例状态和时间记录
4. 检测仿真结果并更新相应状态
"""

import os
import re
import time
import logging
import threading
from datetime import datetime
from typing import Optional, Dict, Any, Tuple
from PyQt5.QtCore import QObject, pyqtSignal, QTimer

# 配置日志
logger = logging.getLogger(__name__)


class CommandParser:
    """命令参数解析器"""
    
    @staticmethod
    def parse_command(command: str) -> Dict[str, str]:
        """
        解析runsim命令参数
        
        Args:
            command: runsim命令字符串
            
        Returns:
            Dict: 解析出的参数字典
        """
        params = {}
        
        # 解析-case参数
        case_match = re.search(r'-case\s+(\S+)', command)
        if case_match:
            params['case'] = case_match.group(1)
        
        # 解析-base参数
        base_match = re.search(r'-base\s+(\S+)', command)
        if base_match:
            params['base'] = base_match.group(1)
        
        # 解析-block参数
        block_match = re.search(r'-block\s+(\S+)', command)
        if block_match:
            params['block'] = block_match.group(1)
        
        # 解析-post参数
        post_match = re.search(r'-post\s+(\S+)', command)
        if post_match:
            params['post'] = post_match.group(1)
        
        # 解析-rundir参数
        rundir_match = re.search(r'-rundir\s+(\S+)', command)
        if rundir_match:
            params['rundir'] = rundir_match.group(1)
        
        return params
    
    @staticmethod
    def determine_case_type(params: Dict[str, str]) -> str:
        """
        根据命令参数确定用例类型
        
        Args:
            params: 命令参数字典
            
        Returns:
            str: 用例类型 ('subsys', 'top', 'post_subsys', 'post_top')
        """
        has_post = 'post' in params and params['post']
        base = params.get('base', '').lower()
        block = params.get('block', '').lower()
        
        is_top = base == 'top' or 'top' in block
        
        if has_post:
            return 'post_top' if is_top else 'post_subsys'
        else:
            return 'top' if is_top else 'subsys'


class LogMonitor:
    """日志文件监控器"""
    
    def __init__(self):
        self.monitoring_threads = {}
    
    def start_monitoring(self, case_name: str, rundir: str, callback):
        """
        开始监控指定用例的日志文件
        
        Args:
            case_name: 用例名称
            rundir: 运行目录
            callback: 监控结果回调函数
        """
        if case_name in self.monitoring_threads:
            # 如果已经在监控，先停止
            self.stop_monitoring(case_name)
        
        # 启动新的监控线程
        thread = threading.Thread(
            target=self._monitor_log_file,
            args=(case_name, rundir, callback),
            daemon=True
        )
        thread.start()
        self.monitoring_threads[case_name] = thread
        
        logger.info(f"开始监控用例日志: {case_name}")
    
    def stop_monitoring(self, case_name: str):
        """
        停止监控指定用例的日志文件
        
        Args:
            case_name: 用例名称
        """
        if case_name in self.monitoring_threads:
            # 线程会自然结束，这里只是清理引用
            del self.monitoring_threads[case_name]
            logger.info(f"停止监控用例日志: {case_name}")
    
    def _monitor_log_file(self, case_name: str, rundir: str, callback):
        """
        监控日志文件的内部方法
        
        Args:
            case_name: 用例名称
            rundir: 运行目录
            callback: 结果回调函数
        """
        log_file_path = os.path.join(rundir, 'irun_sim.log')
        
        # 等待日志文件出现
        max_wait_time = 300  # 最多等待5分钟
        wait_time = 0
        
        while wait_time < max_wait_time:
            if os.path.exists(log_file_path):
                break
            time.sleep(5)
            wait_time += 5
        
        if not os.path.exists(log_file_path):
            logger.warning(f"日志文件未找到: {log_file_path}")
            callback(case_name, False, "日志文件未找到")
            return
        
        # 监控日志文件变化
        last_size = 0
        check_interval = 10  # 每10秒检查一次
        max_monitor_time = 3600  # 最多监控1小时
        monitor_time = 0
        
        while monitor_time < max_monitor_time:
            try:
                current_size = os.path.getsize(log_file_path)
                
                # 如果文件大小没有变化，可能仿真已结束
                if current_size > last_size:
                    last_size = current_size
                    
                    # 检查仿真是否完成
                    result = self._check_simulation_result(log_file_path)
                    if result is not None:
                        callback(case_name, result, "仿真完成")
                        break
                
                time.sleep(check_interval)
                monitor_time += check_interval
                
            except Exception as e:
                logger.error(f"监控日志文件时出错: {e}")
                break
        
        # 超时或出错时的处理
        if monitor_time >= max_monitor_time:
            logger.warning(f"监控超时: {case_name}")
            callback(case_name, False, "监控超时")
    
    def _check_simulation_result(self, log_file_path: str) -> Optional[bool]:
        """
        检查仿真结果
        
        Args:
            log_file_path: 日志文件路径
            
        Returns:
            Optional[bool]: True表示通过，False表示失败，None表示未完成
        """
        try:
            # 读取日志文件的最后50行
            with open(log_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                last_lines = lines[-50:] if len(lines) >= 50 else lines
                
                # 检查是否包含SPRD_PASSED
                for line in last_lines:
                    if 'SPRD_PASSED' in line:
                        return True
                
                # 检查是否有仿真结束的标志
                for line in last_lines:
                    if any(keyword in line.lower() for keyword in ['simulation complete', 'exit', 'finish']):
                        # 仿真已结束但没有SPRD_PASSED，认为失败
                        return False
                
                # 仿真尚未结束
                return None
                
        except Exception as e:
            logger.error(f"检查仿真结果时出错: {e}")
            return None


class SimulationMonitor(QObject):
    """仿真状态监听器主类"""
    
    # 信号定义
    simulation_started = pyqtSignal(str, str, dict)  # case_name, command, params
    simulation_finished = pyqtSignal(str, bool, str)  # case_name, success, message
    
    def __init__(self):
        super().__init__()
        self.log_monitor = LogMonitor()
        self.dashboard_updater = None
        
        # 连接信号到处理函数
        self.simulation_started.connect(self._on_simulation_started)
        self.simulation_finished.connect(self._on_simulation_finished)
    
    def set_dashboard_updater(self, updater):
        """设置仪表盘更新器"""
        self.dashboard_updater = updater
    
    def on_execution_started(self, case_name: str, command: str):
        """
        处理仿真开始事件
        
        Args:
            case_name: 用例名称
            command: 执行命令
        """
        try:
            # 解析命令参数
            params = CommandParser.parse_command(command)
            
            # 发出仿真开始信号
            self.simulation_started.emit(case_name, command, params)
            
            # 开始监控日志文件
            rundir = params.get('rundir', f'work/{case_name}')
            if not os.path.isabs(rundir):
                # 如果是相对路径，使用PROJ_DIR环境变量
                proj_dir = os.environ.get('PROJ_DIR', os.getcwd())
                rundir = os.path.join(proj_dir, rundir)
            
            self.log_monitor.start_monitoring(
                case_name, rundir, self._on_log_monitor_result
            )
            
        except Exception as e:
            logger.error(f"处理仿真开始事件时出错: {e}")
    
    def on_execution_finished(self, case_name: str, exit_code: int):
        """
        处理仿真结束事件
        
        Args:
            case_name: 用例名称
            exit_code: 退出码
        """
        try:
            # 停止日志监控
            self.log_monitor.stop_monitoring(case_name)
            
            # 根据退出码判断结果
            success = exit_code == 0
            message = "执行成功" if success else f"执行失败 (退出码: {exit_code})"
            
            # 发出仿真结束信号
            self.simulation_finished.emit(case_name, success, message)
            
        except Exception as e:
            logger.error(f"处理仿真结束事件时出错: {e}")
    
    def _on_simulation_started(self, case_name: str, command: str, params: dict):
        """处理仿真开始信号"""
        if self.dashboard_updater:
            self.dashboard_updater.update_case_start(case_name, command, params)
    
    def _on_simulation_finished(self, case_name: str, success: bool, message: str):
        """处理仿真结束信号"""
        if self.dashboard_updater:
            self.dashboard_updater.update_case_finish(case_name, success, message)
    
    def _on_log_monitor_result(self, case_name: str, success: bool, message: str):
        """处理日志监控结果"""
        self.simulation_finished.emit(case_name, success, message)
