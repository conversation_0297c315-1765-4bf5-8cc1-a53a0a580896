"""
配置数据模型
"""
import os
import json
from PyQt5.QtCore import QObject, pyqtSignal

class ConfigModel(QObject):
    """配置数据模型，负责管理应用程序配置"""

    # 定义信号
    config_changed = pyqtSignal(dict)
    config_loaded = pyqtSignal(dict)
    config_saved = pyqtSignal(dict)

    def __init__(self, config_file="runsim_config.json"):
        """
        初始化配置模型

        Args:
            config_file (str): 配置文件路径
        """
        super().__init__()
        self.config_file = config_file
        self.config = {}
        self.default_config = {
            "window": {
                "maximized": False,
                "x": 100,
                "y": 100,
                "width": 1400,
                "height": 900
            },
            "base": "",
            "block": "",
            "case_files": [],
            "rundir": "",
            "other_options": "",
            "fsdb": False,
            "vwdb": False,
            "cl": False,
            "fsdb_file": "",
            "dump_sva": False,
            "cov": False,
            "upf": False,
            "sim_only": False,
            "compile_only": False,
            "dump_mem": "",
            "wdd": "",
            "seed": "",
            "simarg": "",
            "cfg_def": "",
            "post": "",
            "regr_file": "",
            "fm_checked": False,
            "bp_server": "",
            "last_command": ""
        }

    def load_config(self):
        """
        从文件加载配置

        Returns:
            dict: 加载的配置
        """
        if not os.path.exists(self.config_file):
            self.config = self.default_config.copy()
            return self.config

        try:
            with open(self.config_file, 'r') as f:
                self.config = json.load(f)

            # 确保所有默认配置项都存在
            for key, value in self.default_config.items():
                if key not in self.config:
                    self.config[key] = value

            self.config_loaded.emit(self.config)
            return self.config
        except Exception as e:
            print(f"加载配置失败: {str(e)}")
            self.config = self.default_config.copy()
            return self.config

    def save_config(self):
        """
        保存配置到文件

        Returns:
            bool: 保存是否成功
        """
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=4)
            self.config_saved.emit(self.config)
            return True
        except Exception as e:
            print(f"保存配置失败: {str(e)}")
            return False

    def update_config(self, new_config):
        """
        更新配置

        Args:
            new_config (dict): 新的配置

        Returns:
            dict: 更新后的配置
        """
        # 更新配置
        self.config.update(new_config)

        # 发射配置变更信号
        # 注意：在使用事件总线的情况下，这里可能会导致无限递归
        # 因此，在控制器中应该直接更新 self.config，而不是调用此方法
        # 此方法保留是为了向后兼容
        try:
            self.config_changed.emit(self.config)
        except RecursionError:
            print("警告：检测到递归调用 update_config，跳过发射信号")

        return self.config

    def get_config(self):
        """
        获取当前配置

        Returns:
            dict: 当前配置
        """
        return self.config

    def get_value(self, key, default=None):
        """
        获取配置项的值

        Args:
            key (str): 配置项的键
            default: 默认值

        Returns:
            配置项的值
        """
        return self.config.get(key, default)
