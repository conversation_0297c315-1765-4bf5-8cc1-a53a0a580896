# 获取种子号功能更新说明

## 概述

RunSim GUI配置面板中的"获取种子号"按钮功能已进行重要更新，现在支持更灵活的种子号获取方式。

## 功能更新

### 原有行为（保持不变）
- 当有选中的case tab时，解析该tab对应用例的 `irun_sim.log` 文件
- 如果该文件不存在，则不进行任何解析操作

### 新增行为
- 当没有选中任何case tab时，自动搜索 `$PROJ_DIR/work` 目录
- 根据配置面板中的用例名称智能匹配相关目录
- 自动选择最新的仿真日志文件进行解析

## 详细功能说明

### 1. 智能模式切换

**有选中case tab时：**
```
用户操作：点击"获取种子号"按钮
系统行为：
1. 检测到当前有选中的case tab
2. 使用原有逻辑解析该tab的日志文件
3. 从 {case_name}/log/irun_sim.log 中提取种子号
```

**无选中case tab时：**
```
用户操作：点击"获取种子号"按钮
系统行为：
1. 检测到当前无选中的case tab
2. 读取配置面板中的用例名称
3. 搜索 $PROJ_DIR/work 目录
4. 匹配包含用例名称的目录
5. 选择最新的日志文件进行解析
```

### 2. 环境变量处理

**PROJ_DIR环境变量：**
- **已设置**：使用 `$PROJ_DIR/work` 作为搜索目录
- **未设置**：使用当前工作目录作为项目根目录，搜索 `./work`

**示例：**
```bash
# 设置了PROJ_DIR环境变量
export PROJ_DIR=/home/<USER>/my_project
# 搜索目录：/home/<USER>/my_project/work

# 未设置PROJ_DIR环境变量
# 搜索目录：{当前工作目录}/work
```

### 3. 智能目录匹配

系统会在work目录中搜索包含当前用例名称的所有子目录：

**匹配规则：**
- 不区分大小写的子字符串匹配
- 支持部分匹配（例如：用例名"test_case"可以匹配"test_case_001"目录）

**示例：**
```
配置面板用例名称：test_uart
work目录结构：
├── test_uart_basic/
├── test_uart_advanced/
├── test_spi_basic/
└── other_test/

匹配结果：test_uart_basic, test_uart_advanced
```

### 4. 日志文件优先级

系统会按以下优先级搜索仿真日志文件：

1. `{case_dir}/log/irun_sim.log`
2. `{case_dir}/log/vcs_sim.log`
3. `{case_dir}/log/sim.log`

### 5. 多文件处理策略

当找到多个匹配的日志文件时：

**选择策略：**
- 按文件修改时间排序
- 选择最新的日志文件
- 向用户显示找到的文件数量和选择的文件

**用户提示：**
```
找到 3 个匹配的日志文件，使用最新的：test_uart_basic_20240101
```

## 使用场景

### 场景1：有选中case tab
```
1. 用户在执行面板中选中了某个用例的tab
2. 点击配置面板的"获取种子号"按钮
3. 系统解析该tab对应的日志文件
4. 提取种子号并填入配置面板
```

### 场景2：无选中case tab，但配置了用例名称
```
1. 用户在配置面板中输入了用例名称（如：test_uart）
2. 没有选中任何case tab
3. 点击"获取种子号"按钮
4. 系统搜索$PROJ_DIR/work目录
5. 找到匹配的用例目录（如：test_uart_basic）
6. 解析最新的日志文件并提取种子号
```

### 场景3：无选中case tab，也无用例名称
```
1. 用户没有选中case tab
2. 配置面板中也没有输入用例名称
3. 点击"获取种子号"按钮
4. 系统提示："请先在配置面板中设置用例名称，或选择一个用例标签页"
```

## 错误处理

### 常见错误及解决方案

**1. PROJ_DIR环境变量未设置**
```
提示：PROJ_DIR环境变量未设置，使用当前目录：/current/path
解决：设置PROJ_DIR环境变量或确保当前目录下有work子目录
```

**2. work目录不存在**
```
错误：工作目录不存在：/path/to/work
解决：检查PROJ_DIR设置或创建work目录
```

**3. 未找到匹配的用例**
```
错误：在目录 /path/to/work 中未找到用例 'test_case' 的仿真日志文件
解决：检查用例名称拼写或确认用例目录存在
```

**4. 日志文件中无种子号**
```
错误：在用例 test_case 的仿真日志中未找到种子号
解决：确认日志文件包含完整的仿真命令信息
```

## 技术实现

### 新增方法

1. **`_get_seed_from_work_directory()`**
   - 从work目录搜索种子号
   - 处理环境变量和目录匹配

2. **`_parse_seed_from_log(log_file, source_info)`**
   - 统一的日志解析方法
   - 支持不同来源的日志文件

3. **`_get_seed_from_case_tab(case_name)`**
   - 从选中case tab获取种子号
   - 重构原有逻辑为独立方法

### 正则表达式

种子号提取使用的正则表达式：
```python
r'-seed\s+(\d+)'
```

支持的格式：
- `-seed 12345`
- `-seed    67890`（多个空格）
- `runsim -base top -seed 99999 -case test`

## 兼容性

- ✅ 完全向后兼容原有功能
- ✅ 不影响现有用户工作流程
- ✅ 新功能仅在无选中tab时激活
- ✅ 支持Windows和Linux环境

## 更新历史

- **2024年**: 新增work目录搜索功能
- **之前版本**: 仅支持从选中case tab获取种子号
