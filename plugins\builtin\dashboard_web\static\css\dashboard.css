/* RunSim Dashboard 自定义样式 */

/* 全局样式 */
:root {
    --primary-color: #007bff;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --secondary-color: #6c757d;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    
    --transition: all 0.3s ease;
}

/* 页面布局 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f6fa;
    color: #2c3e50;
    line-height: 1.6;
}

.main-container {
    min-height: 100vh;
    padding: 20px 0;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    margin-bottom: 1.5rem;
}

.card:hover {
    box-shadow: var(--box-shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* 指标卡片 */
.metric-card {
    border-left: 4px solid var(--primary-color);
    transition: var(--transition);
}

.metric-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--box-shadow-lg);
}

.metric-card.bg-primary {
    border-left-color: var(--primary-color);
}

.metric-card.bg-success {
    border-left-color: var(--success-color);
}

.metric-card.bg-danger {
    border-left-color: var(--danger-color);
}

.metric-card.bg-warning {
    border-left-color: var(--warning-color);
}

.metric-card.bg-info {
    border-left-color: var(--info-color);
}

.metric-label {
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

.metric-value {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0;
    line-height: 1;
}

/* 图表容器 */
.chart-card {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.chart-container {
    position: relative;
    height: 300px;
    padding: 20px;
}

.chart-container canvas {
    max-height: 100%;
}

/* 状态徽章 */
.status-badge {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.375rem 0.75rem;
    border-radius: 50px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-pass {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-pending {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.status-ongoing {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-fail {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-na {
    background-color: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
}

/* 进度条 */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
    overflow: hidden;
}

.progress-bar {
    transition: width 0.6s ease;
}

.progress-mini {
    height: 4px;
    margin-top: 0.5rem;
}

/* 表格样式 */
.table {
    background-color: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background-color: var(--light-color);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
    padding: 1rem 0.75rem;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.table td {
    padding: 0.75rem;
    vertical-align: middle;
}

/* 按钮样式 */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    border: none;
    padding: 0.5rem 1rem;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: var(--border-radius);
    border-bottom-left-radius: var(--border-radius);
}

.btn-group .btn:last-child {
    border-top-right-radius: var(--border-radius);
    border-bottom-right-radius: var(--border-radius);
}

/* 表单样式 */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-select {
    border-radius: var(--border-radius);
}

/* 模态框样式 */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--box-shadow-lg);
}

.modal-header {
    background-color: var(--light-color);
    border-bottom: 1px solid #dee2e6;
}

.modal-footer {
    background-color: var(--light-color);
    border-top: 1px solid #dee2e6;
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 连接状态指示器 */
.connection-status {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-online {
    background-color: var(--success-color);
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.3);
}

.status-offline {
    background-color: var(--danger-color);
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.3);
}

/* 刷新指示器 */
.refresh-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: var(--primary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
    z-index: 1050;
    display: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .metric-value {
        font-size: 2rem;
    }
    
    .chart-container {
        height: 250px;
        padding: 15px;
    }
    
    .table-responsive {
        border-radius: var(--border-radius);
    }
    
    .btn-group-vertical .btn {
        margin-bottom: 0.25rem;
    }
}

@media (max-width: 576px) {
    .metric-value {
        font-size: 1.75rem;
    }
    
    .chart-container {
        height: 200px;
        padding: 10px;
    }
    
    .card {
        margin-bottom: 1rem;
    }
}

/* 打印样式 */
@media print {
    .btn, .navbar, .modal, .refresh-indicator {
        display: none !important;
    }
    
    .card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    .chart-container {
        height: auto;
    }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #343a40;
        --dark-color: #f8f9fa;
    }
    
    body {
        background-color: #1a1a1a;
        color: #f8f9fa;
    }
    
    .card {
        background-color: #2d3748;
        color: #f8f9fa;
    }
    
    .table {
        background-color: #2d3748;
        color: #f8f9fa;
    }
    
    .table thead th {
        background-color: #4a5568;
    }
    
    .table tbody tr:hover {
        background-color: #4a5568;
    }
}
