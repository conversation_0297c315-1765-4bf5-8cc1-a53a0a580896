#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建 RunSim GUI 发布包
这个脚本将创建 RunSim GUI 的发布包，包括应用程序文件、文档和示例
"""
import os
import sys
import shutil
import datetime
import argparse
import zipfile

def create_directory(directory):
    """创建目录"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"创建目录: {directory}")
    return directory

def copy_files(source_files, target_dir):
    """复制文件"""
    for source_file in source_files:
        if os.path.exists(source_file):
            shutil.copy2(source_file, target_dir)
            print(f"复制文件: {source_file} -> {target_dir}")
        else:
            print(f"警告: 文件不存在: {source_file}")

def copy_directories(source_dirs, target_dir):
    """复制目录"""
    for source_dir in source_dirs:
        if os.path.exists(source_dir):
            target_path = os.path.join(target_dir, os.path.basename(source_dir))
            if os.path.exists(target_path):
                shutil.rmtree(target_path)
            shutil.copytree(source_dir, target_path)
            print(f"复制目录: {source_dir} -> {target_path}")
        else:
            print(f"警告: 目录不存在: {source_dir}")

def create_zip_archive(source_dir, zip_file):
    """创建 ZIP 归档"""
    with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(source_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arcname = os.path.relpath(file_path, os.path.dirname(source_dir))
                zipf.write(file_path, arcname)
    print(f"创建 ZIP 归档: {zip_file}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="创建 RunSim GUI 发布包")
    parser.add_argument("--version", default="2.0.0", help="版本号")
    parser.add_argument("--output", default="dist", help="输出目录")
    args = parser.parse_args()

    # 版本号和时间戳
    version = args.version
    timestamp = datetime.datetime.now().strftime("%Y%m%d")
    
    # 创建输出目录
    output_dir = create_directory(args.output)
    release_dir = create_directory(os.path.join(output_dir, f"runsim_gui_{version}"))
    
    # 复制应用程序文件
    app_files = [
        "runsim_gui.py",
        "run_app.py",
        "plugin_config.json",
        "发布说明.md",
        "README.md"
    ]
    copy_files(app_files, release_dir)
    
    # 复制应用程序目录
    app_dirs = [
        "controllers",
        "models",
        "views",
        "utils",
        "plugins",
        "docs"
    ]
    copy_directories(app_dirs, release_dir)
    
    # 创建示例目录
    examples_dir = create_directory(os.path.join(release_dir, "examples"))
    
    # 复制示例文件
    example_files = [
        "test_app.py",
        "test_integration.py"
    ]
    copy_files(example_files, examples_dir)
    
    # 创建 ZIP 归档
    zip_file = os.path.join(output_dir, f"runsim_gui_{version}_{timestamp}.zip")
    create_zip_archive(release_dir, zip_file)
    
    print(f"发布包创建完成: {zip_file}")
    return 0

if __name__ == "__main__":
    sys.exit(main())
