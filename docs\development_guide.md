# RunSim GUI 开发指南

本文档提供了 RunSim GUI 应用程序的开发指南，包括如何设置开发环境、如何扩展和修改应用程序，以及开发过程中的最佳实践。

## 1. 开发环境设置

### 1.1 环境要求

- Python 3.8 或更高版本
- PyQt5 5.15 或更高版本
- 其他依赖项（见 requirements.txt）

### 1.2 安装依赖

```bash
pip install -r requirements.txt
```

### 1.3 开发工具推荐

- Visual Studio Code 或 PyCharm 作为 IDE
- Git 用于版本控制
- PyQt Designer 用于 UI 设计（可选）

## 2. 项目结构

```
runsim/
├── app.py                  # 应用程序入口点（原始版本）
├── run_app.py              # 启动重构后的应用程序
├── test_app.py             # 测试重构后的应用程序
├── models/                 # 数据模型
│   ├── __init__.py
│   ├── case_model.py       # 用例数据模型
│   ├── command_model.py    # 命令生成模型
│   ├── config_model.py     # 配置数据模型
│   └── history_model.py    # 历史记录模型
├── views/                  # 视图组件
│   ├── __init__.py
│   ├── main_window.py      # 主窗口框架
│   ├── case_panel.py       # 用例管理面板
│   ├── config_panel.py     # 配置面板
│   ├── execution_panel.py  # 执行面板
│   └── log_panel.py        # 日志面板
├── controllers/            # 控制器
│   ├── __init__.py
│   ├── app_controller.py   # 应用程序控制器
│   ├── case_controller.py  # 用例控制器
│   ├── config_controller.py # 配置控制器
│   └── execution_controller.py # 执行控制器
├── utils/                  # 工具类
│   ├── __init__.py
│   ├── case_parser.py      # 用例解析器
│   ├── command_generator.py # 命令生成器
│   ├── path_resolver.py    # 路径解析器
│   ├── resource_monitor.py # 资源监控器
│   └── event_bus.py        # 事件总线
├── plugins/                # 插件系统
│   ├── __init__.py
│   ├── manager.py          # 插件管理器
│   ├── base.py             # 插件基类
│   ├── builtin/            # 内置插件
│   └── user/               # 用户插件
└── docs/                   # 文档
    ├── 架构说明.md
    ├── API文档.md
    ├── 开发指南.md
    └── 用户手册.md
```

## 3. 开发流程

### 3.1 添加新功能

1. 确定新功能的需求和设计
2. 确定新功能应该添加到哪个模块
3. 实现新功能的模型部分（如果需要）
4. 实现新功能的视图部分（如果需要）
5. 实现新功能的控制器部分（如果需要）
6. 编写单元测试
7. 进行集成测试
8. 更新文档

### 3.2 修改现有功能

1. 确定需要修改的功能和修改的目的
2. 找到相关的模块和代码
3. 进行修改
4. 更新单元测试
5. 进行集成测试
6. 更新文档

### 3.3 修复 Bug

1. 确定 Bug 的具体表现和复现步骤
2. 找到相关的模块和代码
3. 修复 Bug
4. 添加单元测试以防止 Bug 再次出现
5. 进行集成测试
6. 更新文档（如果需要）

## 4. 扩展应用程序

### 4.1 添加新的模型

1. 在 `models/` 目录下创建新的模型文件
2. 实现模型类，继承 `QObject` 并定义必要的信号
3. 实现模型的业务逻辑
4. 在相关的控制器中使用新模型

示例：

```python
from PyQt5.QtCore import QObject, pyqtSignal

class NewModel(QObject):
    """新模型类"""
    
    # 定义信号
    data_changed = pyqtSignal(dict)
    
    def __init__(self):
        """初始化新模型"""
        super().__init__()
        self.data = {}
        
    def update_data(self, new_data):
        """更新数据"""
        self.data.update(new_data)
        self.data_changed.emit(self.data)
        
    def get_data(self):
        """获取数据"""
        return self.data
```

### 4.2 添加新的视图

1. 在 `views/` 目录下创建新的视图文件
2. 实现视图类，继承 `QWidget` 或其他 Qt 控件
3. 实现视图的 UI 和交互逻辑
4. 在相关的控制器中使用新视图

示例：

```python
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QPushButton, QLabel
from PyQt5.QtCore import pyqtSignal

class NewView(QWidget):
    """新视图类"""
    
    # 定义信号
    button_clicked = pyqtSignal()
    
    def __init__(self, parent=None):
        """初始化新视图"""
        super().__init__(parent)
        self.init_ui()
        
    def init_ui(self):
        """初始化 UI"""
        layout = QVBoxLayout(self)
        
        self.label = QLabel("这是一个新视图")
        layout.addWidget(self.label)
        
        self.button = QPushButton("点击我")
        self.button.clicked.connect(self.button_clicked.emit)
        layout.addWidget(self.button)
        
    def set_text(self, text):
        """设置标签文本"""
        self.label.setText(text)
```

### 4.3 添加新的控制器

1. 在 `controllers/` 目录下创建新的控制器文件
2. 实现控制器类，继承 `QObject`
3. 实现控制器的业务逻辑
4. 在应用程序控制器中使用新控制器

示例：

```python
from PyQt5.QtCore import QObject, pyqtSlot

class NewController(QObject):
    """新控制器类"""
    
    def __init__(self, main_window, model):
        """初始化新控制器"""
        super().__init__()
        self.main_window = main_window
        self.model = model
        self.view = NewView()
        
        # 连接信号
        self.view.button_clicked.connect(self.on_button_clicked)
        self.model.data_changed.connect(self.on_data_changed)
        
    @pyqtSlot()
    def on_button_clicked(self):
        """处理按钮点击事件"""
        # 处理逻辑
        pass
        
    @pyqtSlot(dict)
    def on_data_changed(self, data):
        """处理数据变更事件"""
        # 更新视图
        self.view.set_text(str(data))
```

### 4.4 添加新的工具类

1. 在 `utils/` 目录下创建新的工具类文件
2. 实现工具类的功能
3. 在需要的地方导入和使用新工具类

示例：

```python
class NewUtil:
    """新工具类"""
    
    @staticmethod
    def process_data(data):
        """处理数据"""
        # 处理逻辑
        return processed_data
```

### 4.5 添加新的插件

1. 在 `plugins/user/` 目录下创建新的插件文件
2. 实现插件类，继承 `plugins.base.Plugin`
3. 实现插件的功能
4. 确保插件在 `plugin_config.json` 中注册

示例：

```python
from plugins.base import Plugin
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QPushButton, QLabel

class NewPlugin(Plugin):
    """新插件类"""
    
    def __init__(self, parent=None):
        """初始化新插件"""
        super().__init__(parent)
        self.name = "新插件"
        self.description = "这是一个新插件"
        
    def create_widget(self):
        """创建插件窗口"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        label = QLabel("这是一个新插件")
        layout.addWidget(label)
        
        button = QPushButton("点击我")
        button.clicked.connect(self.on_button_clicked)
        layout.addWidget(button)
        
        return widget
        
    def on_button_clicked(self):
        """处理按钮点击事件"""
        # 处理逻辑
        pass
```

## 5. 最佳实践

### 5.1 代码风格

- 遵循 PEP 8 代码风格指南
- 使用有意义的变量名和函数名
- 添加适当的注释和文档字符串
- 保持代码简洁和可读

### 5.2 错误处理

- 使用 try-except 块捕获和处理异常
- 提供有意义的错误消息
- 记录错误和异常
- 优雅地处理错误，不要让应用程序崩溃

### 5.3 测试

- 为每个模块编写单元测试
- 进行集成测试，确保各个模块能够协同工作
- 进行性能测试，确保应用程序能够高效运行
- 进行用户界面测试，确保用户体验良好

### 5.4 文档

- 为每个模块、类和方法添加文档字符串
- 更新 API 文档，确保文档与代码保持一致
- 更新用户手册，确保用户能够正确使用应用程序
- 记录设计决策和实现细节
