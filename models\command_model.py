"""
命令生成模型
"""
from PyQt5.QtCore import QObject, pyqtSignal
from utils.command_generator import CommandGenerator

class CommandModel(QObject):
    """命令生成模型，负责生成和管理命令"""

    # 定义信号
    command_generated = pyqtSignal(str)
    command_executed = pyqtSignal(str)

    def __init__(self, config_model=None):
        """
        初始化命令模型

        Args:
            config_model (ConfigModel, optional): 配置模型
        """
        super().__init__()
        self.config_model = config_model
        self.current_command = ""
        self.has_terminal = False

    def set_has_terminal(self, has_terminal):
        """
        设置是否有终端集成支持

        Args:
            has_terminal (bool): 是否有终端集成支持
        """
        self.has_terminal = has_terminal

    def generate_command(self, mode="normal", case_name=None):
        """
        生成命令

        Args:
            mode (str): 执行模式，例如 "normal", "R", "C" 等
            case_name (str, optional): 指定的用例名称，默认为 None

        Returns:
            str: 生成的命令
        """
        # 防止无限递归
        if hasattr(self, '_generating_command') and self._generating_command:
            return self.current_command

        self._generating_command = True
        try:
            if self.config_model:
                config = self.config_model.get_config()
            else:
                config = {}

            self.current_command = CommandGenerator.generate_command(
                config, mode, case_name, self.has_terminal
            )

            # 发出信号，但不阻止信号传递
            # 这样可以确保命令预览能够正确更新
            self.command_generated.emit(self.current_command)

            return self.current_command
        finally:
            self._generating_command = False

    def get_current_command(self):
        """
        获取当前命令

        Returns:
            str: 当前命令
        """
        return self.current_command

    def mark_command_executed(self):
        """
        标记命令已执行

        Returns:
            str: 执行的命令
        """
        self.command_executed.emit(self.current_command)
        return self.current_command
