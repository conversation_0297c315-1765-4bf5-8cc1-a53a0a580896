{% extends "base.html" %}

{% block title %}用例管理 - RunSim 项目仪表板{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0">用例管理</h1>
            <p class="text-muted mb-0">管理测试用例和TestPlan文件</p>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list-check me-2"></i>用例管理功能
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-success" role="alert">
                        <h4 class="alert-heading">✅ 用例管理页面加载成功！</h4>
                        <p>恭喜！testplan页面现在可以正常访问了。</p>
                        <hr>
                        <p class="mb-0">这是一个简化版本的用例管理页面，用于验证路由是否正常工作。</p>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">📊 统计信息</h6>
                                    <p class="card-text">
                                        <span class="badge bg-primary">总用例数: <span id="total-cases">0</span></span>
                                        <span class="badge bg-success">已通过: <span id="passed-cases">0</span></span>
                                        <span class="badge bg-danger">已失败: <span id="failed-cases">0</span></span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">🔧 快速操作</h6>
                                    <button class="btn btn-outline-primary btn-sm me-2" onclick="testAPI()">
                                        <i class="fas fa-test me-1"></i>测试API
                                    </button>
                                    <button class="btn btn-outline-success btn-sm me-2" onclick="loadFullVersion()">
                                        <i class="fas fa-upgrade me-1"></i>加载完整版
                                    </button>
                                    <button class="btn btn-outline-info btn-sm" onclick="checkHealth()">
                                        <i class="fas fa-heartbeat me-1"></i>健康检查
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h6>🔍 系统信息</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                当前时间
                                <span class="badge bg-secondary" id="current-time">{{ current_time.strftime('%Y-%m-%d %H:%M:%S') }}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                应用版本
                                <span class="badge bg-secondary">{{ version }}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                页面状态
                                <span class="badge bg-success">正常运行</span>
                            </li>
                        </ul>
                    </div>

                    <div class="mt-4">
                        <h6>📝 测试日志</h6>
                        <div class="border rounded p-3" style="height: 200px; overflow-y: auto; background-color: #f8f9fa;">
                            <div id="test-log">
                                <div class="text-muted">等待测试操作...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    addLog('页面加载完成');
    loadBasicStats();
    
    // 每5秒更新时间
    setInterval(updateTime, 5000);
});

function addLog(message) {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = `<div class="small text-muted">[${timestamp}] ${message}</div>`;
    $('#test-log').append(logEntry);
    
    // 滚动到底部
    const logContainer = $('#test-log').parent();
    logContainer.scrollTop(logContainer[0].scrollHeight);
}

function updateTime() {
    const now = new Date();
    $('#current-time').text(now.toLocaleString('zh-CN'));
}

function loadBasicStats() {
    addLog('开始加载统计数据...');
    
    $.get('/api/testplan/statistics')
        .done(function(response) {
            if (response.success) {
                const stats = response.data;
                $('#total-cases').text(stats.total_cases || 0);
                
                const passCount = (stats.subsys_pass || 0) + (stats.top_pass || 0) + 
                                 (stats.post_subsys_pass || 0) + (stats.post_top_pass || 0);
                const failCount = (stats.subsys_fail || 0) + (stats.top_fail || 0) + 
                                 (stats.post_subsys_fail || 0) + (stats.post_top_fail || 0);
                
                $('#passed-cases').text(passCount);
                $('#failed-cases').text(failCount);
                
                addLog(`✅ 统计数据加载成功: 总计${stats.total_cases}个用例`);
            } else {
                addLog('❌ 统计数据加载失败: ' + response.error);
            }
        })
        .fail(function(xhr, status, error) {
            addLog(`❌ API请求失败: ${status} - ${error}`);
        });
}

function testAPI() {
    addLog('开始测试API接口...');
    
    // 测试健康检查
    $.get('/health')
        .done(function(data) {
            addLog('✅ 健康检查通过: ' + data.status);
        })
        .fail(function() {
            addLog('❌ 健康检查失败');
        });
    
    // 测试用例列表API
    $.get('/api/testplan/cases?page=1&page_size=5')
        .done(function(response) {
            if (response.success) {
                addLog(`✅ 用例列表API正常: 找到${response.data.pagination.total_count}个用例`);
            } else {
                addLog('❌ 用例列表API失败: ' + response.error);
            }
        })
        .fail(function(xhr, status, error) {
            addLog(`❌ 用例列表API请求失败: ${status}`);
        });
}

function checkHealth() {
    addLog('执行健康检查...');
    
    $.get('/health')
        .done(function(data) {
            addLog('✅ 系统健康状态: ' + JSON.stringify(data));
        })
        .fail(function(xhr, status, error) {
            addLog(`❌ 健康检查失败: ${status} - ${error}`);
        });
}

function loadFullVersion() {
    addLog('准备加载完整版用例管理页面...');
    
    if (confirm('确定要加载完整版的用例管理页面吗？这将替换当前的简化版本。')) {
        addLog('用户确认加载完整版');
        // 这里可以添加加载完整版的逻辑
        alert('完整版功能开发中，请稍后再试。');
    } else {
        addLog('用户取消加载完整版');
    }
}

function showError(message) {
    addLog('❌ 错误: ' + message);
}

function showSuccess(message) {
    addLog('✅ 成功: ' + message);
}
</script>
{% endblock %}
