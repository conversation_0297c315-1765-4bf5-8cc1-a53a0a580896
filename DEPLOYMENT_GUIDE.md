# RunSim GUI仪表盘自动状态更新功能部署指南

## 部署概述

本指南将帮助您在现有的RunSim GUI系统中部署和启用仪表盘自动状态更新功能。

## 前置条件

### 系统要求
- Python 3.8+
- PyQt5
- SQLite3
- 现有的RunSim GUI系统

### 依赖库
```bash
pip install requests flask flask-cors
```

## 部署步骤

### 1. 文件部署

确保以下新增文件已正确放置：

```
runsim/
├── utils/
│   ├── simulation_monitor.py          # 仿真状态监听器
│   └── dashboard_updater.py           # 仪表盘状态更新器
├── controllers/
│   └── execution_controller.py        # 已修改：集成仿真监听
├── plugins/builtin/dashboard_web/
│   ├── routes/
│   │   ├── api.py                     # 已修改：新增通过率API
│   │   └── testplan.py                # 已修改：新增状态更新API
│   ├── templates/
│   │   └── dashboard.html             # 已修改：新增图表组件
│   └── static/js/
│       └── dashboard.js               # 已修改：新增图表逻辑
└── docs/
    ├── DASHBOARD_AUTO_UPDATE_GUIDE.md # 使用指南
    ├── FEATURE_VERIFICATION_CHECKLIST.md # 验证清单
    └── DEPLOYMENT_GUIDE.md           # 本部署指南
```

### 2. 数据库准备

#### 2.1 检查数据库结构

确保仪表盘数据库包含以下字段：

```sql
-- test_cases表必需字段
ALTER TABLE test_cases ADD COLUMN start_time TEXT;           -- I列：开始时间
ALTER TABLE test_cases ADD COLUMN end_time TEXT;             -- J列：结束时间
ALTER TABLE test_cases ADD COLUMN subsys_status TEXT;        -- N列：Subsys状态
ALTER TABLE test_cases ADD COLUMN top_status TEXT;           -- P列：TOP状态
ALTER TABLE test_cases ADD COLUMN post_subsys_status TEXT;   -- R列：后仿Subsys状态
ALTER TABLE test_cases ADD COLUMN post_top_status TEXT;      -- T列：后仿TOP状态
ALTER TABLE test_cases ADD COLUMN updated_at TEXT;           -- 更新时间
```

#### 2.2 数据库位置配置

默认数据库查找顺序：
1. 当前工作目录：`./dashboard.db`
2. 插件目录：`./plugins/builtin/dashboard_web/data/dashboard.db`

### 3. 环境变量配置

设置必要的环境变量：

```bash
# Windows
set PROJ_DIR=E:\doc\python\runsim

# Linux/Mac
export PROJ_DIR=/path/to/runsim
```

### 4. 服务启动

#### 4.1 启动仪表盘服务

```bash
# 方法1：通过RunSim GUI启动
# 在GUI中点击仪表盘按钮

# 方法2：独立启动
cd plugins/builtin/dashboard_web
python app.py
```

#### 4.2 验证服务状态

```bash
# 检查服务是否运行
curl http://127.0.0.1:5001/api/health

# 预期响应
{"status": "ok", "timestamp": "2024-12-20T10:30:00"}
```

### 5. 功能启用

#### 5.1 RunSim GUI配置

在RunSim GUI中，自动状态更新功能会在以下情况下启用：

1. 仪表盘服务正在运行
2. 执行控制器已正确初始化仿真监听器
3. 数据库连接正常

#### 5.2 验证功能启用

1. **检查日志输出**
   ```
   INFO - 仿真监听器初始化成功
   INFO - 仪表盘更新器连接正常
   ```

2. **测试状态更新**
   - 执行一个简单的仿真用例
   - 检查仪表盘中的状态变化
   - 验证时间记录是否正确

## 配置选项

### 1. 监控参数调整

在`utils/simulation_monitor.py`中可以调整：

```python
# 日志文件检查间隔（秒）
check_interval = 10

# 最大监控时间（秒）
max_monitor_time = 3600

# 日志文件等待时间（秒）
max_wait_time = 300
```

### 2. API端口配置

在`utils/dashboard_updater.py`中可以修改：

```python
# 仪表盘服务端口
dashboard_port = 5001
```

### 3. 数据库配置

可以通过环境变量指定数据库路径：

```bash
export DASHBOARD_DB_PATH=/custom/path/dashboard.db
```

## 故障排除

### 常见问题及解决方案

#### 1. 仿真监听器初始化失败

**症状：** 执行仿真时状态不更新

**解决方案：**
```python
# 检查导入是否成功
try:
    from utils.simulation_monitor import SimulationMonitor
    print("仿真监听器导入成功")
except ImportError as e:
    print(f"导入失败: {e}")
```

#### 2. 仪表盘API连接失败

**症状：** 状态更新API返回错误

**解决方案：**
1. 检查仪表盘服务是否运行
2. 验证端口是否被占用
3. 检查防火墙设置

```bash
# 检查端口占用
netstat -an | grep 5001

# 测试API连接
curl -v http://127.0.0.1:5001/api/health
```

#### 3. 数据库权限问题

**症状：** 数据库更新失败

**解决方案：**
```bash
# 检查数据库文件权限
ls -la dashboard.db

# 修改权限（Linux/Mac）
chmod 666 dashboard.db
```

#### 4. 日志文件监控不工作

**症状：** 仿真完成后状态不自动更新

**解决方案：**
1. 检查日志文件路径是否正确
2. 验证文件读取权限
3. 确认"SPRD_PASSED"字符串格式

```python
# 手动测试日志检测
log_file = "path/to/irun_sim.log"
with open(log_file, 'r') as f:
    lines = f.readlines()
    last_lines = lines[-50:]
    for line in last_lines:
        if 'SPRD_PASSED' in line:
            print("找到成功标志")
```

### 调试模式

启用详细日志输出：

```python
import logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
```

## 性能优化

### 1. 数据库优化

```sql
-- 创建索引提高查询性能
CREATE INDEX idx_case_name ON test_cases(case_name);
CREATE INDEX idx_updated_at ON test_cases(updated_at);
```

### 2. 监控优化

- 调整日志检查间隔以平衡实时性和性能
- 限制同时监控的用例数量
- 使用异步处理避免阻塞主线程

### 3. 前端优化

- 启用图表数据缓存
- 使用防抖动机制减少频繁更新
- 按需加载大量数据

## 安全考虑

### 1. API安全

- 考虑添加API认证机制
- 限制API访问来源
- 验证输入数据格式

### 2. 数据库安全

- 定期备份数据库
- 限制数据库文件访问权限
- 使用事务确保数据一致性

## 维护指南

### 1. 日常维护

- 定期检查日志文件大小
- 清理过期的监控线程
- 监控系统资源使用情况

### 2. 数据清理

```sql
-- 清理超过30天的历史记录
DELETE FROM test_cases 
WHERE updated_at < datetime('now', '-30 days');
```

### 3. 版本升级

1. 备份现有数据库
2. 更新代码文件
3. 运行数据库迁移脚本
4. 重启服务并验证功能

## 联系支持

如果在部署过程中遇到问题：

1. 查看详细错误日志
2. 参考故障排除指南
3. 联系开发团队获取支持

---

**部署完成后，请使用`FEATURE_VERIFICATION_CHECKLIST.md`进行功能验证。**
