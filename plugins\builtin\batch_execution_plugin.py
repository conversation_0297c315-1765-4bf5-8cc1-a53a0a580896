"""
批量执行插件
"""
from PyQt5.QtWidgets import (
    QAction, QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QTableWidget, QTableWidgetItem, QHeaderView,
    QCheckBox, QMessageBox, QFileDialog, QComboBox, QSpinBox,
    QGroupBox, QLineEdit, QSplitter, QTabWidget, QTextEdit,
    QWidget, QApplication
)
from PyQt5.QtCore import Qt, QProcess, QProcessEnvironment, pyqtSignal, QObject, QTimer
from PyQt5.QtGui import QColor, QIcon, QFont, QTextCursor
from plugins.base import PluginBase, NonModalDialog
import os
import re
import json
import time
import datetime
from utils.command_generator import CommandGenerator
from utils.path_resolver import PathResolver

class BatchExecutionProcess(QObject):
    """批量执行进程管理器"""

    # 定义信号
    execution_started = pyqtSignal(int, str)  # 行索引, 命令
    execution_finished = pyqtSignal(int, int)  # 行索引, 退出码
    execution_output = pyqtSignal(int, str)  # 行索引, 输出
    all_finished = pyqtSignal()  # 所有命令执行完成

    def __init__(self, parent=None):
        """初始化批量执行进程管理器"""
        super().__init__(parent)
        self.processes = {}  # 存储进程 {行索引: QProcess}
        self.commands = []  # 存储命令 [(行索引, 命令, 用例名)]
        self.current_index = 0  # 当前执行的命令索引
        self.max_parallel = 1  # 最大并行执行数
        self.active_count = 0  # 当前活动进程数

    def add_command(self, row_index, command, case_name):
        """添加命令到队列"""
        self.commands.append((row_index, command, case_name))

    def set_max_parallel(self, count):
        """设置最大并行执行数"""
        self.max_parallel = max(1, count)

    def start_execution(self):
        """开始执行命令队列"""
        self.current_index = 0
        self.active_count = 0
        self._execute_next_batch()

    def _execute_next_batch(self):
        """执行下一批命令"""
        # 计算可以启动的进程数
        available_slots = self.max_parallel - self.active_count

        if available_slots <= 0 or self.current_index >= len(self.commands):
            # 如果没有可用槽位或已经执行完所有命令，检查是否全部完成
            if self.active_count == 0:
                self.all_finished.emit()
            return

        # 启动新的进程，直到填满可用槽位或执行完所有命令
        for _ in range(available_slots):
            if self.current_index < len(self.commands):
                row_index, command, case_name = self.commands[self.current_index]
                self._start_process(row_index, command)
                self.current_index += 1
                self.active_count += 1
            else:
                break

    def _start_process(self, row_index, command):
        """启动单个进程"""
        process = QProcess(self)

        # 设置进程环境变量，确保子进程可以显示自己的通知窗口
        env = QProcessEnvironment.systemEnvironment()
        # 添加特殊环境变量，允许子进程创建自己的窗口
        env.insert("QT_PROCESS_SEPARATE_UI", "1")
        # 设置环境变量
        process.setProcessEnvironment(env)

        # 设置工作目录
        process.setWorkingDirectory(os.getcwd())

        # 连接信号
        process.readyReadStandardOutput.connect(
            lambda: self._handle_output(row_index, process)
        )
        process.readyReadStandardError.connect(
            lambda: self._handle_error(row_index, process)
        )
        process.finished.connect(
            lambda exit_code, _: self._handle_finished(row_index, exit_code)
        )

        # 存储进程
        self.processes[row_index] = process

        # 发送开始信号
        self.execution_started.emit(row_index, command)

        # 记录启动信息
        self.execution_output.emit(row_index, f"开始执行命令: {command}\n")

        # 启动进程（使用shell模式）
        if os.name == 'nt':  # Windows
            process.setProcessChannelMode(QProcess.MergedChannels)
            process.start("cmd.exe", ["/c", command])
        else:  # Linux/Mac
            process.setProcessChannelMode(QProcess.MergedChannels)
            # 使用setsid命令创建新会话，确保子进程可以显示自己的通知
            process.start("/bin/sh", ["-c", f"setsid {command}"])

    def _handle_output(self, row_index, process):
        """处理标准输出"""
        try:
            # 尝试多种编码方式解码输出
            raw_data = process.readAllStandardOutput().data()
            output = self._decode_output(raw_data)
            self.execution_output.emit(row_index, output)
        except Exception as e:
            print(f"处理标准输出时出错: {str(e)}")
            # 如果出错，使用安全的替换模式
            self.execution_output.emit(row_index, raw_data.decode('utf-8', errors='replace'))

    def _handle_error(self, row_index, process):
        """处理标准错误"""
        try:
            # 尝试多种编码方式解码错误输出
            raw_data = process.readAllStandardError().data()
            error = self._decode_output(raw_data)
            self.execution_output.emit(row_index, error)
        except Exception as e:
            print(f"处理标准错误时出错: {str(e)}")
            # 如果出错，使用安全的替换模式
            self.execution_output.emit(row_index, raw_data.decode('utf-8', errors='replace'))

    def _decode_output(self, data):
        """尝试使用多种编码解码输出数据"""
        # 常用编码列表，按优先级排序
        encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'big5', 'latin1']

        # 首先尝试检测编码
        try:
            import chardet
            detected = chardet.detect(data)
            if detected and detected['confidence'] > 0.7:
                # 如果检测到的编码可信度高，优先使用
                encodings.insert(0, detected['encoding'])
        except ImportError:
            # 如果没有安装chardet，忽略
            pass

        # 尝试不同的编码
        for encoding in encodings:
            try:
                return data.decode(encoding)
            except UnicodeDecodeError:
                continue

        # 如果所有编码都失败，使用替换模式
        return data.decode('utf-8', errors='replace')

    def _handle_finished(self, row_index, exit_code):
        """处理进程结束"""
        try:
            # 添加执行完成的日志信息
            if exit_code == 0:
                self.execution_output.emit(row_index, f"\n命令执行成功，退出码: {exit_code}\n")
            else:
                self.execution_output.emit(row_index, f"\n命令执行失败，退出码: {exit_code}\n")

            # 发送结束信号
            self.execution_finished.emit(row_index, exit_code)

            # 从进程字典中移除
            if row_index in self.processes:
                del self.processes[row_index]

            # 减少活动进程计数
            self.active_count -= 1

            # 尝试执行下一批命令
            self._execute_next_batch()
        except Exception as e:
            print(f"处理进程结束时出错: {str(e)}")

    def stop_all(self):
        """停止所有进程"""
        import sys
        import subprocess
        import time

        for row_index, process in self.processes.items():
            if process.state() != QProcess.NotRunning:
                try:
                    # 获取进程ID
                    pid = process.processId()
                    if pid > 0:
                        if sys.platform == 'win32':
                            # Windows系统使用taskkill /T命令终止进程树
                            kill_cmd = f"taskkill /F /T /PID {pid}"
                            subprocess.Popen(kill_cmd, shell=True)
                            # 记录终止信息
                            self.execution_output.emit(row_index, "\n[正在终止进程树...]\n")
                        else:
                            # Linux系统使用pkill -P命令终止子进程
                            # 首先终止所有子进程
                            kill_cmd = f"pkill -9 -P {pid}"
                            subprocess.Popen(kill_cmd, shell=True)
                            # 记录终止信息
                            self.execution_output.emit(row_index, "\n[正在终止子进程...]\n")
                            # 等待一小段时间确保命令执行
                            time.sleep(0.2)

                            # 然后尝试使用进程组ID终止整个进程组
                            try:
                                # 获取进程组ID并终止整个组
                                pgid_cmd = f"ps -o pgid= {pid} | tr -d ' '"
                                pgid = subprocess.check_output(pgid_cmd, shell=True, text=True).strip()
                                if pgid and pgid.isdigit():
                                    kill_group_cmd = f"kill -9 -{pgid}"
                                    subprocess.Popen(kill_group_cmd, shell=True)
                                    self.execution_output.emit(row_index, "\n[正在终止进程组...]\n")
                            except Exception as pgid_error:
                                print(f"获取或终止进程组时出错: {str(pgid_error)}")

                    # 最后使用QProcess的方法终止进程
                    process.terminate()
                    # 给进程一点时间来终止
                    time.sleep(0.1)
                    # 如果进程仍在运行，强制终止
                    if process.state() == QProcess.Running:
                        process.kill()

                except Exception as e:
                    self.execution_output.emit(row_index, f"\n[终止进程时出错: {str(e)}]\n")
                    print(f"终止进程时出错: {str(e)}")

        # 等待一段时间后强制结束
        QTimer.singleShot(3000, self._kill_remaining)

    def _kill_remaining(self):
        """强制结束剩余进程"""
        import sys
        import subprocess
        import time

        for row_index, process in list(self.processes.items()):
            if process.state() != QProcess.NotRunning:
                try:
                    # 获取进程ID
                    pid = process.processId()
                    if pid > 0:
                        if sys.platform == 'win32':
                            # Windows系统使用taskkill /F /T命令强制终止进程树
                            kill_cmd = f"taskkill /F /T /PID {pid}"
                            subprocess.Popen(kill_cmd, shell=True)
                            # 记录终止信息
                            self.execution_output.emit(row_index, "\n[强制终止进程树...]\n")
                        else:
                            # Linux系统使用pkill -9 -P命令强制终止子进程
                            kill_cmd = f"pkill -9 -P {pid}"
                            subprocess.Popen(kill_cmd, shell=True)
                            # 记录终止信息
                            self.execution_output.emit(row_index, "\n[强制终止子进程...]\n")
                            # 等待一小段时间确保命令执行
                            time.sleep(0.2)

                            # 然后尝试使用进程组ID终止整个进程组
                            try:
                                # 获取进程组ID并终止整个组
                                pgid_cmd = f"ps -o pgid= {pid} | tr -d ' '"
                                pgid = subprocess.check_output(pgid_cmd, shell=True, text=True).strip()
                                if pgid and pgid.isdigit():
                                    kill_group_cmd = f"kill -9 -{pgid}"
                                    subprocess.Popen(kill_group_cmd, shell=True)
                                    self.execution_output.emit(row_index, "\n[强制终止进程组...]\n")
                            except Exception as pgid_error:
                                print(f"获取或终止进程组时出错: {str(pgid_error)}")

                    # 最后使用QProcess的kill方法强制终止进程
                    process.kill()

                except Exception as e:
                    self.execution_output.emit(row_index, f"\n[强制终止进程时出错: {str(e)}]\n")
                    print(f"强制终止进程时出错: {str(e)}")

class BatchExecutionDialog(NonModalDialog):
    """批量执行对话框"""

    def __init__(self, parent=None, plugin_name=""):
        """初始化批量执行对话框"""
        try:
            super().__init__(parent, plugin_name)
            self.setWindowTitle("批量执行")
            self.resize(1200, 800)

            # 创建命令生成器和路径解析器
            self.command_generator = CommandGenerator()
            self.path_resolver = PathResolver()

            # 创建进程管理器
            self.process_manager = BatchExecutionProcess(self)

            # 连接信号
            self.process_manager.execution_started.connect(self.on_execution_started)
            self.process_manager.execution_finished.connect(self.on_execution_finished)
            self.process_manager.execution_output.connect(self.on_execution_output)
            self.process_manager.all_finished.connect(self.on_all_finished)

            # 初始化UI
            self.init_ui()
        except Exception as e:
            print(f"初始化批量执行对话框时出错: {str(e)}")
            # 重新抛出异常，让上层处理
            raise

    def init_ui(self):
        """初始化UI"""
        try:
            layout = QVBoxLayout()

            # 创建分割器
            splitter = QSplitter(Qt.Vertical)

            # 创建上部分（用例表格）
            top_widget = QWidget()
            top_layout = QVBoxLayout(top_widget)

            # 创建用例表格
            self.case_table = QTableWidget()
            self.case_table.setColumnCount(6)
            self.case_table.setHorizontalHeaderLabels([
                "选择", "用例名", "状态", "命令", "开始时间", "结束时间"
            ])
            self.case_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
            self.case_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
            self.case_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
            self.case_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.Stretch)
            self.case_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)
            self.case_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeToContents)

            # 添加到上部分布局
            top_layout.addWidget(self.case_table)

            # 创建下部分（日志和控制）
            bottom_widget = QWidget()
            bottom_layout = QVBoxLayout(bottom_widget)

            # 创建标签页控件
            self.tab_widget = QTabWidget()

            # 创建日志标签页
            self.log_tabs = {}

            # 添加到下部分布局
            bottom_layout.addWidget(self.tab_widget)

            # 添加控制按钮
            control_layout = QHBoxLayout()

            # 添加用例按钮
            add_button = QPushButton("添加用例")
            add_button.clicked.connect(self.add_cases)

            # 清除按钮
            clear_button = QPushButton("清除所有")
            clear_button.clicked.connect(self.clear_all)

            # 并行执行设置
            parallel_label = QLabel("并行执行数:")
            self.parallel_spin = QSpinBox()
            self.parallel_spin.setRange(1, 10)
            self.parallel_spin.setValue(1)
            self.parallel_spin.valueChanged.connect(self.set_parallel_count)

            # 执行按钮
            self.execute_button = QPushButton("开始执行")
            self.execute_button.clicked.connect(self.start_execution)

            # 停止按钮
            self.stop_button = QPushButton("停止执行")
            self.stop_button.clicked.connect(self.stop_execution)
            self.stop_button.setEnabled(False)

            # 添加到控制布局
            control_layout.addWidget(add_button)
            control_layout.addWidget(clear_button)
            control_layout.addStretch()
            control_layout.addWidget(parallel_label)
            control_layout.addWidget(self.parallel_spin)
            control_layout.addWidget(self.execute_button)
            control_layout.addWidget(self.stop_button)

            # 添加到下部分布局
            bottom_layout.addLayout(control_layout)

            # 添加到分割器
            splitter.addWidget(top_widget)
            splitter.addWidget(bottom_widget)
            splitter.setSizes([400, 400])

            # 添加到主布局
            layout.addWidget(splitter)
            self.setLayout(layout)
        except Exception as e:
            print(f"初始化批量执行对话框UI时出错: {str(e)}")
            # 重新抛出异常，让上层处理
            raise

    def add_cases(self):
        """添加用例"""
        # 获取用例列表
        case_files = self._get_case_files()
        if not case_files:
            return

        # 解析用例文件
        cases = self._parse_case_files(case_files)
        if not cases:
            QMessageBox.warning(self, "警告", "未找到有效的用例")
            return

        # 添加到表格
        self._add_cases_to_table(cases)

    def _get_case_files(self):
        """获取用例文件"""
        # 打开文件对话框
        paths, _ = QFileDialog.getOpenFileNames(
            self,
            "选择用例文件",
            "",
            "用例文件 (*.txt *.cfg);;所有文件 (*.*)"
        )
        return paths

    def _parse_case_files(self, case_files):
        """解析用例文件"""
        cases = []

        for file_path in case_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 使用正则表达式查找用例
                pattern = r'\[case\s+([\w_]+)(?:\s*:\s*([\w_]+))?.*'
                matches = re.findall(pattern, content)

                if matches:
                    # 提取文件名作为前缀
                    file_name = os.path.basename(file_path)

                    # 解析base和block参数
                    base, block = self._parse_base_block_from_path(file_path)

                    # 添加用例
                    for case, parent in matches:
                        cases.append({
                            'name': case,
                            'file': file_name,
                            'path': file_path,
                            'base': base,
                            'block': block
                        })
            except Exception as e:
                print(f"解析用例文件 {file_path} 失败: {str(e)}")

        return cases

    def _parse_base_block_from_path(self, file_path):
        """从文件路径解析base和block参数"""
        # 默认值
        base = ""
        block = ""

        try:
            # 规则1: dv/{subsys}/bin/case_cfg/xxx_case.cfg
            if match := re.search(r'dv/([^/]+)/bin/case_cfg/', file_path):
                subsys = match.group(1)
                base = ""
                block = subsys
                return base, block

            # 规则2: dv/udtb/{subsys}/子环境名/bin/xxx.cfg
            if match := re.search(r'dv/udtb/([^/]+)/([^/]+)/bin/', file_path):
                subsys = match.group(1)
                subenv = match.group(2)
                base = subsys
                block = f"udtb/{subsys}/{subenv}"
                return base, block

            # 规则3: dv/udtb/usvp/bin/case_cfg/<sys>_subsys_case.cfg
            if match := re.search(r'dv/udtb/usvp/bin/case_cfg/([^_]+)_subsys_case\.cfg', file_path):
                sys_name = match.group(1)
                base = f"{sys_name}_sys"
                block = "udtb/usvp"
                return base, block

            # 规则4: dv/udtb/usvp/bin/case_cfg/<sys>_top_case.cfg 或 xxx.cfg
            if re.search(r'dv/udtb/usvp/bin/case_cfg/.*_top_case\.cfg', file_path) or \
               re.search(r'dv/udtb/usvp/bin/case_cfg/.*\.cfg', file_path):
                base = "top"
                block = "udtb/usvp"
                return base, block
        except Exception as e:
            print(f"解析base/block参数失败: {str(e)}")

        return base, block

    def _add_cases_to_table(self, cases):
        """添加用例到表格"""
        # 获取当前行数
        row_count = self.case_table.rowCount()

        # 添加新行
        for case in cases:
            # 检查是否已存在
            exists = False
            for row in range(row_count):
                if self.case_table.item(row, 1).text() == case['name']:
                    exists = True
                    break

            if exists:
                continue

            # 添加新行
            self.case_table.insertRow(row_count)

            # 添加复选框
            checkbox = QCheckBox()
            checkbox.setChecked(True)
            self.case_table.setCellWidget(row_count, 0, checkbox)

            # 添加用例名
            self.case_table.setItem(row_count, 1, QTableWidgetItem(case['name']))

            # 添加状态
            status_item = QTableWidgetItem("等待执行")
            status_item.setForeground(QColor(128, 128, 128))
            self.case_table.setItem(row_count, 2, status_item)

            # 生成命令
            command = self._generate_command(case)
            self.case_table.setItem(row_count, 3, QTableWidgetItem(command))

            # 添加开始时间和结束时间
            self.case_table.setItem(row_count, 4, QTableWidgetItem(""))
            self.case_table.setItem(row_count, 5, QTableWidgetItem(""))

            # 存储用例数据
            self.case_table.item(row_count, 1).setData(Qt.UserRole, case)

            # 增加行数
            row_count += 1

    def _generate_command(self, case):
        """生成命令"""
        # 基本命令
        command = f"runsim -case {case['name']}"

        # 添加base和block参数
        if case['base']:
            command += f" -base {case['base']}"
        if case['block']:
            command += f" -block {case['block']}"

        return command

    def clear_all(self):
        """清除所有用例"""
        # 确认对话框
        reply = QMessageBox.question(
            self,
            "确认清除",
            "确定要清除所有用例吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # 清除表格
        self.case_table.setRowCount(0)

        # 清除日志标签页
        for i in range(self.tab_widget.count() - 1, -1, -1):
            self.tab_widget.removeTab(i)
        self.log_tabs = {}

    def set_parallel_count(self, count):
        """设置并行执行数"""
        self.process_manager.set_max_parallel(count)

    def start_execution(self):
        """开始执行"""
        # 检查是否有选中的用例
        selected_rows = []
        for row in range(self.case_table.rowCount()):
            checkbox = self.case_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                selected_rows.append(row)

        if not selected_rows:
            QMessageBox.warning(self, "警告", "请选择要执行的用例")
            return

        # 确认对话框
        reply = QMessageBox.question(
            self,
            "确认执行",
            f"确定要执行选中的 {len(selected_rows)} 个用例吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # 清除进程管理器中的命令
        self.process_manager.commands = []

        # 添加命令到进程管理器
        for row in selected_rows:
            # 获取命令
            command = self.case_table.item(row, 3).text()

            # 获取用例名
            case_name = self.case_table.item(row, 1).text()

            # 添加命令
            self.process_manager.add_command(row, command, case_name)

            # 更新状态
            status_item = self.case_table.item(row, 2)
            status_item.setText("等待执行")
            status_item.setForeground(QColor(128, 128, 128))

            # 清除时间
            self.case_table.item(row, 4).setText("")
            self.case_table.item(row, 5).setText("")

        # 设置按钮状态
        self.execute_button.setEnabled(False)
        self.stop_button.setEnabled(True)

        # 开始执行
        self.process_manager.start_execution()

    def stop_execution(self):
        """停止执行"""
        # 确认对话框
        reply = QMessageBox.question(
            self,
            "确认停止",
            "确定要停止所有正在执行的用例吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # 停止所有进程
        self.process_manager.stop_all()

        # 设置按钮状态
        self.execute_button.setEnabled(True)
        self.stop_button.setEnabled(False)

    def on_execution_started(self, row_index, command):
        """处理执行开始事件"""
        try:
            # 检查行索引是否有效
            if row_index < 0 or row_index >= self.case_table.rowCount():
                print(f"警告: 无效的行索引 {row_index}")
                return

            # 更新状态
            status_item = self.case_table.item(row_index, 2)
            if status_item is None:
                print(f"警告: 行 {row_index} 的状态项为空")
                # 创建新的状态项
                status_item = QTableWidgetItem()
                self.case_table.setItem(row_index, 2, status_item)

            status_item.setText("正在执行")
            status_item.setForeground(QColor(0, 0, 255))

            # 更新开始时间
            start_time_item = self.case_table.item(row_index, 4)
            if start_time_item is None:
                start_time_item = QTableWidgetItem()
                self.case_table.setItem(row_index, 4, start_time_item)

            start_time = datetime.datetime.now().strftime("%H:%M:%S")
            start_time_item.setText(start_time)

            # 获取用例名
            case_name_item = self.case_table.item(row_index, 1)
            if case_name_item is None:
                print(f"警告: 行 {row_index} 的用例名项为空")
                case_name = f"用例_{row_index}"
            else:
                case_name = case_name_item.text()

            # 创建日志标签页
            self._create_log_tab(row_index, case_name)

        except Exception as e:
            print(f"处理执行开始事件时出错: {str(e)}")

    def on_execution_finished(self, row_index, exit_code):
        """处理执行结束事件"""
        try:
            # 检查行索引是否有效
            if row_index < 0 or row_index >= self.case_table.rowCount():
                print(f"警告: 无效的行索引 {row_index}")
                return

            # 更新状态
            status_item = self.case_table.item(row_index, 2)
            if status_item is None:
                print(f"警告: 行 {row_index} 的状态项为空")
                # 创建新的状态项
                status_item = QTableWidgetItem()
                self.case_table.setItem(row_index, 2, status_item)

            if exit_code == 0:
                status_item.setText("执行成功")
                status_item.setForeground(QColor(0, 128, 0))
            else:
                status_item.setText(f"执行失败 ({exit_code})")
                status_item.setForeground(QColor(255, 0, 0))

            # 更新结束时间
            end_time_item = self.case_table.item(row_index, 5)
            if end_time_item is None:
                end_time_item = QTableWidgetItem()
                self.case_table.setItem(row_index, 5, end_time_item)

            end_time = datetime.datetime.now().strftime("%H:%M:%S")
            end_time_item.setText(end_time)

        except Exception as e:
            print(f"处理执行结束事件时出错: {str(e)}")

    def on_execution_output(self, row_index, output):
        """处理执行输出事件"""
        try:
            # 检查行索引是否有效
            if row_index < 0:
                print(f"警告: 无效的行索引 {row_index}")
                return

            # 如果日志标签页不存在，尝试创建
            if row_index not in self.log_tabs:
                # 尝试获取用例名
                try:
                    if row_index < self.case_table.rowCount():
                        case_name_item = self.case_table.item(row_index, 1)
                        if case_name_item:
                            case_name = case_name_item.text()
                        else:
                            case_name = f"用例_{row_index}"
                    else:
                        case_name = f"用例_{row_index}"

                    # 创建日志标签页
                    self._create_log_tab(row_index, case_name)
                except Exception as tab_error:
                    print(f"创建日志标签页失败: {str(tab_error)}")
                    return

            # 添加到日志
            if row_index in self.log_tabs:
                # 确保输出不为空
                if output and output.strip():
                    # 处理可能的HTML特殊字符
                    output_html = self._prepare_output_for_html(output)

                    # 获取日志编辑器
                    log_edit = self.log_tabs[row_index]
                    if log_edit is None:
                        print(f"警告: 行 {row_index} 的日志编辑器为空")
                        return

                    # 使用HTML格式添加文本，保留格式和颜色
                    log_edit.append(output_html)

                    # 滚动到底部
                    cursor = log_edit.textCursor()
                    cursor.movePosition(QTextCursor.End)
                    log_edit.setTextCursor(cursor)

                    # 确保更新UI
                    QApplication.processEvents()
        except Exception as e:
            print(f"处理执行输出时出错: {str(e)}")

    def _prepare_output_for_html(self, text):
        """准备输出文本用于HTML显示"""
        # 转义HTML特殊字符
        text = text.replace('&', '&amp;')
        text = text.replace('<', '&lt;')
        text = text.replace('>', '&gt;')

        # 保留空格和换行
        text = text.replace(' ', '&nbsp;')
        text = text.replace('\n', '<br>')

        # 使用等宽字体确保正确显示
        return f'<span style="font-family: Courier New, monospace;">{text}</span>'

    def on_all_finished(self):
        """处理所有命令执行完成事件"""
        # 设置按钮状态
        self.execute_button.setEnabled(True)
        self.stop_button.setEnabled(False)

        # 显示完成消息
        QMessageBox.information(self, "完成", "所有选中的用例已执行完成")

    def _create_log_tab(self, row_index, case_name):
        """创建日志标签页"""
        try:
            # 检查参数有效性
            if row_index < 0:
                print(f"警告: 无效的行索引 {row_index}")
                return

            if not case_name:
                case_name = f"用例_{row_index}"

            # 检查是否已存在
            if row_index in self.log_tabs and self.log_tabs[row_index] is not None:
                # 切换到已有标签页
                tab_index = self.tab_widget.indexOf(self.log_tabs[row_index])
                if tab_index != -1:
                    self.tab_widget.setCurrentIndex(tab_index)
                return

            # 创建文本编辑器
            log_edit = QTextEdit()
            log_edit.setReadOnly(True)
            log_edit.setLineWrapMode(QTextEdit.NoWrap)

            # 设置更好的字体
            font = QFont("Consolas", 10)  # 使用Consolas字体，更适合显示代码和日志
            if not font.exactMatch():  # 如果Consolas不可用
                font = QFont("Courier New", 10)  # 回退到Courier New
            if not font.exactMatch():  # 如果Courier New也不可用
                font = QFont("Monospace", 10)  # 回退到通用等宽字体
            log_edit.setFont(font)

            # 设置文本颜色和背景
            log_edit.setStyleSheet("""
                QTextEdit {
                    background-color: #F8F8F8;
                    color: #333333;
                    border: 1px solid #CCCCCC;
                }
            """)

            # 添加欢迎信息
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_edit.setHtml(f"""
                <div style="color: #0066CC; font-family: Arial, sans-serif; margin-bottom: 10px;">
                    <b>批量执行 - {case_name}</b><br>
                    <span style="font-size: smaller;">开始时间: {current_time}</span>
                </div>
                <div style="color: #666666; font-family: Consolas, 'Courier New', monospace; margin-bottom: 10px;">
                    准备执行命令，请稍候...
                </div>
            """)

            # 添加到标签页
            tab_index = self.tab_widget.addTab(log_edit, case_name)
            self.tab_widget.setCurrentIndex(tab_index)

            # 存储引用
            self.log_tabs[row_index] = log_edit

        except Exception as e:
            print(f"创建日志标签页时出错: {str(e)}")

class BatchExecutionPlugin(PluginBase):
    """批量执行插件"""

    @property
    def name(self):
        return "批量执行工具"

    @property
    def version(self):
        return "1.0.0"

    @property
    def description(self):
        return "批量执行多个用例，支持并行执行和结果管理"

    def initialize(self, main_window):
        """初始化插件"""
        try:
            self.main_window = main_window
            self.dialog = None

            # 创建菜单项
            self.menu_action = QAction(self.name, main_window)
            self.menu_action.setStatusTip(self.description)
            self.menu_action.triggered.connect(self.show_dialog)

            # 添加到工具菜单
            if hasattr(main_window, 'tools_menu'):
                main_window.tools_menu.addAction(self.menu_action)

            print(f"成功初始化插件: {self.name}")

        except Exception as e:
            print(f"初始化插件 {self.name} 失败: {str(e)}")

    def cleanup(self):
        """清理插件资源"""
        try:
            # 关闭对话框
            if self.dialog and self.dialog.isVisible():
                self.dialog.close()

            # 移除菜单项
            if hasattr(self, 'menu_action') and hasattr(self.main_window, 'tools_menu'):
                self.main_window.tools_menu.removeAction(self.menu_action)

            print(f"成功清理插件: {self.name}")

        except Exception as e:
            print(f"清理插件 {self.name} 失败: {str(e)}")

    def show_dialog(self):
        """显示批量执行对话框"""
        try:
            if not self.dialog:
                self.dialog = BatchExecutionDialog(self.main_window, self.name)

            # 显示对话框
            self.dialog.show()
            self.dialog.raise_()
            self.dialog.activateWindow()
        except KeyboardInterrupt:
            print("批量执行对话框被用户中断")
        except Exception as e:
            print(f"显示批量执行对话框时出错: {str(e)}")
            # 如果创建对话框失败，重置对话框引用
            self.dialog = None
