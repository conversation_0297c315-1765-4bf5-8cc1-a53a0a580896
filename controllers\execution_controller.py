"""
执行控制器
"""
import os
import sys
import subprocess
import logging
from PyQt5.QtWidgets import QFileDialog, QMessageBox
from PyQt5.QtCore import QObject, pyqtSlot, pyqtSignal

from views.execution_panel import ExecutionPanel
from utils.path_resolver import PathResolver
from utils.event_bus import EventBus
from utils.simulation_monitor import SimulationMonitor
from utils.dashboard_updater import get_dashboard_updater

# 配置日志
logger = logging.getLogger(__name__)

class ExecutionController(QObject):
    """执行控制器，负责管理命令执行和日志显示"""

    # 定义信号
    execution_requested = pyqtSignal(str, str)  # command, case_name

    def __init__(self, main_window, config_model, history_model):
        """
        初始化执行控制器

        Args:
            main_window (MainWindow): 主窗口
            config_model (ConfigModel): 配置模型
            history_model (HistoryModel): 历史记录模型
        """
        super().__init__()
        self.main_window = main_window
        self.config_model = config_model
        self.history_model = history_model
        self.path_resolver = PathResolver()

        # 获取事件总线实例
        self.event_bus = EventBus.instance()

        # 创建执行面板
        self.execution_panel = ExecutionPanel()

        # 初始化仿真监听器
        self.simulation_monitor = SimulationMonitor()
        self.dashboard_updater = get_dashboard_updater()

        # 设置仿真监听器的仪表盘更新器
        self.simulation_monitor.set_dashboard_updater(self.dashboard_updater)

        # 连接信号
        self.connect_signals()

    def connect_signals(self):
        """连接信号和槽"""
        # 执行面板信号
        self.execution_panel.open_verdi_requested.connect(self.open_verdi)
        self.execution_panel.open_verisium_requested.connect(self.open_verisium)
        self.execution_panel.open_compile_log_requested.connect(self.open_compile_log)
        self.execution_panel.open_sim_log_requested.connect(self.open_sim_log)
        self.execution_panel.open_asm_file_requested.connect(self.open_asm_file)
        self.execution_panel.close_tab_requested.connect(self.close_tab)

        # 执行请求信号
        self.execution_requested.connect(self.execute_command)

        # 使用事件总线连接命令执行信号
        try:
            self.event_bus.command_executed.connect(self.execute_command)
        except Exception as e:
            print(f"警告: 连接事件总线命令执行信号时出错: {str(e)}")

        # 连接仿真监听器信号
        self.simulation_monitor.simulation_started.connect(self._on_simulation_started)
        self.simulation_monitor.simulation_finished.connect(self._on_simulation_finished)

    def on_config_loaded(self, config):
        """
        配置加载后的处理

        Args:
            config (dict): 配置数据
        """
        # 这里可以处理与执行相关的配置
        pass

    def _launch_tool_with_nohup(self, case_dir, tool_name, command, script_name=None):
        """
        使用nohup启动外部工具，确保在终端关闭后工具仍能继续运行

        Args:
            case_dir (str): 用例目录
            tool_name (str): 工具名称，用于显示消息
            command (str): 要执行的命令
            script_name (str, optional): 脚本文件名，默认为"launch_{tool_name}.sh"
        """
        if script_name is None:
            script_name = f"launch_{tool_name.lower()}.sh"

        # 创建启动脚本
        script_path = os.path.join(case_dir, script_name)
        with open(script_path, "w") as f:
            f.write("#!/bin/bash\n")
            f.write("cd $(dirname \"$0\")\n")  # 切换到脚本所在目录

            # 检查命令是否已经是脚本（如run_verdi, run_verdi_vcs, run_verisium）
            if command.startswith("run_"):
                # 如果是已有脚本，直接执行它（不使用nohup，因为脚本内部可能已经处理了后台运行）
                f.write(f"./{command} > {tool_name.lower()}_launch.log 2>&1\n")
            else:
                # 否则直接执行命令
                f.write(f"{command} > {tool_name.lower()}_launch.log 2>&1\n")

        # 设置脚本可执行权限
        os.chmod(script_path, 0o755)

        # 直接在后台执行脚本，不依赖终端
        try:
            # 使用nohup直接在后台执行脚本，确保即使终端关闭也能继续运行
            subprocess.Popen(
                ['nohup', 'bash', script_path],
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                shell=False,
                start_new_session=True  # 创建新会话，与父进程完全分离
            )

            # 显示启动消息
            print(f"{tool_name}已在后台启动，可以在{case_dir}目录查看{tool_name.lower()}_launch.log日志")

        except Exception as e:
            print(f"启动工具时出错: {str(e)}")
            # 尝试其他方法启动
            try:
                # 直接执行命令，不使用终端
                full_cmd = f"cd {case_dir} && {command}"
                subprocess.Popen(
                    ['bash', '-c', full_cmd],
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                    shell=False,
                    start_new_session=True  # 创建新会话，与父进程完全分离
                )
            except Exception as e2:
                print(f"备用启动方法也失败: {str(e2)}")

    @pyqtSlot(str, str)
    def execute_command(self, command, case_name):
        """
        执行命令

        Args:
            command (str): 命令字符串
            case_name (str): 用例名称
        """
        # 导入命令解析器
        from utils.command_generator import CommandParser

        # 如果没有指定用例名称，尝试从命令中提取
        if not case_name:
            case_name = CommandParser.parse_case_from_command(command)

        # 如果仍然没有用例名称，使用时间戳或其他标识
        if not case_name:
            import time
            case_name = f"执行_{int(time.time())}"

        # 确定标签页名称：优先使用rundir，如果没有则使用case_name
        # 这是参考旧版本GUI的实现逻辑
        rundir = CommandParser.parse_rundir_from_command(command)
        tab_name = rundir if rundir else case_name

        # 添加日志标签页
        success = self.execution_panel.add_log_tab(tab_name, command)

        if success:
            # 获取日志面板并开始执行
            log_panel = self.execution_panel.get_log_panel(tab_name)
            if log_panel:
                # 连接日志面板的执行完成信号到仿真监听器
                log_panel.execution_finished.connect(
                    lambda case, exit_code: self.simulation_monitor.on_execution_finished(case, exit_code)
                )

                # 启动执行
                log_panel.start_execution()

                # 通知仿真监听器执行开始
                self.simulation_monitor.on_execution_started(case_name, command)

                # 注意：历史记录已经在config_controller.execute_command中添加，这里不需要重复添加
        else:
            self.main_window.show_warning(
                "标签页已满",
                "已达到最大标签页数量，请关闭一些标签页后再试"
            )

    def _on_simulation_started(self, case_name: str, command: str, params: dict):
        """
        处理仿真开始信号

        Args:
            case_name: 用例名称
            command: 执行命令
            params: 命令参数
        """
        logger.info(f"仿真开始: {case_name}")
        # 这里可以添加额外的处理逻辑，比如更新UI状态等

    def _on_simulation_finished(self, case_name: str, success: bool, message: str):
        """
        处理仿真结束信号

        Args:
            case_name: 用例名称
            success: 是否成功
            message: 结果消息
        """
        status = "成功" if success else "失败"
        logger.info(f"仿真结束: {case_name} - {status}")
        # 这里可以添加额外的处理逻辑，比如显示通知等

    @pyqtSlot(int)
    def close_tab(self, index):
        """
        关闭标签页

        Args:
            index (int): 标签页索引
        """
        self.execution_panel.close_tab(index)

    @pyqtSlot(str)
    def open_verdi(self, case_name):
        """
        打开 Verdi

        Args:
            case_name (str): 用例名称
        """
        if not case_name:
            self.main_window.show_warning("操作失败", "请先选择一个用例")
            return

        # 获取用例目录
        case_dirs = self.path_resolver.get_case_directories()

        # 查找匹配的用例目录
        matching_dirs = [d for d in case_dirs if case_name in d]

        if not matching_dirs:
            self.main_window.show_warning(
                "未找到用例目录",
                f"无法找到与 '{case_name}' 匹配的用例目录"
            )
            return

        # 使用第一个匹配的目录
        case_dir = matching_dirs[0]

        try:
            # 检查是否存在VCS仿真结果
            vcs_result_exists = os.path.exists(os.path.join(case_dir, "simv.daidir")) or \
                               os.path.exists(os.path.join(case_dir, "vcdplus.vpd"))

            # 根据仿真类型选择合适的命令
            if vcs_result_exists:
                # VCS仿真，使用run_verdi_vcs脚本
                verdi_cmd = "run_verdi_vcs"
                tool_name = "Verdi(VCS)"
            else:
                # XRUN仿真，使用run_verdi脚本
                verdi_cmd = "run_verdi comp_load"
                tool_name = "Verdi(XRUN)"

            # 构建完整命令
            full_cmd = f"cd {case_dir} && {verdi_cmd}"

            # 参考旧版本实现，使用CaseTab方式启动Verdi
            # 创建新的标签页并执行命令
            from views.log_panel import LogPanel

            # 创建标签页名称
            tab_name = f"Verdi_{os.path.basename(case_dir)}"

            # 添加日志标签页
            success = self.execution_panel.add_log_tab(tab_name, full_cmd)

            if success:
                # 获取日志面板并开始执行
                log_panel = self.execution_panel.get_log_panel(tab_name)
                if log_panel:
                    log_panel.start_execution()
                    self.main_window.show_message(f"正在打开 {tool_name}: {case_dir}")
            else:
                self.main_window.show_warning(
                    "标签页已满",
                    "已达到最大标签页数量，请关闭一些标签页后再试"
                )

        except Exception as e:
            self.main_window.show_error(
                "启动 Verdi 失败",
                f"无法启动 Verdi: {str(e)}"
            )

    @pyqtSlot(str)
    def open_verisium(self, case_name):
        """
        打开 Verisium

        Args:
            case_name (str): 用例名称
        """
        if not case_name:
            self.main_window.show_warning("操作失败", "请先选择一个用例")
            return

        # 获取用例目录
        case_dirs = self.path_resolver.get_case_directories()

        # 查找匹配的用例目录
        matching_dirs = [d for d in case_dirs if case_name in d]

        if not matching_dirs:
            self.main_window.show_warning(
                "未找到用例目录",
                f"无法找到与 '{case_name}' 匹配的用例目录"
            )
            return

        # 使用第一个匹配的目录
        case_dir = matching_dirs[0]

        try:
            # 使用run_verisium脚本启动Verisium
            verisium_cmd = "run_verisium"

            # 构建完整命令
            full_cmd = f"cd {case_dir} && {verisium_cmd}"

            # 参考旧版本实现，使用CaseTab方式启动Verisium
            # 创建新的标签页并执行命令
            from views.log_panel import LogPanel

            # 创建标签页名称
            tab_name = f"Verisium_{os.path.basename(case_dir)}"

            # 添加日志标签页
            success = self.execution_panel.add_log_tab(tab_name, full_cmd)

            if success:
                # 获取日志面板并开始执行
                log_panel = self.execution_panel.get_log_panel(tab_name)
                if log_panel:
                    log_panel.start_execution()
                    self.main_window.show_message(f"正在打开 Verisium: {case_dir}")
            else:
                self.main_window.show_warning(
                    "标签页已满",
                    "已达到最大标签页数量，请关闭一些标签页后再试"
                )

        except Exception as e:
            self.main_window.show_error(
                "启动 Verisium 失败",
                f"无法启动 Verisium: {str(e)}"
            )

    @pyqtSlot(str)
    def open_compile_log(self, case_name):
        """
        打开编译日志

        Args:
            case_name (str): 用例名称
        """
        if not case_name:
            self.main_window.show_warning("操作失败", "请先选择一个用例")
            return

        # 获取用例目录
        case_dirs = self.path_resolver.get_case_directories()

        # 查找匹配的用例目录
        matching_dirs = [d for d in case_dirs if case_name in d]

        if not matching_dirs:
            self.main_window.show_warning(
                "未找到用例目录",
                f"无法找到与 '{case_name}' 匹配的用例目录"
            )
            return

        # 使用第一个匹配的目录
        case_dir = matching_dirs[0]

        # 获取日志文件路径
        log_file = self.path_resolver.get_log_file_path(case_dir, "compile")

        if not log_file or not os.path.exists(log_file):
            self.main_window.show_warning(
                "未找到日志文件",
                f"无法找到编译日志文件: {log_file}"
            )
            return

        # 打开日志文件
        try:
            if sys.platform == 'win32':
                os.startfile(log_file)
            else:
                subprocess.Popen(['xdg-open', log_file])

            self.main_window.show_message(f"正在打开编译日志: {log_file}")

        except Exception as e:
            self.main_window.show_error(
                "打开日志文件失败",
                f"无法打开编译日志文件: {str(e)}"
            )

    @pyqtSlot(str)
    def open_sim_log(self, case_name):
        """
        打开仿真日志

        Args:
            case_name (str): 用例名称
        """
        if not case_name:
            self.main_window.show_warning("操作失败", "请先选择一个用例")
            return

        # 获取用例目录
        case_dirs = self.path_resolver.get_case_directories()

        # 查找匹配的用例目录
        matching_dirs = [d for d in case_dirs if case_name in d]

        if not matching_dirs:
            self.main_window.show_warning(
                "未找到用例目录",
                f"无法找到与 '{case_name}' 匹配的用例目录"
            )
            return

        # 使用第一个匹配的目录
        case_dir = matching_dirs[0]

        # 获取日志文件路径
        log_file = self.path_resolver.get_log_file_path(case_dir, "sim")

        if not log_file or not os.path.exists(log_file):
            self.main_window.show_warning(
                "未找到日志文件",
                f"无法找到仿真日志文件: {log_file}"
            )
            return

        # 打开日志文件
        try:
            if sys.platform == 'win32':
                os.startfile(log_file)
            else:
                subprocess.Popen(['xdg-open', log_file])

            self.main_window.show_message(f"正在打开仿真日志: {log_file}")

        except Exception as e:
            self.main_window.show_error(
                "打开日志文件失败",
                f"无法打开仿真日志文件: {str(e)}"
            )

    @pyqtSlot(str)
    def open_asm_file(self, case_name):
        """
        打开反汇编文件

        Args:
            case_name (str): 用例名称
        """
        if not case_name:
            self.main_window.show_warning("操作失败", "请先选择一个用例")
            return

        # 获取用例目录
        case_dirs = self.path_resolver.get_case_directories()

        # 查找匹配的用例目录
        matching_dirs = [d for d in case_dirs if case_name in d]

        if not matching_dirs:
            self.main_window.show_warning(
                "未找到用例目录",
                f"无法找到与 '{case_name}' 匹配的用例目录"
            )
            return

        # 使用第一个匹配的目录
        case_dir = matching_dirs[0]

        # 查找反汇编文件
        asm_files = self.path_resolver.find_asm_files(case_dir)

        if not asm_files:
            self.main_window.show_warning(
                "未找到反汇编文件",
                f"在 '{case_dir}' 中未找到反汇编文件"
            )
            return

        # 如果只有一个文件，直接打开
        if len(asm_files) == 1:
            asm_file = asm_files[0]
            try:
                if sys.platform == 'win32':
                    os.startfile(asm_file)
                else:
                    subprocess.Popen(['xdg-open', asm_file])

                self.main_window.show_message(f"正在打开反汇编文件: {asm_file}")

            except Exception as e:
                self.main_window.show_error(
                    "打开反汇编文件失败",
                    f"无法打开反汇编文件: {str(e)}"
                )
        else:
            # 如果有多个文件，让用户选择
            file_path, _ = QFileDialog.getOpenFileName(
                self.main_window,
                "选择反汇编文件",
                case_dir,
                "反汇编文件 (*.asm);;所有文件 (*.*)"
            )

            if file_path:
                try:
                    if sys.platform == 'win32':
                        os.startfile(file_path)
                    else:
                        subprocess.Popen(['xdg-open', file_path])

                    self.main_window.show_message(f"正在打开反汇编文件: {file_path}")

                except Exception as e:
                    self.main_window.show_error(
                        "打开反汇编文件失败",
                        f"无法打开反汇编文件: {str(e)}"
                    )
