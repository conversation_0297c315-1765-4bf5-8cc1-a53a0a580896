#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TestPlan表格生成器
基于提供的TestPlan格式生成标准的Excel测试计划表格
"""

import openpyxl
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.utils import get_column_letter
import os

def create_testplan_template():
    """创建TestPlan模板表格"""

    # 创建工作簿
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "TP"

    # 定义样式
    header_font = Font(name='Arial', size=10, bold=True, color='000000')
    normal_font = Font(name='Arial', size=9, color='000000')

    # 黄色背景（表头）
    yellow_fill = PatternFill(start_color='FFFF00', end_color='FFFF00', fill_type='solid')
    # 绿色背景（PASS状态）
    green_fill = PatternFill(start_color='00FF00', end_color='00FF00', fill_type='solid')
    # 红色背景（FAIL状态）
    red_fill = PatternFill(start_color='FF0000', end_color='FF0000', fill_type='solid')
    # 灰色背景（N/A状态）
    gray_fill = PatternFill(start_color='C0C0C0', end_color='C0C0C0', fill_type='solid')

    # 边框样式
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )

    # 居中对齐
    center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    left_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)

    # 第一行：项目信息
    ws['A1'] = 'Project: Example_SOC'
    ws.merge_cells('A1:U1')
    ws['A1'].font = header_font
    ws['A1'].fill = yellow_fill
    ws['A1'].alignment = center_alignment
    ws['A1'].border = thin_border

    # 第二行：子系统信息
    ws['A2'] = 'Subsystem: ADCV2_DV'
    ws.merge_cells('A2:U2')
    ws['A2'].font = header_font
    ws['A2'].fill = yellow_fill
    ws['A2'].alignment = center_alignment
    ws['A2'].border = thin_border

    # 第三行和第四行：表头
    headers_row3 = [
        'Test Category', 'Items', 'Test Areas', 'Function points', 'Test Scope', 'Check Point',
        'Cover', 'TestCase Name', 'Start Time', 'End Time', 'Actual Time', 'Owner',
        'Subsys', '', 'TOP', '', 'POST_Subsys', '', 'POST_TOP', '', 'Note'
    ]

    headers_row4 = [
        '', '', '', '', '', '', '', '', '', '', '', '',
        'Phase', 'Status', 'Phase', 'Status', 'Phase', 'Status', 'Phase', 'Status', ''
    ]

    # 写入第三行表头
    for col, header in enumerate(headers_row3, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = header_font
        cell.fill = yellow_fill
        cell.alignment = center_alignment
        cell.border = thin_border

    # 写入第四行表头
    for col, header in enumerate(headers_row4, 1):
        cell = ws.cell(row=4, column=col, value=header)
        cell.font = header_font
        cell.fill = yellow_fill
        cell.alignment = center_alignment
        cell.border = thin_border

    # 合并表头单元格
    merge_ranges = [
        'A3:A4', 'B3:B4', 'C3:C4', 'D3:D4', 'E3:E4', 'F3:F4', 'G3:G4', 'H3:H4',
        'I3:I4', 'J3:J4', 'K3:K4', 'L3:L4', 'M3:N3', 'O3:P3', 'Q3:R3', 'S3:T3', 'U3:U4'
    ]

    for range_str in merge_ranges:
        ws.merge_cells(range_str)

    # 示例数据
    test_data = [
        # MEM类别
        ['MEM', 'APC_MEM_BIST_001', 'Memory BIST', 'Function:内存BIST功能验证\nFeature:验证内存BIST基本功能', 'BIST测试流程', '检查BIST结果', 'BIST功能覆盖', 'apc_mem_bist_001', '2024/03/15', '2024/03/16', '', 'Owner1', 'DVR1', 'PASS', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', ''],
        ['', 'APC_MEM_BIST_002', 'Memory Error Detection', 'Function:内存BIST错误检测\nFeature:验证内存BIST错误检测功能', 'BIST错误注入', '检查错误响应', '错误处理覆盖', 'apc_mem_bist_002', '2024/03/17', '2024/03/18', '', 'Owner1', 'DVR1', 'PASS', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', ''],

        # BUS ACCESS类别
        ['BUS ACCESS', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''],
        ['BUS', 'APC_BUS_001', 'Bus Normal Access', 'Function:总线访问正常路径验证\nFeature:验证默认模式下总线正常访问', '总线访问测试', '检查访问结果', '正常路径覆盖', 'apc_bus_001', '2024/04/15', '2024/04/16', '', 'Owner2', 'DVR1', 'PASS', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', ''],
        ['BUS', 'APC_BUS_002', 'Bus Power Down Access', 'Function:掉电模式总线访问\nFeature:验证掉电模式下总线访问', '掉电模式测试', '检查掉电响应', '掉电场景覆盖', 'apc_bus_002', '2024/04/17', '2024/04/18', '', 'Owner2', 'DVR1', 'PASS', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', ''],
        ['BUS', 'APC_BUS_003', 'Bus Timeout', 'Function:总线超时处理\nFeature:验证总线超时机制', '超时测试流程', '检查超时处理', '超时场景覆盖', 'apc_bus_003', '2024/04/19', '2024/04/20', '', 'Owner2', 'DVR1', 'PASS', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', ''],
        ['BUS', 'APC_BUS_004', 'Bus Error Handling', 'Function:总线错误处理\nFeature:验证总线错误处理机制', '错误注入测试', '检查错误处理', '错误场景覆盖', 'apc_bus_004', '2024/04/21', '2024/04/22', '', 'Owner2', 'DVR1', 'PASS', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', ''],
        ['BUS', 'APC_BUS_005', 'Bus Normal Function', 'Function:正常功能测试\nFeature:验证正常工作模式', '正常功能测试', '检查功能正确性', '正常功能覆盖', 'apc_bus_005', '2024/04/23', '2024/04/24', '', 'Owner2', 'DVR1', 'PASS', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', ''],
        ['BUS', 'APC_BUS_006', 'Bus Power Down', 'Function:掉电测试\nFeature:验证掉电功能', '掉电功能测试', '检查掉电行为', '掉电功能覆盖', 'apc_bus_006', '2024/04/25', '2024/04/26', '', 'Owner2', 'DVR1', 'PASS', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', ''],
        ['BUS', 'APC_BUS_007', 'Bus Signal Glitch', 'Function:信号毛刺测试\nFeature:验证信号毛刺处理', '毛刺注入测试', '检查毛刺处理', '毛刺场景覆盖', 'apc_bus_007', '2024/04/27', '2024/04/28', '', 'Owner2', 'DVR1', 'PASS', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', ''],
        ['BUS', 'APC_BUS_008', 'Bus Interrupt', 'Function:中断测试\nFeature:验证中断处理机制', '中断测试流程', '检查中断响应', '中断处理覆盖', 'apc_bus_008', '2024/04/29', '2024/04/30', '', 'Owner2', 'DVR1', 'PASS', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', ''],

        # RUNTIME ACCESS类别
        ['RUNTIME ACCESS', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''],
        ['MEM', 'APC_MEM_RT_001', 'Memory Connection', 'Function:SRAM/ROM连接测试\nFeature:验证存储器连接', '连接测试流程', '检查连接状态', '连接功能覆盖', 'apc_mem_rt_001', '2024/05/01', '2024/05/02', '2024/05/01', 'Owner3', 'DVR1', 'PASS', 'DVR2', 'PASS', 'N/A', 'N/A', 'N/A', 'N/A', ''],
        ['MEM', 'APC_MEM_RT_002', 'SPI Connection', 'Function:SPI连接测试\nFeature:验证SPI接口连接', 'SPI连接测试', '检查SPI接口', 'SPI功能覆盖', 'apc_mem_rt_002', '2024/05/03', '2024/05/04', '', 'Owner3', 'N/A', 'N/A', 'DVR2', 'PASS', 'N/A', 'N/A', 'N/A', 'N/A', ''],
        ['MEM', 'APC_MEM_RT_003', 'SPI Function', 'Function:SPI连接测试2\nFeature:验证SPI接口连接功能', 'SPI功能测试', '检查SPI功能', 'SPI高级覆盖', 'apc_mem_rt_003', '2024/05/05', '2024/05/06', '', 'Owner3', 'DVR1', 'PASS', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', ''],
        ['MEM', 'APC_MEM_RT_004', 'SPI Advanced', 'Function:SPI连接测试3\nFeature:验证SPI接口高级功能', 'SPI高级测试', '检查高级功能', 'SPI边界覆盖', 'apc_mem_rt_004', '2024/05/07', '2024/05/08', '', 'Owner3', 'N/A', 'N/A', 'DVR2', 'PASS', 'N/A', 'N/A', 'N/A', 'N/A', ''],

        # SBR TEST类别
        ['SBR TEST', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''],
        ['REG', 'APC_SBR_001', 'Reset Test', 'Function:复位测试\nFeature:验证复位功能', '复位测试流程', '检查复位行为', '复位功能覆盖', 'apc_sbr_001', '2024/05/11', '2024/05/12', '', 'Owner4', 'DVR1', 'PASS', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', ''],
        ['REG', 'APC_SBR_002', 'APB Register', 'Function:APB寄存器测试\nFeature:验证APB寄存器功能', 'APB寄存器测试', '检查寄存器读写', '寄存器功能覆盖', 'apc_sbr_002', '2024/05/13', '2024/05/14', '', 'Owner4', 'DVR2', 'PASS', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', ''],
        ['REG', 'APC_SBR_003', 'APB Advanced', 'Function:APB寄存器测试2\nFeature:验证APB寄存器高级功能', 'APB高级测试', '检查高级功能', '高级场景覆盖', 'apc_sbr_003', '2024/05/15', '2024/05/16', '', 'Owner4', 'DVR2', 'PASS', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', ''],
        ['REG', 'APC_SBR_004', 'APB Boundary', 'Function:APB寄存器测试3\nFeature:验证APB寄存器边界条件', 'APB边界测试', '检查边界条件', '边界场景覆盖', 'apc_sbr_004', '2024/05/17', '2024/05/18', '', 'Owner4', 'DVR2', 'PASS', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', ''],
    ]

    # 写入测试数据
    for row_idx, row_data in enumerate(test_data, 5):  # 从第5行开始
        for col_idx, value in enumerate(row_data, 1):
            cell = ws.cell(row=row_idx, column=col_idx, value=value)
            cell.font = normal_font
            cell.border = thin_border

            # 设置对齐方式
            if col_idx in [1, 2, 3, 4]:  # 文本列左对齐
                cell.alignment = left_alignment
            else:  # 其他列居中对齐
                cell.alignment = center_alignment

            # 设置状态颜色
            if value == 'PASS':
                cell.fill = green_fill
            elif value == 'FAIL':
                cell.fill = red_fill
            elif value == 'N/A':
                cell.fill = gray_fill

    # 设置列宽
    column_widths = {
        'A': 15,  # Test Category
        'B': 20,  # Items
        'C': 25,  # Test Areas
        'D': 35,  # Function points
        'E': 15,  # Test Scope
        'F': 15,  # Check Point
        'G': 15,  # Cover
        'H': 20,  # TestCase Name
        'I': 12,  # Start Time
        'J': 12,  # End Time
        'K': 12,  # Actual Time
        'L': 8,   # Owner
        'M': 8,   # Subsys Phase
        'N': 8,   # Subsys Status
        'O': 8,   # TOP Phase
        'P': 8,   # TOP Status
        'Q': 8,   # POST_Subsys Phase
        'R': 8,   # POST_Subsys Status
        'S': 8,   # POST_TOP Phase
        'T': 8,   # POST_TOP Status
        'U': 15,  # Note
    }

    for col_letter, width in column_widths.items():
        ws.column_dimensions[col_letter].width = width

    # 设置行高
    for row in range(1, len(test_data) + 5):
        ws.row_dimensions[row].height = 25

    return wb

def create_case_status_sheet(wb):
    """创建case_status工作表"""
    ws = wb.create_sheet("case_status for soc")

    # 定义样式
    header_font = Font(name='Arial', size=10, bold=True, color='000000')
    normal_font = Font(name='Arial', size=9, color='000000')
    yellow_fill = PatternFill(start_color='FFFF00', end_color='FFFF00', fill_type='solid')

    # 边框样式
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )

    # 居中对齐
    center_alignment = Alignment(horizontal='center', vertical='center')

    # 表头
    headers = [
        'Category', 'Total', 'DVR1_PASS', 'DVR1_FAIL', 'DVR1_N/A',
        'DVR2_PASS', 'DVR2_FAIL', 'DVR2_N/A', 'DVR3_PASS', 'DVR3_FAIL', 'DVR3_N/A',
        'DVS1_PASS', 'DVS1_FAIL', 'DVS1_N/A', 'DVS2_PASS', 'DVS2_FAIL', 'DVS2_N/A'
    ]

    # 写入表头
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = yellow_fill
        cell.alignment = center_alignment
        cell.border = thin_border

    # 示例统计数据
    status_data = [
        ['MEM', 2, 2, 0, 0, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2],
        ['BUS ACCESS', 8, 8, 0, 0, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8],
        ['RUNTIME ACCESS', 4, 2, 0, 2, 2, 0, 2, 0, 0, 4, 0, 0, 4, 0, 0, 4],
        ['SBR TEST', 4, 1, 0, 3, 3, 0, 1, 0, 0, 4, 0, 0, 4, 0, 0, 4],
        ['TOTAL', 18, 13, 0, 5, 5, 0, 13, 0, 0, 18, 0, 0, 18, 0, 0, 18],
    ]

    # 写入统计数据
    for row_idx, row_data in enumerate(status_data, 2):
        for col_idx, value in enumerate(row_data, 1):
            cell = ws.cell(row=row_idx, column=col_idx, value=value)
            cell.font = normal_font
            cell.alignment = center_alignment
            cell.border = thin_border

    # 设置列宽
    for col in range(1, len(headers) + 1):
        ws.column_dimensions[get_column_letter(col)].width = 12

    return wb

def main():
    """主函数"""
    print("正在生成TestPlan模板...")

    # 创建TestPlan表格
    wb = create_testplan_template()

    # 创建case_status表格
    wb = create_case_status_sheet(wb)

    # 保存文件
    output_file = "TestPlan_Template.xlsx"
    wb.save(output_file)

    print(f"TestPlan模板已生成: {output_file}")
    print("\n表格包含以下内容:")
    print("1. TP工作表 - 主要的测试计划表格")
    print("2. case_status for soc工作表 - 用例状态统计表")
    print("\n表格特点:")
    print("- 标准的TestPlan格式，包含项目和子系统信息")
    print("- 完整的表头结构，支持多个验证阶段")
    print("- 示例测试用例数据")
    print("- 状态颜色标识（绿色=PASS，红色=FAIL，灰色=N/A）")
    print("- 自动调整的列宽和行高")

if __name__ == "__main__":
    main()
