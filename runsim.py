#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RunSim 模拟脚本 - Windows环境下的runsim命令模拟
用于测试RunSim GUI的日志刷新功能
"""
import sys
import os
import time
import argparse
import random
from datetime import datetime

# 设置Windows控制台编码
if sys.platform == 'win32':
    try:
        # 设置控制台代码页为UTF-8
        os.system('chcp 65001 >nul 2>&1')

        # 重新配置标准输出和错误输出
        import io
        import codecs

        # 使用UTF-8编码包装标准输出
        if hasattr(sys.stdout, 'buffer'):
            sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
        if hasattr(sys.stderr, 'buffer'):
            sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')

        # 确保输出立即刷新
        sys.stdout.reconfigure(line_buffering=True)
        sys.stderr.reconfigure(line_buffering=True)

    except Exception as e:
        # 如果设置失败，使用默认编码但添加错误处理
        print(f"Warning: Failed to set UTF-8 encoding: {e}", file=sys.stderr)
        pass

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='RunSim 仿真脚本模拟器')

    # 基本参数
    parser.add_argument('-base', type=str, help='基础配置')
    parser.add_argument('-block', type=str, help='模块配置')
    parser.add_argument('-case', type=str, help='测试案例名称')
    parser.add_argument('-rundir', type=str, help='运行目录')
    parser.add_argument('-regr', type=str, help='回归列表文件')

    # 仿真选项
    parser.add_argument('-fsdb', action='store_true', help='生成FSDB波形文件')
    parser.add_argument('-vwdb', action='store_true', help='生成VWDB波形文件')
    parser.add_argument('-cl', action='store_true', help='编译后清理')
    parser.add_argument('-cov', action='store_true', help='覆盖率收集')
    parser.add_argument('-upf', action='store_true', help='UPF低功耗仿真')
    parser.add_argument('-R', action='store_true', help='仅运行仿真')
    parser.add_argument('-C', action='store_true', help='仅编译')

    # 其他选项
    parser.add_argument('-seed', type=str, help='随机种子')
    parser.add_argument('-dump_mem', type=str, help='内存dump选项')
    parser.add_argument('-simarg', type=str, help='仿真参数')
    parser.add_argument('-cfg_def', type=str, help='配置定义')
    parser.add_argument('-post', type=str, help='后处理脚本')
    parser.add_argument('-wdd', type=str, help='波形数据目录')
    parser.add_argument('-fm', action='store_true', help='形式化验证')
    parser.add_argument('-bp', type=str, help='断点服务器')

    return parser.parse_args()

def simulate_compilation(case_name, args):
    """模拟编译过程"""
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 开始编译 {case_name}")
    print(f"编译器版本: VCS-2023.03 (模拟)")
    print(f"工作目录: {os.getcwd()}")
    print(f"编译目标: {case_name}")
    print("=" * 50)

    if args.base:
        print(f"基础配置: {args.base}")
    if args.block:
        print(f"模块配置: {args.block}")

    # 模拟环境检查
    print("检查编译环境...")
    env_checks = [
        "检查VCS许可证... ✓",
        "检查SystemVerilog编译器... ✓",
        "检查设计库路径... ✓",
        "检查工具版本兼容性... ✓",
        "验证编译选项... ✓"
    ]

    for check in env_checks:
        print(f"  {check}")
        time.sleep(0.2)

    print("\n开始详细编译过程:")
    print("-" * 40)

    # 模拟编译步骤
    compile_steps = [
        "解析设计文件...",
        "分析SystemVerilog代码...",
        "处理接口和模块...",
        "生成仿真可执行文件...",
        "链接库文件...",
        "优化编译结果..."
    ]

    for i, step in enumerate(compile_steps, 1):
        print(f"[编译步骤 {i}/{len(compile_steps)}] {step}")

        # 每个步骤的详细输出
        if i == 1:
            files = ["top.sv", "cpu_core.sv", "memory_ctrl.sv", "bus_fabric.sv", "interrupt_ctrl.sv"]
            for j, file in enumerate(files, 1):
                print(f"  解析文件 {j}/{len(files)}: {file}")
                time.sleep(0.3)

        elif i == 2:
            print("  分析模块依赖关系...")
            time.sleep(0.4)
            print("  发现 15 个模块")
            print("  发现 8 个接口")
            print("  发现 23 个端口连接")
            print("  检查语法正确性... ✓")
            print("  检查时序约束... ✓")

        elif i == 3:
            modules = ["cpu_core", "alu_unit", "register_file", "cache_ctrl", "mmu_unit"]
            for j, module in enumerate(modules, 1):
                print(f"  处理模块 {j}/{len(modules)}: {module}")
                time.sleep(0.2)
                print(f"    - 端口数量: {random.randint(8, 32)}")
                print(f"    - 内部信号: {random.randint(50, 200)}")

        elif i == 4:
            print("  生成中间代码...")
            time.sleep(0.5)
            print("  生成可执行文件: simv")
            print("  文件大小: 15.2 MB")
            print("  优化级别: O2")

        elif i == 5:
            libs = ["libvcs.so", "libucli.so", "libvirsim.so", "libsynopsys_sim.so"]
            for lib in libs:
                print(f"  链接库: {lib}")
                time.sleep(0.2)
            print("  链接系统库...")

        elif i == 6:
            print("  代码优化...")
            print("  死代码消除... ✓")
            print("  常量传播... ✓")
            print("  循环展开... ✓")
            print("  内联优化... ✓")

        time.sleep(0.3 + random.uniform(0, 0.3))

    # 额外选项处理
    if args.cov:
        print("\n配置覆盖率收集:")
        print("  启用行覆盖率...")
        print("  启用分支覆盖率...")
        print("  启用条件覆盖率...")
        print("  配置覆盖率数据库...")
        time.sleep(0.5)

    if args.upf:
        print("\n配置UPF低功耗仿真:")
        print("  解析UPF文件...")
        print("  配置电源域...")
        print("  设置电源状态...")
        time.sleep(0.4)

    if args.fsdb:
        print("\n配置FSDB波形生成:")
        print("  设置波形输出路径...")
        print("  配置信号层次...")
        time.sleep(0.3)

    print("\n" + "=" * 50)
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 编译完成")
    print("编译统计信息:")
    print(f"  编译时间: {random.randint(45, 120)} 秒")
    print(f"  内存使用: {random.randint(800, 1500)} MB")
    print(f"  生成文件数: {random.randint(15, 30)}")
    print("=" * 50)
    return True

def simulate_simulation(case_name, args):
    """模拟仿真过程"""
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 开始仿真 {case_name}")
    print("=" * 50)

    # 仿真环境初始化
    print("初始化仿真环境...")
    init_steps = [
        "加载仿真可执行文件...",
        "初始化仿真内核...",
        "设置仿真参数...",
        "配置信号监控...",
        "准备波形输出..."
    ]

    for step in init_steps:
        print(f"  {step}")
        time.sleep(0.3)

    # 设置随机种子
    seed = args.seed if args.seed else str(random.randint(1, 999999))
    print(f"\n随机种子: {seed}")
    print(f"仿真模式: {'回归测试' if args.regr else '单用例测试'}")

    # 配置选项输出
    if args.dump_mem:
        print(f"内存dump选项: {args.dump_mem}")
        print("  配置内存监控...")
        print("  设置dump触发条件...")

    if args.fsdb:
        print("\n配置FSDB波形文件:")
        print("  波形文件路径: ./waves.fsdb")
        print("  信号深度: 全层次")
        print("  压缩级别: 最高")
        print("  自动刷新: 启用")

    if args.vwdb:
        print("\n配置VWDB波形文件:")
        print("  波形数据库: ./waves.vwdb")
        print("  索引模式: 快速")

    # 模拟仿真运行
    simulation_duration = random.randint(30, 60)  # 30-60秒的仿真
    print(f"\n预计仿真时间: {simulation_duration} 秒")
    print("开始仿真执行...")
    print("-" * 40)

    # 仿真阶段
    phases = [
        ("复位阶段", 5),
        ("初始化阶段", 8),
        ("主要测试阶段", simulation_duration - 20),
        ("结束阶段", 7)
    ]

    current_sim_time = 0

    for phase_name, phase_duration in phases:
        print(f"\n>>> {phase_name} (预计 {phase_duration} 秒) <<<")

        for i in range(phase_duration):
            current_time = current_sim_time * 1000  # 模拟时间单位ns

            # 基础时钟和状态输出
            if i % 2 == 0:
                cycle = current_sim_time // 2
                print(f"@{current_time}ns: 时钟周期 {cycle}")

            # 模块状态更新
            if i % 3 == 0:
                modules = ['cpu_core', 'memory_ctrl', 'bus_fabric', 'interrupt_ctrl', 'cache_unit', 'dma_ctrl']
                module_name = random.choice(modules)
                actions = ['状态更新', '数据传输', '中断处理', '缓存刷新', '总线仲裁']
                action = random.choice(actions)
                print(f"@{current_time}ns: {module_name} - {action}")

            # 信号变化
            if i % 4 == 0:
                signals = [
                    ('clk', ['0', '1']),
                    ('rst_n', ['0', '1']),
                    ('valid', ['0', '1']),
                    ('ready', ['0', '1']),
                    ('data[31:0]', [f'0x{random.randint(0, 0xFFFFFFFF):08X}']),
                    ('addr[31:0]', [f'0x{random.randint(0x1000, 0xFFFF):04X}'])
                ]
                signal_name, possible_values = random.choice(signals)
                value = random.choice(possible_values)
                print(f"@{current_time}ns: {signal_name} = {value}")

            # 事务和协议信息
            if i % 6 == 0:
                transactions = [
                    "AXI读事务开始",
                    "AXI写事务完成",
                    "APB配置访问",
                    "AHB突发传输",
                    "中断向量更新",
                    "DMA传输启动"
                ]
                transaction = random.choice(transactions)
                print(f"@{current_time}ns: PROTOCOL: {transaction}")

            # 检查点和里程碑
            if i % 10 == 0:
                checkpoint = current_sim_time // 10
                print(f"@{current_time}ns: *** 检查点 {checkpoint} - {phase_name} ***")

                # 添加一些统计信息
                if checkpoint % 3 == 0:
                    print(f"@{current_time}ns: STATS: 已执行指令 {random.randint(1000, 5000)}")
                    print(f"@{current_time}ns: STATS: 内存访问次数 {random.randint(500, 2000)}")

            # 随机事件和消息
            if random.random() < 0.15:  # 15%概率
                event_types = [
                    ('INFO', [
                        f"仿真进度 {(current_sim_time/simulation_duration)*100:.1f}%",
                        f"当前频率: {random.randint(100, 500)} MHz",
                        f"功耗估算: {random.uniform(0.5, 2.5):.2f} W",
                        "缓存命中率: 95.2%",
                        "流水线效率: 87.5%"
                    ]),
                    ('WARNING', [
                        "检测到时序违例",
                        "总线争用检测",
                        "缓存一致性警告",
                        "功耗超出预期",
                        "时钟域交叉警告"
                    ]),
                    ('DEBUG', [
                        "断点触发",
                        "观察点命中",
                        "内存访问跟踪",
                        "信号跳变检测",
                        "协议违例检查"
                    ])
                ]

                msg_type, messages = random.choice(event_types)
                message = random.choice(messages)
                print(f"@{current_time}ns: {msg_type}: {message}")

            # 特殊阶段的特定输出
            if phase_name == "复位阶段" and i < 3:
                print(f"@{current_time}ns: RESET: 复位信号有效")
            elif phase_name == "初始化阶段":
                if i == 2:
                    print(f"@{current_time}ns: INIT: 寄存器初始化完成")
                elif i == 5:
                    print(f"@{current_time}ns: INIT: 内存映射配置完成")
            elif phase_name == "主要测试阶段":
                if i % 15 == 0:
                    test_cases = ["数据路径测试", "中断响应测试", "缓存一致性测试", "总线压力测试", "功耗管理测试"]
                    test_case = random.choice(test_cases)
                    print(f"@{current_time}ns: TEST: 执行 {test_case}")

            current_sim_time += 1
            time.sleep(0.8)  # 稍微快一点的输出

    # 仿真结束处理
    final_time = current_sim_time * 1000
    print(f"\n@{final_time}ns: 仿真执行结束")
    print("=" * 50)

    # 详细的结果统计
    success = random.choice([True, True, True, False])  # 75%成功率

    print("仿真结果统计:")
    print("-" * 30)

    if success:
        print("仿真状态: ✓ PASS")
        print("错误数量: 0")
        print("警告数量: 2")
        print("信息数量: 15")
    else:
        print("仿真状态: ✗ FAIL")
        print("错误数量: 1")
        print("警告数量: 5")
        print("信息数量: 12")
        failure_reasons = [
            "断言失败 @ 15000ns",
            "协议违例 @ 23500ns",
            "时序约束违反 @ 18200ns",
            "内存访问错误 @ 31000ns"
        ]
        print(f"失败原因: {random.choice(failure_reasons)}")

    # 性能统计
    print(f"\n性能统计:")
    print(f"  仿真时间: {simulation_duration} 秒")
    print(f"  仿真周期: {current_sim_time // 2}")
    print(f"  平均频率: {random.randint(200, 800)} MHz")
    print(f"  内存使用: {random.randint(1200, 2500)} MB")
    print(f"  CPU使用率: {random.randint(60, 95)}%")

    if args.cov:
        print(f"\n覆盖率统计:")
        line_cov = random.uniform(85, 98)
        branch_cov = random.uniform(80, 95)
        cond_cov = random.uniform(75, 90)
        print(f"  行覆盖率: {line_cov:.1f}%")
        print(f"  分支覆盖率: {branch_cov:.1f}%")
        print(f"  条件覆盖率: {cond_cov:.1f}%")
        print(f"  总体覆盖率: {(line_cov + branch_cov + cond_cov) / 3:.1f}%")

    if args.fsdb or args.vwdb:
        print(f"\n波形文件:")
        if args.fsdb:
            print(f"  FSDB文件: waves.fsdb ({random.randint(50, 200)} MB)")
        if args.vwdb:
            print(f"  VWDB文件: waves.vwdb ({random.randint(80, 300)} MB)")

    print("=" * 50)
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 仿真完成")
    return success

def main():
    """主函数"""
    print("=" * 60)
    print("RunSim 仿真脚本模拟器 v2.0 (增强版)")
    print("支持详细日志输出和编码测试")
    print("=" * 60)

    # 显示系统信息
    print(f"系统平台: {sys.platform}")
    print(f"Python版本: {sys.version.split()[0]}")
    print(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"工作目录: {os.getcwd()}")

    args = parse_arguments()

    # 确定案例名称
    case_name = args.case if args.case else "default_case"

    print("\n" + "=" * 60)
    print("执行配置信息:")
    print("-" * 30)
    print(f"案例名称: {case_name}")
    print(f"基础配置: {args.base if args.base else '未指定'}")
    print(f"模块配置: {args.block if args.block else '未指定'}")
    print(f"运行目录: {args.rundir if args.rundir else '当前目录'}")
    print(f"随机种子: {args.seed if args.seed else '自动生成'}")

    # 显示启用的选项
    enabled_options = []
    if args.fsdb: enabled_options.append("FSDB波形")
    if args.vwdb: enabled_options.append("VWDB波形")
    if args.cov: enabled_options.append("覆盖率收集")
    if args.upf: enabled_options.append("UPF低功耗")
    if args.cl: enabled_options.append("编译后清理")
    if args.R: enabled_options.append("仅运行")
    if args.C: enabled_options.append("仅编译")
    if args.fm: enabled_options.append("形式化验证")

    print(f"启用选项: {', '.join(enabled_options) if enabled_options else '无'}")

    if args.dump_mem:
        print(f"内存dump: {args.dump_mem}")
    if args.simarg:
        print(f"仿真参数: {args.simarg}")
    if args.post:
        print(f"后处理: {args.post}")

    print("=" * 60)
    print(f"开始执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    start_time = time.time()

    try:
        # 编译阶段
        if not args.R:  # 如果不是仅运行模式
            print(f"\n🔨 开始编译阶段...")
            compile_success = simulate_compilation(case_name, args)
            if not compile_success:
                print("❌ 编译失败，退出")
                return 1
            print("✅ 编译阶段完成")
        else:
            print("\n⏭️  跳过编译阶段 (仅运行模式)")

        # 仿真阶段
        if not args.C:  # 如果不是仅编译模式
            print(f"\n🚀 开始仿真阶段...")
            sim_success = simulate_simulation(case_name, args)
            if not sim_success:
                print("❌ 仿真失败")
                return 1
            print("✅ 仿真阶段完成")
        else:
            print("\n⏭️  跳过仿真阶段 (仅编译模式)")

        # 后处理阶段
        if args.post:
            print(f"\n🔄 开始后处理阶段...")
            print(f"执行后处理脚本: {args.post}")
            time.sleep(2)
            print("✅ 后处理完成")

        # 清理阶段
        if args.cl:
            print(f"\n🧹 开始清理阶段...")
            print("清理临时文件...")
            print("清理编译中间文件...")
            time.sleep(1)
            print("✅ 清理完成")

        end_time = time.time()
        total_time = end_time - start_time

        print("\n" + "=" * 60)
        print("🎉 RunSim 执行完成")
        print("=" * 60)
        print("执行总结:")
        print(f"  案例名称: {case_name}")
        print(f"  开始时间: {datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"  结束时间: {datetime.fromtimestamp(end_time).strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"  总耗时: {total_time:.1f} 秒")
        print(f"  执行状态: ✅ 成功")

        # 生成的文件信息
        print(f"\n生成的文件:")
        if args.fsdb:
            print(f"  📊 waves.fsdb")
        if args.vwdb:
            print(f"  📊 waves.vwdb")
        if args.cov:
            print(f"  📈 coverage.db")
        print(f"  📝 simulation.log")

        print("=" * 60)

        return 0

    except KeyboardInterrupt:
        print(f"\n\n⚠️  用户中断执行 (Ctrl+C)")
        print("清理资源...")
        time.sleep(0.5)
        print("退出程序")
        return 130
    except Exception as e:
        print(f"\n❌ 执行出错: {str(e)}")
        import traceback
        print("详细错误信息:")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
