"""
验证阶段数据分析器

该模块负责分析TestPlan中的验证阶段数据，包括：
- 按阶段统计用例分布
- 计算各阶段进度
- 生成阶段报告
"""

import logging
from datetime import datetime
from typing import Dict, List, Tuple, Optional

# 配置日志
logger = logging.getLogger(__name__)

class PhaseAnalyzer:
    """验证阶段数据分析器"""

    # 验证阶段定义
    PHASES = ['DVR1', 'DVR2', 'DVR3', 'DVS1', 'DVS2']

    # 用例类型定义
    CASE_TYPES = {
        'subsys': {'phase_col': 'subsys_phase', 'status_col': 'subsys_status'},
        'top': {'phase_col': 'top_phase', 'status_col': 'top_status'},
        'post_subsys': {'phase_col': 'post_subsys_phase', 'status_col': 'post_subsys_status'},
        'post_top': {'phase_col': 'post_top_phase', 'status_col': 'post_top_status'}
    }

    # 状态定义（符合TestPlan规范）
    STATUSES = ['PASS', 'Pending', 'On-Going', 'N/A']

    def __init__(self, db_connection):
        """
        初始化分析器

        Args:
            db_connection: 数据库连接
        """
        self.conn = db_connection

    def analyze_phase_distribution(self, project_id: Optional[int] = None) -> Dict:
        """
        分析验证阶段分布

        Args:
            project_id: 项目ID，None表示所有项目

        Returns:
            Dict: 阶段分布数据
        """
        try:
            cursor = self.conn.cursor()

            # 构建查询条件
            where_clause = "WHERE project_id = ?" if project_id else ""
            params = [project_id] if project_id else []

            phase_data = {}

            # 分析每个阶段的用例分布
            for phase in self.PHASES:
                phase_data[phase] = {}

                for case_type, cols in self.CASE_TYPES.items():
                    phase_col = cols['phase_col']
                    status_col = cols['status_col']

                    # 对于POST类型，只有DVS2阶段才统计POST用例
                    if case_type.startswith('post_'):
                        if phase == 'DVS2':
                            post_query = f'''
                                SELECT
                                    COUNT(CASE WHEN {phase_col} = '√' THEN 1 END) as total,
                                    SUM(CASE WHEN {status_col} = 'PASS' AND {phase_col} = '√' THEN 1 ELSE 0 END) as pass_count,
                                    SUM(CASE WHEN {status_col} = 'Pending' AND {phase_col} = '√' THEN 1 ELSE 0 END) as pending_count,
                                    SUM(CASE WHEN {status_col} = 'On-Going' AND {phase_col} = '√' THEN 1 ELSE 0 END) as ongoing_count,
                                    SUM(CASE WHEN {status_col} = 'N/A' AND {phase_col} = '√' THEN 1 ELSE 0 END) as na_count
                                FROM test_cases
                                {where_clause}
                            '''
                            cursor.execute(post_query, params)
                            result = cursor.fetchone()
                        else:
                            # 其他阶段不统计POST用例
                            phase_data[phase][case_type] = {
                                'total': 0,
                                'pass': 0,
                                'pending': 0,
                                'ongoing': 0,
                                'na': 0,
                                'fail': 0
                            }
                            continue
                    else:
                        # 对于普通类型，根据阶段字段筛选用例
                        # 注意：这里不应该统计所有用例，而应该根据阶段进行筛选
                        normal_query = f'''
                            SELECT
                                COUNT(CASE WHEN ({phase_col} = ? OR ({phase_col} IS NULL AND ? = 'DVR1'))
                                           AND {status_col} IS NOT NULL AND {status_col} != 'N/A' THEN 1 END) as total,
                                SUM(CASE WHEN ({phase_col} = ? OR ({phase_col} IS NULL AND ? = 'DVR1'))
                                         AND {status_col} = 'PASS' THEN 1 ELSE 0 END) as pass_count,
                                SUM(CASE WHEN ({phase_col} = ? OR ({phase_col} IS NULL AND ? = 'DVR1'))
                                         AND {status_col} = 'Pending' THEN 1 ELSE 0 END) as pending_count,
                                SUM(CASE WHEN ({phase_col} = ? OR ({phase_col} IS NULL AND ? = 'DVR1'))
                                         AND {status_col} = 'On-Going' THEN 1 ELSE 0 END) as ongoing_count,
                                SUM(CASE WHEN ({phase_col} = ? OR ({phase_col} IS NULL AND ? = 'DVR1'))
                                         AND {status_col} = 'N/A' THEN 1 ELSE 0 END) as na_count
                            FROM test_cases
                            {where_clause}
                        '''
                        # 为每个阶段条件添加参数
                        phase_params = params + [phase, phase, phase, phase, phase, phase, phase, phase, phase, phase]
                        cursor.execute(normal_query, phase_params)
                        result = cursor.fetchone()

                    phase_data[phase][case_type] = {
                        'total': result['total'] or 0,
                        'pass': result['pass_count'] or 0,
                        'pending': result['pending_count'] or 0,
                        'ongoing': result['ongoing_count'] or 0,
                        'na': result['na_count'] or 0,
                        'fail': 0  # 暂时设为0，因为当前数据库中没有Fail状态
                    }

            return phase_data

        except Exception as e:
            logger.error(f"分析验证阶段分布失败: {e}")
            return {}

    def calculate_phase_progress(self, project_id: Optional[int] = None) -> Dict:
        """
        计算各阶段进度

        Args:
            project_id: 项目ID

        Returns:
            Dict: 阶段进度数据
        """
        try:
            phase_data = self.analyze_phase_distribution(project_id)
            progress_data = {}

            for phase in self.PHASES:
                total_cases = 0
                completed_cases = 0

                for case_type in self.CASE_TYPES.keys():
                    if phase in phase_data and case_type in phase_data[phase]:
                        data = phase_data[phase][case_type]
                        # 只统计非N/A的用例，因为N/A表示该阶段不需要运行
                        valid_cases = data['total'] - data['na']
                        total_cases += valid_cases
                        completed_cases += data['pass']  # 只有PASS状态算作完成

                progress_percentage = (completed_cases / total_cases * 100) if total_cases > 0 else 0

                progress_data[phase] = {
                    'total_cases': total_cases,
                    'completed_cases': completed_cases,
                    'progress_percentage': round(progress_percentage, 2)
                }

            return progress_data

        except Exception as e:
            logger.error(f"计算阶段进度失败: {e}")
            return {}

    def get_phase_case_list(self, phase: str, case_type: str, status: Optional[str] = None,
                           project_id: Optional[int] = None) -> List[Dict]:
        """
        获取指定阶段和类型的用例列表

        Args:
            phase: 验证阶段
            case_type: 用例类型
            status: 状态筛选
            project_id: 项目ID

        Returns:
            List[Dict]: 用例列表
        """
        try:
            cursor = self.conn.cursor()

            if case_type not in self.CASE_TYPES:
                return []

            cols = self.CASE_TYPES[case_type]
            phase_col = cols['phase_col']
            status_col = cols['status_col']

            # 构建查询条件
            conditions = []
            params = []

            if project_id:
                conditions.append("project_id = ?")
                params.append(project_id)

            # 阶段条件
            if case_type.startswith('post_'):
                conditions.append(f"{phase_col} = '√'")
            else:
                conditions.append(f"({phase_col} = ? OR ({phase_col} IS NULL AND ? = 'DVR1'))")
                params.extend([phase, phase])

            # 状态条件
            if status:
                conditions.append(f"{status_col} = ?")
                params.append(status)

            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""

            query = f'''
                SELECT
                    id, case_name, category, test_areas, function_point,
                    {phase_col} as phase, {status_col} as status,
                    start_time, end_time, owner, remarks
                FROM test_cases
                {where_clause}
                ORDER BY case_name
            '''

            cursor.execute(query, params)
            return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            logger.error(f"获取阶段用例列表失败: {e}")
            return []

    def update_phase_statistics(self, project_id: Optional[int] = None):
        """
        更新验证阶段统计表

        Args:
            project_id: 项目ID
        """
        try:
            cursor = self.conn.cursor()
            phase_data = self.analyze_phase_distribution(project_id)

            # 清除旧统计数据
            if project_id:
                cursor.execute("DELETE FROM phase_statistics WHERE project_id = ?", (project_id,))
            else:
                cursor.execute("DELETE FROM phase_statistics")

            # 插入新统计数据
            for phase in self.PHASES:
                for case_type in self.CASE_TYPES.keys():
                    if phase in phase_data and case_type in phase_data[phase]:
                        data = phase_data[phase][case_type]

                        cursor.execute('''
                            INSERT OR REPLACE INTO phase_statistics
                            (project_id, phase_name, case_type, total_cases, pass_cases,
                             fail_cases, ongoing_cases, not_started_cases, updated_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            project_id, phase, case_type,
                            data['total'], data['pass'], data['fail'],
                            data['ongoing'], data['not_started'],
                            datetime.now()
                        ))

            self.conn.commit()
            logger.info(f"验证阶段统计更新完成，项目ID: {project_id}")

        except Exception as e:
            logger.error(f"更新验证阶段统计失败: {e}")
            self.conn.rollback()

    def generate_phase_report(self, project_id: Optional[int] = None) -> Dict:
        """
        生成验证阶段报告

        Args:
            project_id: 项目ID

        Returns:
            Dict: 阶段报告数据
        """
        try:
            phase_data = self.analyze_phase_distribution(project_id)
            progress_data = self.calculate_phase_progress(project_id)

            report = {
                'summary': {
                    'total_phases': len(self.PHASES),
                    'total_case_types': len(self.CASE_TYPES),
                    'generated_at': datetime.now().isoformat()
                },
                'phase_distribution': phase_data,
                'phase_progress': progress_data,
                'recommendations': self._generate_recommendations(phase_data, progress_data)
            }

            return report

        except Exception as e:
            logger.error(f"生成验证阶段报告失败: {e}")
            return {}

    def _generate_recommendations(self, phase_data: Dict, progress_data: Dict) -> List[str]:
        """
        生成改进建议

        Args:
            phase_data: 阶段分布数据
            progress_data: 进度数据

        Returns:
            List[str]: 建议列表
        """
        recommendations = []

        try:
            # 检查进度较慢的阶段
            for phase, progress in progress_data.items():
                if progress['progress_percentage'] < 50 and progress['total_cases'] > 0:
                    recommendations.append(f"{phase}阶段进度较慢({progress['progress_percentage']:.1f}%)，建议加强资源投入")

            # 检查失败率较高的阶段
            for phase in self.PHASES:
                if phase in phase_data:
                    total_fail = sum(data['fail'] for data in phase_data[phase].values())
                    total_cases = sum(data['total'] for data in phase_data[phase].values())
                    if total_cases > 0 and (total_fail / total_cases) > 0.1:
                        fail_rate = total_fail / total_cases * 100
                        recommendations.append(f"{phase}阶段失败率较高({fail_rate:.1f}%)，建议分析失败原因")

            # 检查POST用例覆盖率
            for phase in self.PHASES:
                if phase in phase_data:
                    post_total = phase_data[phase].get('post_subsys', {}).get('total', 0) + \
                                phase_data[phase].get('post_top', {}).get('total', 0)
                    normal_total = phase_data[phase].get('subsys', {}).get('total', 0) + \
                                  phase_data[phase].get('top', {}).get('total', 0)

                    if normal_total > 0 and post_total / normal_total < 0.3:
                        recommendations.append(f"{phase}阶段后仿用例覆盖率较低，建议增加后仿测试")

        except Exception as e:
            logger.error(f"生成建议失败: {e}")

        return recommendations
