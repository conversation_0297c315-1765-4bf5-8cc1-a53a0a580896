"""
主窗口视图组件
"""
import sys
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QHBoxLayout, QVBoxLayout,
    QStatusBar, QMenuBar, QMenu, QAction, QMessageBox,
    QTabWidget, QSplitter
)
from PyQt5.QtCore import Qt, pyqtSignal, QSize
from PyQt5.QtGui import QIcon, QFont

class MainWindow(QMainWindow):
    """主窗口类，提供应用程序的主框架"""

    # 定义信号
    save_config_requested = pyqtSignal()
    load_config_requested = pyqtSignal()
    clear_history_requested = pyqtSignal()

    def __init__(self):
        """初始化主窗口"""
        super().__init__()

        # 设置窗口属性
        self.setWindowTitle('runsim 控制台')
        self.setMinimumSize(1024, 768)

        # 设置默认窗口大小
        from PyQt5.QtWidgets import QApplication
        screen = QApplication.primaryScreen().geometry()
        default_width = min(1400, int(screen.width() * 0.8))
        default_height = min(900, int(screen.height() * 0.8))
        self.resize(default_width, default_height)

        # 创建中央部件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        # 创建主布局（水平布局）
        self.main_layout = QHBoxLayout(self.central_widget)
        self.main_layout.setSpacing(5)
        self.main_layout.setContentsMargins(5, 5, 5, 5)

        # 创建状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # 创建菜单栏
        self.create_menu()

        # 应用样式表
        self.init_styles()

        # 初始化插件窗口管理
        self.active_plugin_windows = []

    def init_styles(self):
        """初始化应用样式表，设置现代化GUI风格"""
        # 设置全局样式
        self.setStyleSheet("""
/* 主窗口背景 */
            QMainWindow {
                background-color: #f5f5f5;
            }

            /* 分组框样式 */
            QGroupBox {
font-family: "Microsoft YaHei";
                font-weight: bold;
                border: 2px solid #d0d0d0;
                border-radius: 6px;
                margin-top: 12px;
                padding: 10px;
                background-color: white;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                left: 10px;
                padding: 0 5px;
                color: #444;
            }

            /* 输入框样式 */
            QLineEdit {
                                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 5px;
                background: white;
                selection-background-color: #4a9eff;
                font-family: "Microsoft YaHei";
            }

            QLineEdit:focus {
                border: 2px solid #4a9eff;
            }

            /* 按钮样式 */
            QPushButton {
                background-color: #4a9eff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
font-family: "Microsoft YaHei";
                min-width: 80px;
            }

            QPushButton:hover {
                background-color: #3d8ced;
            }

            QPushButton:pressed {
                background-color: #3274bf;
            }

            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }

            /* 复选框样式 */
            QCheckBox {
                font-family: "Microsoft YaHei";
                spacing: 5px;
            }

            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border: 2px solid #ccc;
                border-radius: 4px;
            }

            QCheckBox::indicator:checked {
                background-color: #4a9eff;
                border-color: #4a9eff;
                image: url(resources/check.png);
            }

            /* 树形控件样式 */
            QTreeWidget {
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                background-color: white;
                font-family: "Microsoft YaHei";
            }

            QTreeWidget::item {
                height: 25px;
                color: #333;
            }

            QTreeWidget::item:selected {
                background-color: #e6f3ff;
                color: #000;
            }

            QTreeWidget::item:hover {
                background-color: #f5f5f5;
            }

            /* 标签页样式 */
            QTabWidget::pane {
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                top: -1px;
            }

            QTabBar::tab {
                font-family: "Microsoft YaHei";
                padding: 8px 15px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                border: 1px solid #d0d0d0;
                background-color: #f5f5f5;
            }

            QTabBar::tab:selected {
                background-color: white;
                border-bottom-color: white;
            }

            QTabBar::tab:hover:!selected {
                background-color: #e6f3ff;
            }

            /* 状态栏样式 */
            QStatusBar {
                background-color: #f8f9fa;
                color: #666;
                font-family: "Microsoft YaHei";
                padding: 2px;
                border-top: 1px solid #e4e4e4;
            }

            /* 菜单样式 */
            QMenuBar {
                background-color: #f8f9fa;
                border-bottom: 1px solid #e4e4e4;
            }

            QMenuBar::item {
                padding: 6px 10px;
                background-color: transparent;
            }

            QMenuBar::item:selected {
                background-color: #e6f3ff;
                border-radius: 4px;
            }

            QMenu {
                background-color: white;
                border: 1px solid #d0d0d0;
                padding: 5px;
            }

            QMenu::item {
                padding: 6px 25px 6px 20px;
                border-radius: 4px;
            }

            QMenu::item:selected {
                background-color: #e6f3ff;
                color: #000;
            }

            /* 滚动条样式 */
            QScrollBar:vertical {
                border: none;
                background: #f5f5f5;
                width: 10px;
                margin: 0;
            }

            QScrollBar::handle:vertical {
                background: #c1c1c1;
                min-height: 30px;
                border-radius: 5px;
            }

            QScrollBar::handle:vertical:hover {
                background: #a8a8a8;
            }

            /* 文本编辑器样式 */
            QTextEdit {
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                padding: 5px;
                font-family: "Microsoft YaHei";
                selection-background-color: #4a9eff;
            }
        """)

        # 设置应用程序字体
        app_font = QFont("Microsoft YaHei", 9)
        self.setFont(app_font)

    def create_menu(self):
        """创建菜单栏"""
        menu_bar = self.menuBar()

        # 文件菜单
        file_menu = QMenu("文件", self)
        save_action = file_menu.addAction("保存配置")
        save_action.triggered.connect(self.save_config_requested.emit)
        load_action = file_menu.addAction("加载配置")
        load_action.triggered.connect(self.load_config_requested.emit)
        file_menu.addSeparator()
        history_action = file_menu.addAction("清除历史")
        history_action.triggered.connect(self.clear_history_requested.emit)

        # 工具菜单
        self.tools_menu = QMenu("工具", self)

        # 添加菜单到菜单栏
        menu_bar.addMenu(file_menu)
        menu_bar.addMenu(self.tools_menu)

    def set_left_panel(self, panel):
        """设置左侧面板

        Args:
            panel (QWidget): 左侧面板
        """
        self.main_layout.addWidget(panel, stretch=2)

    def set_right_panel(self, panel):
        """设置右侧面板

        Args:
            panel (QWidget): 右侧面板
        """
        self.main_layout.addWidget(panel, stretch=3)

    def show_message(self, message, duration=3000):
        """在状态栏显示消息"""
        self.status_bar.showMessage(message, duration)

    def show_error(self, title, message):
        """显示错误对话框"""
        QMessageBox.critical(self, title, message)

    def show_warning(self, title, message):
        """显示警告对话框"""
        QMessageBox.warning(self, title, message)

    def show_info(self, title, message):
        """显示信息对话框"""
        QMessageBox.information(self, title, message)

    def show_question(self, title, message):
        """显示问题对话框"""
        return QMessageBox.question(
            self,
            title,
            message,
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

    def closeEvent(self, event):
        """关闭事件处理"""
        # 发出保存配置信号
        self.save_config_requested.emit()

        # 关闭所有活动的插件窗口
        for window in self.active_plugin_windows[:]:
            window.close()

        # 断开资源监控和性能监控的信号连接
        try:
            # 获取所有LogPanel实例
            from views.log_panel import LogPanel
            for child in self.findChildren(LogPanel):
                # 断开资源监控信号
                if hasattr(child, 'resource_monitor') and child.resource_monitor is not None:
                    try:
                        # 先停止监控
                        child.resource_monitor._running = False
                        # 断开信号连接
                        # 不尝试断开特定信号，而是使用无参数的disconnect()
                        # 这会断开所有连接到该对象的信号
                        try:
                            child.resource_monitor.resources_updated.disconnect()
                        except (TypeError, RuntimeError):
                            # 信号可能没有连接或已被销毁
                            pass
                    except Exception as e:
                        print(f"断开资源监控信号时出错: {str(e)}")

                # 断开性能监控信号
                if hasattr(child, 'performance_monitor') and child.performance_monitor is not None:
                    try:
                        # 先停止监控
                        child.performance_monitor._running = False
                        # 断开信号连接
                        # 不尝试断开特定信号，而是使用无参数的disconnect()
                        # 这会断开所有连接到该对象的信号
                        try:
                            child.performance_monitor.performance_alert.disconnect()
                        except (TypeError, RuntimeError):
                            # 信号可能没有连接或已被销毁
                            pass

                        try:
                            child.performance_monitor.metrics_updated.disconnect()
                        except (TypeError, RuntimeError):
                            # 信号可能没有连接或已被销毁
                            pass
                    except Exception as e:
                        print(f"断开性能监控信号时出错: {str(e)}")
        except Exception as e:
            print(f"清理资源监控和性能监控时出错: {str(e)}")

        super().closeEvent(event)

    def activateWindow(self):
        """激活窗口，确保主窗口显示在插件窗口之上"""
        super().activateWindow()
        self.raise_()
