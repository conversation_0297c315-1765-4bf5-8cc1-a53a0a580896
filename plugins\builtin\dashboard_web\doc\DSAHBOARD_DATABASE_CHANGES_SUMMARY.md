# RunSim Dashboard 数据库路径动态配置 - 修改总结

## 修改概述

本次修改解决了RunSim Dashboard在多用户环境下的权限问题，将数据库文件从固定路径 `plugins/builtin/dashboard_web/data` 改为动态路径配置，支持用户自定义数据库存储位置。

**重要修复**: 解决了工作目录切换导致的路径问题。现在即使插件内部切换了工作目录，数据库路径配置仍然能够正确使用用户的原始执行目录。

## 主要修改文件

### 1. 核心配置文件

#### `plugins/builtin/dashboard_web/config.py`
- **新增功能**:
  - `get_database_path()` 函数：动态获取数据库路径
  - `get_database_backup_dir()` 函数：获取备份目录
  - `ORIGINAL_WORK_DIR` 变量：保存用户的原始执行目录
- **修改内容**:
  - Config类的DATABASE_PATH使用动态路径
  - DevelopmentConfig支持动态路径配置
  - 支持环境变量、配置文件、原始执行目录等多种配置方式
  - 通过环境变量 `RUNSIM_ORIGINAL_CWD` 保持原始工作目录

#### `plugins/builtin/dashboard_web/app.py`
- **修改内容**:
  - 导入并使用动态数据库路径配置
  - 保持向后兼容性

### 2. 工具脚本更新

#### `plugins/builtin/dashboard_plugin.py`
- **修改内容**:
  - 在切换工作目录前保存原始工作目录到环境变量 `RUNSIM_ORIGINAL_CWD`
  - 确保配置模块能够访问用户的原始执行目录

#### `plugins/builtin/dashboard_web/sync_database.py`
- **修改内容**: 使用动态数据库路径，保持向后兼容

#### `plugins/builtin/dashboard_web/check_database.py`
- **修改内容**: 使用动态数据库路径，保持向后兼容

### 3. 新增文件

#### `plugins/builtin/dashboard_web/database_config.json`
- **功能**: 配置文件模板，支持用户自定义数据库路径

#### `plugins/builtin/dashboard_web/configure_database_path.py`
- **功能**: 交互式配置工具
- **特性**: 
  - 查看当前配置
  - 设置数据库路径（环境变量/配置文件）
  - 测试数据库访问
  - 用户友好的界面

#### `plugins/builtin/dashboard_web/test_database_path.py`
- **功能**: 自动化测试脚本
- **测试内容**:
  - 默认路径配置
  - 环境变量配置
  - 配置文件配置
  - 目录路径配置
  - 数据库初始化

#### `plugins/builtin/dashboard_web/example_usage.py`
- **功能**: 使用示例和最佳实践

#### `plugins/builtin/dashboard_web/DATABASE_PATH_GUIDE.md`
- **功能**: 详细的配置指南和故障排除

## 路径优先级

系统按以下优先级确定数据库文件路径：

1. **环境变量** `RUNSIM_DB_PATH` (最高优先级)
2. **配置文件** `database_config.json`
3. **原始执行目录** `runsim_dashboard.db` (用户执行脚本的目录)
4. **默认路径** `plugins/builtin/dashboard_web/data/dashboard.db` (向后兼容)

**重要**: 第3项使用的是用户执行脚本时的原始工作目录，而不是插件内部切换后的工作目录。

## 配置方法

### 方法1: 环境变量 (推荐)
```bash
# Windows
set RUNSIM_DB_PATH=C:\Users\<USER>\runsim_data\dashboard.db

# Linux/Mac
export RUNSIM_DB_PATH=/home/<USER>/runsim_data/dashboard.db
```

### 方法2: 配置文件
编辑 `database_config.json`:
```json
{
    "database": {
        "path": "/path/to/database.db"
    }
}
```

### 方法3: 使用配置工具
```bash
python configure_database_path.py
```

## 向后兼容性

- 现有的数据库文件路径仍然有效
- 如果没有配置自定义路径，系统会使用当前工作目录
- 所有现有功能保持不变

## 权限处理

- 自动检查目录写权限
- 如果当前目录没有写权限，回退到默认路径
- 支持权限检查的开关配置

## 测试验证

运行测试脚本验证功能：
```bash
cd plugins/builtin/dashboard_web
python test_database_path.py
```

所有测试应该通过，确保功能正常工作。

## 使用建议

### 单用户环境
使用默认配置即可，数据库会在当前工作目录下创建。

### 多用户环境
推荐每个用户设置个人数据库路径：
```bash
export RUNSIM_DB_PATH=$HOME/runsim_data/dashboard.db
```

### 共享环境
所有用户使用同一个共享数据库：
```bash
export RUNSIM_DB_PATH=/shared/runsim_data/dashboard.db
```

### 项目特定
每个项目使用独立数据库：
```bash
cd /path/to/project
export RUNSIM_DB_PATH=./project_dashboard.db
```

## 故障排除

1. **权限问题**: 确保目标目录有读写权限
2. **路径不存在**: 系统会自动创建目录
3. **配置不生效**: 重启应用程序，检查环境变量
4. **数据迁移**: 参考 DATABASE_PATH_GUIDE.md

## 技术细节

- 支持绝对路径和相对路径
- 自动处理路径分隔符差异
- 支持目录路径（自动添加文件名）
- 完整的错误处理和回退机制
- 详细的日志记录

## 影响范围

- ✅ 解决多用户权限问题
- ✅ 支持灵活的部署配置
- ✅ 保持向后兼容性
- ✅ 提供完整的工具支持
- ✅ 详细的文档和示例

## 后续维护

- 定期运行测试脚本确保功能正常
- 根据用户反馈优化配置体验
- 考虑添加更多配置选项（如数据库类型等）
