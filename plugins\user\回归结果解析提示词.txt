帮我开发一个回归结果解析插件，要求如下：
1. 回归完成后会在$PROJ_DIR/work/regression目录下产生回归结果log文件，其中regr_pass_list_regr_<时间戳>.lst存储所有pass case列表，regr_fail_list_regr_<时间戳>.lst存储着所有fail case列表；<时间戳>格式是年月日_时分秒，例如20250407_095825；
2. 回归结果列表内示例如下：
   // Pass caes regression lst for regr_20250407_095825
   // on/off,   block,   case,   seed,   iterative,   tag, priority,  config,  CFG_DEF, env/base,  plusarg,  sdf_corner

   //group:    apcpu_base_case
   //log：/proj/QogirS6/.../.../.../xxx.log
   ON, udtb/usvp, apcpu_hello_world_test, [128442343], 1, [PASS], H, default, [UPF_SIM,MEM_LOWPOWER], apcpu_sys, (EXEC_CPU=[0]), , ,
   //log：/proj/QogirS6/.../.../.../xxx.log
   ON, udtb/usvp, apcpu_sgd_test, [128442343], 1, [PASS], H, default, [UPF_SIM,MEM_LOWPOWER], apcpu_sys, (EXEC_CPU=[0]), , ,
   
   Group行描述了仿真case组，log行描述了仿真结果的LOG信息
   log下一行描述了回归配置格式，具体解释如下：
   ON：表示回归case开关，合法值是ON或OFF
   udtb/usvp：表示runsim的-block选项
   apcpu_hello_world_test：即runsim -case选项，即用例名
   [128442343]：即runsim -seed选项，中括号内部就是随机种子号
   1： 回归次数
   [PASS]：中括号内部字符串标记当前case仿真状态，一般仿真pass的tag为PASS，仿真fail的tag为NOT_RUN、RSP、RSF、RCP、RCF、BJF，含义分别为not submit job、real simulation pass、real simulation fail、real compile pass、real compile fail、bsub job fail；
   H：即优先级
   default：config文件名，一般是default，即使用默认使用dv/block_name/bin/block_name.cfg的user config file
   [UPF_SIM,MEM_LOWPOWER]：即-cfg_def选项，中括号内部描述，如果是default表示没有，如果有，可以是一个或多个
   apcpu_sys：即-base选项，如果为空，则表示执行指令没有-base
   (EXEC_CPU=[0])：即-simarg选项，如果为空，则表示执行指令没有-simarg选项，如果不为空，则括号内部可以翻译为：-simarg +EXEC_CPU=0，如果括号内部位多个则表示多个simarg参数
3. 解析回归目录下的所有pass和fail文件，时间戳为一个超链接，点击后展示该时间戳下的所有仿真case组，点击某个仿真case组展示该case组所有PASS和FAIL文件，以表格形式输出，要包括用例名、仿真状态、结果日志、仿真命令，其中点击结果日志可以直接通过gvim打开该日志文件，仿真命令通过解析log的下一行得到，并且点击后可以直接通过执行该命令
4. 表格支持排序，支持简单搜索功能；