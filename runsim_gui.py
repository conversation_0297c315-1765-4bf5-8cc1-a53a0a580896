#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RunSim GUI - 仿真运行控制台
这是重构后的入口点脚本，完全替代原有的runsim_gui.py
"""
import sys
import os
import traceback
from PyQt5.QtWidgets import QApplication, QMessageBox

def show_exception_box(exception_type, exception_value, exception_traceback):
    """显示异常对话框"""
    traceback_str = ''.join(traceback.format_exception(
        exception_type, exception_value, exception_traceback))

    msg_box = QMessageBox()
    msg_box.setIcon(QMessageBox.Critical)
    msg_box.setWindowTitle("应用程序错误")
    msg_box.setText("程序发生错误，请查看详细信息。")
    msg_box.setDetailedText(traceback_str)
    msg_box.setStandardButtons(QMessageBox.Ok)
    msg_box.exec_()

def main():
    """主函数"""
    # 创建 QApplication 实例
    app = QApplication(sys.argv)

    try:
        # 导入应用程序控制器
        from controllers.app_controller import AppController

        # 创建应用程序控制器
        app_controller = AppController()

        # 显示主窗口
        app_controller.show()

        # 运行应用程序事件循环
        sys.exit(app.exec_())
    except Exception as e:
        # 显示异常对话框
        show_exception_box(type(e), e, e.__traceback__)
        sys.exit(1)

if __name__ == "__main__":
    # 设置异常钩子
    sys.excepthook = show_exception_box
    main()
