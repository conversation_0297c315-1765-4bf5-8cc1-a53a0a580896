"""
虚拟滚动日志视图组件（第二阶段优化版本）
"""
from PyQt5.QtWidgets import (
    QAbstractScrollArea, QStyleOption, QStyle,
    QApplication, QScrollBar
)
from PyQt5.QtCore import Qt, QRect, QRectF, QSize, pyqtSignal, QTimer
from PyQt5.QtGui import QPainter, QColor, QTextOption, QFont, QFontMetrics
import time
import uuid
import threading
from collections import OrderedDict

class VirtualLogView(QAbstractScrollArea):
    """
    虚拟滚动日志视图，只渲染可见部分的日志行

    优化点：
    1. 只渲染可见区域的日志行，大幅减少渲染开销
    2. 使用行列表存储日志，避免大字符串拼接和处理
    3. 实现高效的行缓存机制，减少重复渲染
    4. 支持高效的日志追加和滚动操作
    """

    # 定义信号
    scrolled_to_bottom = pyqtSignal()  # 滚动到底部信号

    # 常量定义
    MAX_LINES = 100000  # 最大行数
    RENDER_MARGIN = 5   # 额外渲染的行数（上下各多渲染几行，避免滚动时出现空白）

    def __init__(self, parent=None):
        """初始化虚拟滚动日志视图（第二阶段优化版本）"""
        super().__init__(parent)

        # 日志数据
        self._lines = []  # 存储所有日志行
        self._line_heights = []  # 每行的高度
        self._total_height = 0  # 所有行的总高度

        # 渲染相关
        self._line_spacing = 2  # 行间距
        self._font = QFont("Consolas", 9)  # 默认字体
        self._font_metrics = QFontMetrics(self._font)
        self._default_line_height = self._font_metrics.height() + self._line_spacing

        # 颜色和样式
        self._background_color = QColor(255, 255, 255)  # 背景色
        self._text_color = QColor(51, 51, 51)  # 文本颜色
        self._selection_color = QColor(173, 214, 255)  # 选择颜色

        # 滚动相关
        self._auto_scroll = True  # 自动滚动标志
        self._scroll_timer = QTimer()
        self._scroll_timer.timeout.connect(self._check_auto_scroll)
        self._scroll_timer.start(100)  # 每100ms检查一次是否需要自动滚动

        # 第二阶段优化：缓存机制
        self._render_cache = OrderedDict()  # 渲染缓存
        self._cache_max_size = 50  # 最大缓存项数
        self._cache_lock = threading.Lock()  # 缓存锁

        # 第二阶段优化：批处理
        self._pending_lines = []  # 待处理的行
        self._batch_timer = QTimer()
        self._batch_timer.timeout.connect(self._process_batch)
        self._batch_timer.setSingleShot(True)
        self._batch_size = 100  # 批处理大小
        self._batch_timeout = 50  # 批处理超时（毫秒）

        # 第二阶段优化：性能统计
        self._render_stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'total_renders': 0,
            'avg_render_time': 0.0
        }

        # 初始化滚动条
        self.verticalScrollBar().setRange(0, 0)
        self.verticalScrollBar().valueChanged.connect(self._handle_scroll)

        # 设置视口属性
        self.viewport().setAutoFillBackground(False)

        # 设置控件属性
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOn)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.setMinimumHeight(100)

    def append(self, text):
        """
        追加文本到日志视图（第二阶段优化：使用批处理）

        Args:
            text (str): 要追加的文本
        """
        # 如果文本为空，直接返回
        if not text:
            return

        try:
            # 分割多行文本
            new_lines = text.split('\n')

            # 过滤掉空行，避免不必要的处理
            new_lines = [line for line in new_lines if line.strip()]

            # 如果没有有效行，直接返回
            if not new_lines:
                return

            # 添加到待处理队列
            self._pending_lines.extend(new_lines)

            # 如果达到批处理大小或者是第一批数据，立即处理
            if len(self._pending_lines) >= self._batch_size or not self._lines:
                self._process_batch()
            else:
                # 否则启动批处理定时器
                if not self._batch_timer.isActive():
                    self._batch_timer.start(self._batch_timeout)

        except Exception as e:
            print(f"追加日志时出错: {str(e)}")

    def _process_batch(self):
        """处理批量日志数据"""
        if not self._pending_lines:
            return

        # 停止批处理定时器
        self._batch_timer.stop()

        # 记录开始时间（用于性能监控）
        start_time = time.time()

        try:
            # 获取待处理的行
            lines_to_process = self._pending_lines[:]
            self._pending_lines.clear()

            line_count = len(lines_to_process)

            # 批量处理行，而不是逐行处理
            # 预先计算所有行高
            line_heights = [self._default_line_height] * line_count  # 使用默认行高
            total_height_to_add = sum(line_heights)

            # 批量添加到列表
            self._lines.extend(lines_to_process)
            self._line_heights.extend(line_heights)
            self._total_height += total_height_to_add

            # 如果超过最大行数，移除旧行
            if len(self._lines) > self.MAX_LINES:
                lines_to_remove = len(self._lines) - self.MAX_LINES
                height_to_remove = sum(self._line_heights[:lines_to_remove])

                self._lines = self._lines[lines_to_remove:]
                self._line_heights = self._line_heights[lines_to_remove:]
                self._total_height -= height_to_remove

                # 清空缓存，因为行索引已经改变
                self._clear_render_cache()

            # 更新滚动条范围
            self._update_scrollbar()

            # 检查是否在底部
            was_at_bottom = self.verticalScrollBar().value() >= self.verticalScrollBar().maximum() - 10

            # 如果之前在底部或者自动滚动已启用，则滚动到底部
            if was_at_bottom or self._auto_scroll:
                self._auto_scroll = True
                # 延迟滚动到底部，避免频繁更新
                QTimer.singleShot(0, self._scroll_to_bottom)

            # 更新视图 - 使用延迟更新减少重绘次数
            self.viewport().update()

            # 记录处理时间（用于性能监控）
            process_time = (time.time() - start_time) * 1000.0  # 转换为毫秒

            # 如果处理时间过长，输出警告
            if process_time > 100.0:  # 提高阈值到100ms
                print(f"警告: 虚拟日志视图批处理时间过长: {process_time:.1f}ms, 行数: {line_count}")

            # 使用弱引用访问性能监控器，避免引用已删除对象
            try:
                parent = self.parent()
                if parent and hasattr(parent, 'performance_monitor') and parent.performance_monitor:
                    parent.performance_monitor.record_log_processing(line_count)
            except Exception:
                # 忽略性能监控器可能已被删除的错误
                pass

        except Exception as e:
            print(f"批处理日志时出错: {str(e)}")

    def clear(self):
        """清空日志视图"""
        self._lines = []
        self._line_heights = []
        self._total_height = 0
        self._pending_lines.clear()
        self._clear_render_cache()
        self._update_scrollbar()
        self.viewport().update()

    def _clear_render_cache(self):
        """清空渲染缓存"""
        with self._cache_lock:
            self._render_cache.clear()

    def _get_cache_key(self, start_line, end_line, scroll_value):
        """生成缓存键"""
        return f"{start_line}_{end_line}_{scroll_value}"

    def _get_cached_render_data(self, cache_key):
        """获取缓存的渲染数据"""
        with self._cache_lock:
            if cache_key in self._render_cache:
                # 移动到末尾（LRU）
                self._render_cache.move_to_end(cache_key)
                self._render_stats['cache_hits'] += 1
                return self._render_cache[cache_key]

            self._render_stats['cache_misses'] += 1
            return None

    def _cache_render_data(self, cache_key, render_data):
        """缓存渲染数据"""
        with self._cache_lock:
            # 如果缓存已满，移除最旧的项
            if len(self._render_cache) >= self._cache_max_size:
                self._render_cache.popitem(last=False)

            self._render_cache[cache_key] = render_data

    def get_cache_stats(self):
        """获取缓存统计信息"""
        total_requests = self._render_stats['cache_hits'] + self._render_stats['cache_misses']
        hit_rate = (self._render_stats['cache_hits'] / total_requests * 100) if total_requests > 0 else 0

        return {
            'cache_size': len(self._render_cache),
            'cache_hits': self._render_stats['cache_hits'],
            'cache_misses': self._render_stats['cache_misses'],
            'hit_rate': hit_rate,
            'total_renders': self._render_stats['total_renders'],
            'avg_render_time': self._render_stats['avg_render_time']
        }

    def setText(self, text):
        """
        设置日志视图的文本内容

        Args:
            text (str): 要设置的文本
        """
        self.clear()
        self.append(text)

    def text(self):
        """
        获取日志视图的文本内容

        Returns:
            str: 日志视图的文本内容
        """
        return '\n'.join(self._lines)

    def setFont(self, font):
        """
        设置日志视图的字体

        Args:
            font (QFont): 要设置的字体
        """
        self._font = font
        self._font_metrics = QFontMetrics(self._font)
        self._default_line_height = self._font_metrics.height() + self._line_spacing

        # 重新计算所有行的高度
        self._recalculate_line_heights()
        self._update_scrollbar()
        self.viewport().update()

    def setAutoScroll(self, enabled):
        """
        设置是否启用自动滚动

        Args:
            enabled (bool): 是否启用自动滚动
        """
        self._auto_scroll = enabled
        if enabled:
            self._scroll_to_bottom()

    def paintEvent(self, event):
        """
        绘制事件处理（第二阶段优化：使用缓存）

        Args:
            event (QPaintEvent): 绘制事件
        """
        try:
            # 记录渲染开始时间（用于性能监控）
            render_start_time = time.time()
            self._render_stats['total_renders'] += 1

            painter = QPainter(self.viewport())
            painter.setFont(self._font)

            # 绘制背景
            painter.fillRect(event.rect(), self._background_color)

            # 获取可见区域
            visible_rect = self.viewport().rect()
            scroll_value = self.verticalScrollBar().value()

            # 计算可见行的范围
            visible_lines = self._get_visible_lines(scroll_value, visible_rect.height())
            if not visible_lines:
                painter.end()
                return

            start_line, end_line, y_offset = visible_lines

            # 生成缓存键
            cache_key = self._get_cache_key(start_line, end_line, scroll_value // 10)  # 降低缓存精度

            # 尝试从缓存获取渲染数据
            cached_data = self._get_cached_render_data(cache_key)

            if cached_data and len(self._lines) == cached_data['total_lines']:
                # 使用缓存数据快速渲染
                self._render_from_cache(painter, cached_data, visible_rect)
            else:
                # 重新渲染并缓存
                render_data = self._render_and_cache(painter, start_line, end_line, y_offset, visible_rect, cache_key)

            # 确保绘制器正确结束
            painter.end()

            # 记录渲染结束时间（用于性能监控）
            render_time = (time.time() - render_start_time) * 1000.0  # 转换为毫秒

            # 更新平均渲染时间
            self._render_stats['avg_render_time'] = (
                (self._render_stats['avg_render_time'] * (self._render_stats['total_renders'] - 1) + render_time) /
                self._render_stats['total_renders']
            )

            # 如果渲染时间过长，输出警告
            if render_time > 50.0:  # 50ms
                print(f"警告: 虚拟日志视图渲染时间过长: {render_time:.1f}ms, 行数: {end_line - start_line}")

        except Exception as e:
            print(f"绘制日志视图时出错: {str(e)}")
            # 确保在异常情况下也结束绘制器
            if 'painter' in locals() and painter.isActive():
                painter.end()

    def _render_from_cache(self, painter, cached_data, visible_rect):
        """从缓存数据渲染"""
        painter.setPen(self._text_color)

        for line_data in cached_data['lines']:
            text_rect = QRectF(5, line_data['y'], visible_rect.width() - 10, line_data['height'])
            text_option = QTextOption()
            text_option.setWrapMode(QTextOption.WrapAtWordBoundaryOrAnywhere)
            painter.drawText(text_rect, line_data['text'], text_option)

    def _render_and_cache(self, painter, start_line, end_line, y_offset, visible_rect, cache_key):
        """渲染并缓存数据"""
        render_data = {
            'lines': [],
            'total_lines': len(self._lines)
        }

        painter.setPen(self._text_color)
        y = y_offset

        for i in range(start_line, end_line):
            if i >= len(self._lines):
                break

            line = self._lines[i]
            line_height = self._line_heights[i]

            # 绘制文本
            text_rect = QRectF(5, y, visible_rect.width() - 10, line_height)
            text_option = QTextOption()
            text_option.setWrapMode(QTextOption.WrapAtWordBoundaryOrAnywhere)
            painter.drawText(text_rect, line, text_option)

            # 缓存行数据
            render_data['lines'].append({
                'text': line,
                'y': y,
                'height': line_height
            })

            y += line_height

        # 缓存渲染数据
        self._cache_render_data(cache_key, render_data)

        return render_data

    def resizeEvent(self, event):
        """
        调整大小事件处理

        Args:
            event (QResizeEvent): 调整大小事件
        """
        super().resizeEvent(event)
        self._update_scrollbar()

        # 如果启用了自动滚动，滚动到底部
        if self._auto_scroll:
            self._scroll_to_bottom()

    def _calculate_line_height(self, line):
        """
        计算行高

        Args:
            line (str): 行文本

        Returns:
            int: 行高（像素）
        """
        # 简单实现：固定行高
        # 可以扩展为根据文本内容计算实际行高（如处理换行）
        return self._default_line_height

    def _recalculate_line_heights(self):
        """重新计算所有行的高度"""
        self._line_heights = []
        self._total_height = 0

        for line in self._lines:
            line_height = self._calculate_line_height(line)
            self._line_heights.append(line_height)
            self._total_height += line_height

    def _update_scrollbar(self):
        """更新滚动条范围"""
        viewport_height = self.viewport().height()

        # 设置滚动条范围
        max_value = max(0, self._total_height - viewport_height)
        self.verticalScrollBar().setRange(0, max_value)
        self.verticalScrollBar().setPageStep(viewport_height)

    def _get_visible_lines(self, scroll_value, viewport_height):
        """
        获取可见行的范围

        Args:
            scroll_value (int): 滚动条位置
            viewport_height (int): 视口高度

        Returns:
            tuple: (起始行索引, 结束行索引, 起始Y偏移)
        """
        if not self._lines:
            return None

        # 查找起始行
        start_line = 0
        y = 0
        for i, height in enumerate(self._line_heights):
            if y + height > scroll_value:
                start_line = i
                break
            y += height

        # 计算起始行的Y偏移
        y_offset = y - scroll_value

        # 查找结束行（加上额外的渲染行数）
        end_line = start_line
        y = y_offset
        while end_line < len(self._lines) and y < viewport_height + self.RENDER_MARGIN * self._default_line_height:
            y += self._line_heights[end_line]
            end_line += 1

        # 加上额外的渲染行数
        end_line = min(len(self._lines), end_line + self.RENDER_MARGIN)

        return (max(0, start_line - self.RENDER_MARGIN), end_line, y_offset)

    def _handle_scroll(self, value):
        """
        处理滚动事件

        Args:
            value (int): 滚动条位置
        """
        # 检查是否滚动到底部
        if value >= self.verticalScrollBar().maximum():
            self._auto_scroll = True
            self.scrolled_to_bottom.emit()
        else:
            self._auto_scroll = False

        # 更新视图
        self.viewport().update()

    def _scroll_to_bottom(self):
        """滚动到底部"""
        # 获取最大值
        max_value = self.verticalScrollBar().maximum()

        # 设置滚动条值
        self.verticalScrollBar().setValue(max_value)

        # 强制更新视图
        self.viewport().update()

        # 确保处理事件，使滚动立即生效
        QApplication.processEvents()

    def _check_auto_scroll(self):
        """检查是否需要自动滚动"""
        try:
            if self._auto_scroll:
                # 直接设置滚动条值，避免递归调用
                max_value = self.verticalScrollBar().maximum()
                current_value = self.verticalScrollBar().value()

                # 只有当当前值不是最大值时才设置，避免不必要的UI更新
                if current_value != max_value:
                    self.verticalScrollBar().setValue(max_value)
                    # 强制更新视图
                    self.viewport().update()
        except Exception as e:
            # 避免异常导致递归错误
            print(f"自动滚动检查出错: {str(e)}")
