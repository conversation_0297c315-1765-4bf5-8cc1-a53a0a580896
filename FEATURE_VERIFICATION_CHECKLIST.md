# RunSim GUI仪表盘自动状态更新功能验证清单

## 功能验证清单

### ✅ 1. 仿真执行时间记录功能

**验证步骤：**
- [ ] 在RunSim GUI中选择一个测试用例
- [ ] 点击"执行仿真和编译"按钮
- [ ] 检查仪表盘数据库中对应用例的Start Time字段（I列）是否已更新
- [ ] 检查用例状态是否更新为"On-Going"

**预期结果：**
- 开始时间自动记录为当前时间戳
- 状态自动更新为"On-Going"
- 根据命令参数更新正确的状态列

### ✅ 2. 仿真结果自动检测功能

**验证步骤：**
- [ ] 等待仿真执行完成
- [ ] 检查用例目录下的`irun_sim.log`文件
- [ ] 验证日志文件最后50行是否包含"SPRD_PASSED"字符串
- [ ] 检查仪表盘数据库中的状态和时间更新

**预期结果：**
- 如果找到"SPRD_PASSED"：状态更新为"PASS"，记录End Time
- 如果未找到"SPRD_PASSED"：状态更新为"FAIL"，不记录结束时间

### ✅ 3. 状态列映射规则验证

**测试用例：**

**3.1 Subsys级用例**
- 命令：`runsim -base subsys -block dv/test -case test_subsys`
- 预期：更新N列（subsys_status）

**3.2 TOP级用例**
- 命令：`runsim -base top -block udtb/usvp -case test_top`
- 预期：更新P列（top_status）

**3.3 Subsys级后仿用例**
- 命令：`runsim -base subsys -block dv/test -case test_post_subsys -post post_sim`
- 预期：更新R列（post_subsys_status）

**3.4 TOP级后仿用例**
- 命令：`runsim -base top -case test_post_top -post post_analysis`
- 预期：更新T列（post_top_status）

### ✅ 4. 用例通过率统计图表功能

**4.1 API功能验证**
- [ ] 访问`/api/dashboard/case_pass_rate?unit=day&days=30`
- [ ] 验证返回数据包含：labels, pass_count, total_count, pass_rate, cumulative
- [ ] 测试按周统计：`/api/dashboard/case_pass_rate?unit=week&days=30`

**4.2 前端图表验证**
- [ ] 访问仪表盘页面，检查用例通过率统计图表是否显示
- [ ] 测试时间单位切换（按天/按周）
- [ ] 测试显示模式切换（数量/通过率）
- [ ] 验证累计统计信息显示

**4.3 实时更新验证**
- [ ] 执行新的仿真用例
- [ ] 检查图表数据是否自动更新
- [ ] 验证累计统计是否正确计算

### ✅ 5. 系统集成验证

**5.1 执行控制器集成**
- [ ] 验证仿真监听器已正确集成到执行控制器
- [ ] 检查执行事件是否正确触发状态更新
- [ ] 验证日志面板执行完成信号连接

**5.2 仪表盘API集成**
- [ ] 测试`update_from_runsim` API端点
- [ ] 验证API和直接数据库更新的备用机制
- [ ] 检查错误处理和重试逻辑

**5.3 数据一致性验证**
- [ ] 同时执行多个用例，验证状态更新不冲突
- [ ] 检查数据库事务的原子性
- [ ] 验证时间戳格式和时区处理

### ✅ 6. 性能和稳定性验证

**6.1 性能测试**
- [ ] 测试大量用例同时执行时的性能表现
- [ ] 验证日志监控的资源消耗
- [ ] 检查前端图表渲染性能

**6.2 错误处理验证**
- [ ] 测试仪表盘服务不可用时的降级处理
- [ ] 验证日志文件不存在时的错误处理
- [ ] 检查网络异常时的重试机制

**6.3 边界条件测试**
- [ ] 测试超长运行时间的仿真监控
- [ ] 验证大文件日志的读取性能
- [ ] 检查异常退出情况的处理

### ✅ 7. 用户界面验证

**7.1 仪表盘界面**
- [ ] 验证用例通过率图表的交互性
- [ ] 检查图表切换按钮的响应性
- [ ] 测试图表数据的工具提示显示

**7.2 响应式设计**
- [ ] 测试不同屏幕尺寸下的图表显示
- [ ] 验证移动设备上的用户体验
- [ ] 检查图表的自适应布局

### ✅ 8. 文档和配置验证

**8.1 配置文件**
- [ ] 验证环境变量配置（PROJ_DIR等）
- [ ] 检查数据库路径配置
- [ ] 测试API端口配置

**8.2 文档完整性**
- [ ] 验证使用指南的准确性
- [ ] 检查API文档的完整性
- [ ] 测试故障排除指南的有效性

## 验证结果记录

### 测试环境信息
- 操作系统：Windows/Linux
- Python版本：
- PyQt5版本：
- 数据库版本：
- 浏览器版本：

### 测试执行记录

| 功能模块 | 测试项目 | 执行状态 | 结果 | 备注 |
|---------|---------|---------|------|------|
| 时间记录 | 开始时间记录 | ⏳ | | |
| 时间记录 | 状态更新为On-Going | ⏳ | | |
| 结果检测 | SPRD_PASSED检测 | ⏳ | | |
| 结果检测 | 结束时间记录 | ⏳ | | |
| 状态映射 | Subsys级用例 | ⏳ | | |
| 状态映射 | TOP级用例 | ⏳ | | |
| 状态映射 | 后仿用例 | ⏳ | | |
| 统计图表 | API功能 | ⏳ | | |
| 统计图表 | 前端显示 | ⏳ | | |
| 统计图表 | 实时更新 | ⏳ | | |
| 系统集成 | 执行控制器 | ⏳ | | |
| 系统集成 | API集成 | ⏳ | | |
| 性能测试 | 并发执行 | ⏳ | | |
| 性能测试 | 错误处理 | ⏳ | | |
| 界面验证 | 图表交互 | ⏳ | | |
| 界面验证 | 响应式设计 | ⏳ | | |

### 问题记录

| 问题ID | 问题描述 | 严重程度 | 状态 | 解决方案 |
|-------|---------|---------|------|---------|
| | | | | |

### 验证结论

- [ ] 所有核心功能正常工作
- [ ] 性能满足要求
- [ ] 用户界面友好
- [ ] 文档完整准确
- [ ] 系统稳定可靠

**总体评估：** ⏳ 待验证

**验证人员：** 
**验证日期：** 
**版本号：** v1.0
