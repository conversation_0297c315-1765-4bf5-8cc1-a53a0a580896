/**
 * 实时数据更新管理器
 *
 * 提供智能的数据刷新策略和WebSocket连接管理
 */

class RealtimeManager {
    constructor() {
        this.refreshInterval = null;
        this.websocket = null;
        this.isConnected = false;
        this.retryCount = 0;
        this.maxRetries = 5;
        this.baseRefreshRate = 30000; // 30秒基础刷新率
        this.currentRefreshRate = this.baseRefreshRate;
        this.lastUpdateTime = Date.now();
        this.dataChangeCallbacks = new Map();
        this.connectionStatusCallbacks = [];

        // 页面可见性状态
        this.isPageVisible = !document.hidden;
        this.setupVisibilityHandling();

        // 网络状态监听
        this.setupNetworkHandling();
    }

    /**
     * 启动实时更新
     */
    start() {
        console.log('启动实时数据更新管理器');

        // 尝试建立WebSocket连接
        this.connectWebSocket();

        // 启动定时刷新作为备用
        this.startPolling();

        // 立即加载一次数据
        this.triggerDataRefresh();
    }

    /**
     * 停止实时更新
     */
    stop() {
        console.log('停止实时数据更新管理器');

        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }

        if (this.websocket) {
            this.websocket.close();
            this.websocket = null;
        }

        this.isConnected = false;
        this.notifyConnectionStatus(false);
    }

    /**
     * 建立WebSocket连接
     */
    connectWebSocket() {
        // 暂时禁用WebSocket，因为当前Flask应用不支持WebSocket
        console.log('WebSocket功能暂时禁用，使用轮询模式');
        this.isConnected = false;
        this.notifyConnectionStatus(false);
        return;

        try {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/dashboard`;

            this.websocket = new WebSocket(wsUrl);

            this.websocket.onopen = () => {
                console.log('WebSocket连接已建立');
                this.isConnected = true;
                this.retryCount = 0;
                this.notifyConnectionStatus(true);

                // WebSocket连接成功，可以降低轮询频率
                this.adjustRefreshRate(60000); // 1分钟
            };

            this.websocket.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleWebSocketMessage(data);
                } catch (error) {
                    console.error('WebSocket消息解析失败:', error);
                }
            };

            this.websocket.onclose = () => {
                console.log('WebSocket连接已关闭');
                this.isConnected = false;
                this.notifyConnectionStatus(false);

                // 连接断开，恢复正常轮询频率
                this.adjustRefreshRate(this.baseRefreshRate);

                // 尝试重连
                this.scheduleReconnect();
            };

            this.websocket.onerror = (error) => {
                console.error('WebSocket连接错误:', error);
                this.isConnected = false;
                this.notifyConnectionStatus(false);
            };

        } catch (error) {
            console.warn('WebSocket不可用，使用轮询模式:', error);
            this.adjustRefreshRate(this.baseRefreshRate);
        }
    }

    /**
     * 处理WebSocket消息
     */
    handleWebSocketMessage(data) {
        console.log('收到WebSocket消息:', data);

        switch (data.type) {
            case 'data_update':
                this.handleDataUpdate(data.payload);
                break;
            case 'heartbeat':
                // 心跳响应，更新连接状态
                this.lastUpdateTime = Date.now();
                break;
            default:
                console.warn('未知的WebSocket消息类型:', data.type);
        }
    }

    /**
     * 处理数据更新
     */
    handleDataUpdate(payload) {
        const { dataType, data } = payload;

        // 触发对应的回调函数
        if (this.dataChangeCallbacks.has(dataType)) {
            const callbacks = this.dataChangeCallbacks.get(dataType);
            callbacks.forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`数据更新回调执行失败 (${dataType}):`, error);
                }
            });
        }

        // 更新最后更新时间
        this.lastUpdateTime = Date.now();
    }

    /**
     * 启动轮询
     */
    startPolling() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }

        this.refreshInterval = setInterval(() => {
            if (this.isPageVisible && !this.isConnected) {
                this.triggerDataRefresh();
            }
        }, this.currentRefreshRate);
    }

    /**
     * 触发数据刷新
     */
    triggerDataRefresh() {
        if (!this.isPageVisible) {
            console.log('页面不可见，跳过数据刷新');
            return;
        }

        console.log('触发数据刷新');

        // 触发全局数据刷新事件
        const event = new CustomEvent('dashboard:refresh', {
            detail: { timestamp: Date.now() }
        });
        document.dispatchEvent(event);
    }

    /**
     * 调整刷新频率
     */
    adjustRefreshRate(newRate) {
        if (this.currentRefreshRate !== newRate) {
            console.log(`调整刷新频率: ${this.currentRefreshRate}ms -> ${newRate}ms`);
            this.currentRefreshRate = newRate;
            this.startPolling(); // 重启轮询
        }
    }

    /**
     * 智能调整刷新频率
     */
    smartAdjustRefreshRate() {
        const timeSinceLastUpdate = Date.now() - this.lastUpdateTime;

        // 如果长时间没有更新，降低刷新频率
        if (timeSinceLastUpdate > 300000) { // 5分钟
            this.adjustRefreshRate(120000); // 2分钟
        } else if (timeSinceLastUpdate > 120000) { // 2分钟
            this.adjustRefreshRate(60000); // 1分钟
        } else {
            this.adjustRefreshRate(this.baseRefreshRate); // 30秒
        }
    }

    /**
     * 安排重连
     */
    scheduleReconnect() {
        if (this.retryCount >= this.maxRetries) {
            console.warn('WebSocket重连次数已达上限，停止重连');
            return;
        }

        const delay = Math.min(1000 * Math.pow(2, this.retryCount), 30000); // 指数退避，最大30秒
        this.retryCount++;

        console.log(`${delay}ms后尝试WebSocket重连 (第${this.retryCount}次)`);

        setTimeout(() => {
            if (!this.isConnected && this.websocket?.readyState !== WebSocket.CONNECTING) {
                this.connectWebSocket();
            }
        }, delay);
    }

    /**
     * 设置页面可见性处理
     */
    setupVisibilityHandling() {
        document.addEventListener('visibilitychange', () => {
            this.isPageVisible = !document.hidden;

            if (this.isPageVisible) {
                console.log('页面变为可见，恢复数据更新');
                this.triggerDataRefresh();
                this.startPolling();
            } else {
                console.log('页面变为不可见，暂停数据更新');
                if (this.refreshInterval) {
                    clearInterval(this.refreshInterval);
                    this.refreshInterval = null;
                }
            }
        });
    }

    /**
     * 设置网络状态处理
     */
    setupNetworkHandling() {
        window.addEventListener('online', () => {
            console.log('网络连接恢复');
            this.triggerDataRefresh();
            if (!this.isConnected) {
                this.connectWebSocket();
            }
        });

        window.addEventListener('offline', () => {
            console.log('网络连接断开');
            this.notifyConnectionStatus(false);
        });
    }

    /**
     * 注册数据变化回调
     */
    onDataChange(dataType, callback) {
        if (!this.dataChangeCallbacks.has(dataType)) {
            this.dataChangeCallbacks.set(dataType, []);
        }
        this.dataChangeCallbacks.get(dataType).push(callback);
    }

    /**
     * 注册连接状态回调
     */
    onConnectionStatusChange(callback) {
        this.connectionStatusCallbacks.push(callback);
    }

    /**
     * 通知连接状态变化
     */
    notifyConnectionStatus(isConnected) {
        this.connectionStatusCallbacks.forEach(callback => {
            try {
                callback(isConnected);
            } catch (error) {
                console.error('连接状态回调执行失败:', error);
            }
        });
    }

    /**
     * 获取连接状态
     */
    getConnectionStatus() {
        return {
            isConnected: this.isConnected,
            isWebSocketSupported: !!window.WebSocket,
            currentRefreshRate: this.currentRefreshRate,
            lastUpdateTime: this.lastUpdateTime,
            retryCount: this.retryCount
        };
    }

    /**
     * 手动触发数据刷新
     */
    forceRefresh() {
        console.log('手动触发数据刷新');
        this.triggerDataRefresh();

        // 重置刷新频率
        this.adjustRefreshRate(this.baseRefreshRate);
        this.lastUpdateTime = Date.now();
    }
}

// 创建全局实例
window.realtimeManager = new RealtimeManager();

// 页面加载完成后自动启动
document.addEventListener('DOMContentLoaded', () => {
    window.realtimeManager.start();
});

// 页面卸载前清理
window.addEventListener('beforeunload', () => {
    window.realtimeManager.stop();
});
