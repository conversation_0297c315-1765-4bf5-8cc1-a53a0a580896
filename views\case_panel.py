"""
用例管理面板视图组件
"""
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
    QTreeWidget, QTreeWidgetItem, QLineEdit, QPushButton,
    QLabel, QMenu, QFileDialog, QMessageBox, QDialog
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QIcon

class CasePanel(QGroupBox):
    """用例管理面板，用于管理和显示用例"""

    # 定义信号
    load_case_file_requested = pyqtSignal(str)
    remove_case_file_requested = pyqtSignal(str)
    parse_case_files_requested = pyqtSignal()
    case_selected = pyqtSignal(str)
    show_env_parse_dialog_requested = pyqtSignal()
    parse_params_requested = pyqtSignal(list)  # 新增：请求解析参数信号

    def __init__(self, parent=None):
        """初始化用例管理面板"""
        super().__init__("用例管理", parent)
        self.init_ui()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        layout.setSpacing(10)
        layout.setContentsMargins(10, 15, 10, 10)

        # 文件加载区域
        file_layout = QHBoxLayout()
        file_layout.setSpacing(8)

        # 增加环境解析按钮
        self.parse_env_btn = QPushButton("解析环境")
        self.parse_env_btn.setIcon(QIcon.fromTheme("system-search", QIcon()))
        self.parse_env_btn.clicked.connect(self.show_env_parse_dialog_requested.emit)

        # 加载用例文件按钮
        self.case_file_btn = QPushButton("加载用例文件")
        self.case_file_btn.setIcon(QIcon.fromTheme("document-open", QIcon()))
        self.case_file_btn.clicked.connect(self.load_case_file)

        # 删除用例文件按钮
        self.remove_case_file_btn = QPushButton("删除用例文件")
        self.remove_case_file_btn.setIcon(QIcon.fromTheme("edit-delete", QIcon()))
        self.remove_case_file_btn.clicked.connect(self.remove_case_file)

        # 添加刷新按钮
        self.refresh_btn = QPushButton("刷新用例")
        self.refresh_btn.setIcon(QIcon.fromTheme("view-refresh", QIcon()))
        self.refresh_btn.clicked.connect(self.parse_case_files_requested.emit)

        file_layout.addWidget(self.parse_env_btn)
        file_layout.addWidget(self.case_file_btn)
        file_layout.addWidget(self.remove_case_file_btn)
        file_layout.addWidget(self.refresh_btn)

        # 搜索过滤区域
        self.search_box = QLineEdit()
        self.search_box.setPlaceholderText("过滤用例...")
        self.search_box.textChanged.connect(self.filter_cases)

        # 添加搜索图标
        search_label = QLabel()
        search_label.setText("🔍")  # 使用Unicode搜索图标
        search_layout = QHBoxLayout()
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_box)
        search_layout.setSpacing(5)
        search_layout.setContentsMargins(0, 5, 0, 5)

        # 用例列表，树形结构展示用例
        self.case_tree = QTreeWidget()
        self.case_tree.setHeaderLabels(["用例名称", "基础用例"])
        self.case_tree.setAlternatingRowColors(True)  # 交替行颜色
        self.case_tree.setUniformRowHeights(True)  # 统一行高
        self.case_tree.setSortingEnabled(True)  # 允许排序
        self.case_tree.setSelectionMode(QTreeWidget.ExtendedSelection)  # 允许多选
        self.case_tree.itemSelectionChanged.connect(self.handle_selection_changed)

        # 设置表头样式
        header = self.case_tree.header()
        header.setDefaultSectionSize(200)  # 设置默认列宽
        header.setStretchLastSection(True)  # 最后一列自动拉伸

        # 添加右键菜单
        self.case_tree.setContextMenuPolicy(Qt.CustomContextMenu)
        self.case_tree.customContextMenuRequested.connect(self.show_context_menu)

        layout.addLayout(file_layout)
        layout.addLayout(search_layout)
        layout.addWidget(self.case_tree)
        self.setLayout(layout)

    def load_case_file(self):
        """加载用例文件，支持多选文件"""
        paths, _ = QFileDialog.getOpenFileNames(
            self,
            "选择用例文件",
            "",
            "用例文件 (*.txt *.cfg);;所有文件 (*.*)"
        )

        if not paths:
            return

        for path in paths:
            self.load_case_file_requested.emit(path)

    def remove_case_file(self):
        """删除用例文件，弹出文件选择对话框"""
        # 获取所有顶层项（文件节点）
        root = self.case_tree.invisibleRootItem()
        if root.childCount() == 0:
            QMessageBox.information(self, "提示", "没有可删除的用例文件")
            return

        # 显示文件选择对话框
        self.show_delete_files_dialog()

    def show_delete_files_dialog(self):
        """显示文件选择对话框，让用户选择要删除的文件"""
        # 创建文件选择对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("选择要删除的用例文件")
        dialog.setMinimumWidth(400)

        layout = QVBoxLayout()

        # 创建文件列表，用于显示可删除的文件
        file_list = QTreeWidget()
        file_list.setHeaderLabels(["文件名", "路径"])
        file_list.setAlternatingRowColors(True)
        file_list.setSelectionMode(QTreeWidget.ExtendedSelection)

        # 添加文件到列表
        root = self.case_tree.invisibleRootItem()
        for i in range(root.childCount()):
            item = root.child(i)
            file_name = item.text(0)
            # 路径列留空，因为我们只有文件名
            list_item = QTreeWidgetItem([file_name, ""])
            file_list.addTopLevelItem(list_item)

        # 调整列宽以适应内容
        file_list.resizeColumnToContents(0)

        layout.addWidget(file_list)

        # 按钮区域，水平布局
        btn_layout = QHBoxLayout()
        delete_btn = QPushButton("删除选中")
        cancel_btn = QPushButton("取消")

        btn_layout.addWidget(delete_btn)
        btn_layout.addWidget(cancel_btn)

        layout.addLayout(btn_layout)
        dialog.setLayout(layout)

        # 连接信号和槽
        delete_btn.clicked.connect(lambda: self.delete_files_from_dialog(file_list, dialog))
        cancel_btn.clicked.connect(dialog.reject)

        # 显示对话框，模态显示
        dialog.exec_()

    def delete_files_from_dialog(self, file_list, dialog):
        """从文件选择对话框中删除选中的文件"""
        selected_items = file_list.selectedItems()

        if not selected_items:
            QMessageBox.information(dialog, "提示", "请选择要删除的文件")
            return

        # 获取选中的文件名
        selected_files = [item.text(0) for item in selected_items]

        # 确认删除
        reply = QMessageBox.question(
            dialog,
            "确认删除",
            f"确定要删除选中的 {len(selected_files)} 个用例文件吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 发送信号，请求删除文件
            for file_name in selected_files:
                self.remove_case_file_requested.emit(file_name)

            # 关闭对话框
            dialog.accept()

    def filter_cases(self, text):
        """过滤用例树，根据关键字显示或隐藏用例"""
        keyword = text.strip().lower()

        def filter_item(item):
            """递归过滤树节点"""
            # 获取当前项的文本，转换为小写
            item_text = item.text(0).lower()
            item_visible = keyword in item_text

            # 遍历所有子项，递归过滤
            child_visible = False
            for i in range(item.childCount()):
                child = item.child(i)
                if filter_item(child):
                    child_visible = True

            # 如果当前项或任何子项可见，则显示当前项
            visible = item_visible or child_visible
            item.setHidden(not visible)

            # 如果当前项可见，确保其所有父节点也可见
            if visible:
                parent = item.parent()
                while parent:
                    parent.setHidden(False)
                    parent = parent.parent()

            return visible

        # 从根节点开始过滤
        root = self.case_tree.invisibleRootItem()
        for i in range(root.childCount()):
            filter_item(root.child(i))

    def handle_selection_changed(self):
        """处理用例选择变化"""
        selected_items = self.case_tree.selectedItems()

        if selected_items:
            # 过滤掉文件名节点（顶层节点），只处理用例节点
            valid_items = [item for item in selected_items if item.parent() is not None]
            if valid_items:
                if len(valid_items) == 1:
                    self.case_selected.emit(valid_items[0].text(0))
                else:
                    self.case_selected.emit(f"已选择 {len(valid_items)} 个用例")

    def show_context_menu(self, position):
        """显示右键菜单"""
        menu = QMenu()

        # 获取选中的项
        selected_items = self.case_tree.selectedItems()
        top_level_items = []

        for item in selected_items:
            # 找到顶级父项，即文件节点
            parent = item
            while parent.parent():
                parent = parent.parent()

            if parent not in top_level_items:
                top_level_items.append(parent)

        # 如果有选中的顶级项，添加菜单选项
        if top_level_items:
            # 添加自动解析参数选项
            parse_action = menu.addAction("自动解析Base/Block参数")
            parse_action.triggered.connect(lambda: self.parse_params_from_selected(top_level_items))

            menu.addSeparator()

            remove_action = menu.addAction("删除用例文件")
            remove_action.triggered.connect(lambda: self.remove_selected_files(top_level_items))

        # 显示菜单
        if not menu.isEmpty():
            menu.exec_(self.case_tree.viewport().mapToGlobal(position))

    def parse_params_from_selected(self, items):
        """从选中的用例文件解析 Base/Block 参数"""
        # 发送信号，请求控制器解析参数
        self.parse_params_requested.emit(items)

    def remove_selected_files(self, items):
        """删除选中的用例文件"""
        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除选中的 {len(items)} 个用例文件吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            for item in items:
                file_name = item.text(0)
                self.remove_case_file_requested.emit(file_name)

    def update_case_tree(self, case_data):
        """更新用例树"""
        self.case_tree.clear()

        for file_data in case_data:
            file_root = QTreeWidgetItem([file_data['name'], ""])
            self.case_tree.addTopLevelItem(file_root)

            # 添加根节点
            nodes = {}
            for case in file_data['nodes']:
                item = QTreeWidgetItem([case, ""])
                nodes[case] = item
                file_root.addChild(item)

            # 添加子节点
            for case, base in file_data['child_cases']:
                if base in nodes:
                    parent = nodes[base]
                    item = QTreeWidgetItem(parent, [case, base])
                    nodes[case] = item
                else:
                    parent_item = QTreeWidgetItem([base, ""])
                    nodes[base] = parent_item
                    file_root.addChild(parent_item)
                    item = QTreeWidgetItem(parent_item, [case, base])
                    nodes[case] = item

            # 展开树
            file_root.setExpanded(True)
