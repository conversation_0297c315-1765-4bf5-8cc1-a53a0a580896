#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示插件，用于展示非模态对话框功能
"""
from PyQt5.QtWidgets import (
    QAction, QVBoxLayout, QHBoxLayout, QPushButton, 
    QLabel, QTextEdit, QLineEdit
)
from PyQt5.QtCore import Qt, pyqtSlot
from plugins.base import PluginBase

class DemoPlugin(PluginBase):
    """演示插件，展示非模态对话框功能"""

    @property
    def name(self):
        return "演示插件"

    @property
    def version(self):
        return "1.0.0"

    @property
    def description(self):
        return "演示非模态对话框功能"

    def initialize(self, main_window):
        """初始化插件"""
        try:
            self.main_window = main_window
            self.dialogs = []  # 存储所有创建的对话框

            # 创建菜单项
            self.menu_action = QAction(self.name, main_window)
            self.menu_action.setStatusTip(self.description)
            self.menu_action.triggered.connect(self.show_demo_dialog)

            # 添加到工具菜单
            if hasattr(main_window, 'tools_menu'):
                self.main_window.tools_menu.addSeparator()
                self.main_window.tools_menu.addAction(self.menu_action)

            print(f"成功初始化插件: {self.name}")

        except Exception as e:
            print(f"初始化插件 {self.name} 失败: {str(e)}")

    def cleanup(self):
        """清理插件资源"""
        try:
            # 关闭所有对话框
            for dialog in self.dialogs[:]:
                dialog.close()
                
            # 移除菜单项
            if hasattr(self.main_window, 'tools_menu') and hasattr(self, 'menu_action'):
                self.main_window.tools_menu.removeAction(self.menu_action)
                
            print(f"成功清理插件: {self.name}")
            
        except Exception as e:
            print(f"清理插件 {self.name} 失败: {str(e)}")

    def show_demo_dialog(self):
        """显示演示对话框"""
        # 创建非模态对话框
        dialog = self.create_dialog(self.main_window, "演示非模态对话框")
        dialog.resize(600, 400)
        
        # 创建布局
        layout = QVBoxLayout()
        
        # 添加说明标签
        title_label = QLabel("非模态对话框演示")
        title_label.setStyleSheet("font-size: 16pt; font-weight: bold; color: #4a9eff;")
        layout.addWidget(title_label)
        
        info_label = QLabel(
            "这是一个非模态对话框演示。您可以在此对话框打开的情况下继续使用主界面。"
            "点击主窗口，主窗口将显示在此对话框之上。"
        )
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 添加文本编辑框
        text_edit = QTextEdit()
        text_edit.setPlaceholderText("在这里输入文本...")
        layout.addWidget(text_edit)
        
        # 添加按钮区域
        button_layout = QHBoxLayout()
        
        # 创建新对话框按钮
        new_dialog_btn = QPushButton("创建新对话框")
        new_dialog_btn.clicked.connect(self.show_demo_dialog)
        button_layout.addWidget(new_dialog_btn)
        
        # 激活主窗口按钮
        activate_main_btn = QPushButton("激活主窗口")
        activate_main_btn.clicked.connect(self.main_window.activateWindow)
        button_layout.addWidget(activate_main_btn)
        
        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(dialog.close)
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
        
        # 设置对话框布局
        dialog.setLayout(layout)
        
        # 存储对话框引用
        self.dialogs.append(dialog)
        
        # 连接关闭信号，从列表中移除对话框
        dialog.finished.connect(lambda: self.remove_dialog(dialog))
        
        # 显示对话框
        dialog.show()
        
    def remove_dialog(self, dialog):
        """从对话框列表中移除对话框"""
        if dialog in self.dialogs:
            self.dialogs.remove(dialog)
