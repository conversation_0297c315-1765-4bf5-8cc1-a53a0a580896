# 仪表盘问题修复指南

## 问题概述

用户报告了两个主要问题：

1. **JavaScript函数未定义错误**：`showPassRateDetails is not defined`
2. **用例管理页面API错误**：服务器返回500内部错误

## 问题分析

### 问题1：JavaScript函数未定义
- **原因**：用例通过率图表的点击事件调用了未定义的`showPassRateDetails`函数
- **影响**：点击图表时会出现JavaScript错误

### 问题2：用例管理API错误
- **原因**：`TestCaseManager`模块导入失败或方法不存在
- **影响**：无法添加、查询用例，用例管理功能完全不可用

## 修复方案

### ✅ 已修复：JavaScript函数未定义

**修复文件**：`plugins/builtin/dashboard_web/static/js/dashboard.js`

**修复内容**：
```javascript
/**
 * 显示用例通过率详情
 */
function showPassRateDetails(date, index) {
    console.log('显示用例通过率详情:', date, index);
    
    // 获取当前图表数据
    const chart = dashboardCharts.passRateChart;
    if (!chart || !chart.data) return;
    
    const passCount = chart.data.datasets[0].data[index] || 0;
    const totalCount = chart.data.datasets[1].data[index] || 0;
    const passRate = chart.data.datasets[2].data[index] || 0;
    
    // 构建详情内容并显示
    const content = `
        <div class="pass-rate-details">
            <h6>日期: ${date}</h6>
            <div class="row">
                <div class="col-4 text-center">
                    <div class="detail-item">
                        <h4 class="text-success">${passCount}</h4>
                        <small>通过用例</small>
                    </div>
                </div>
                <div class="col-4 text-center">
                    <div class="detail-item">
                        <h4 class="text-primary">${totalCount}</h4>
                        <small>执行用例</small>
                    </div>
                </div>
                <div class="col-4 text-center">
                    <div class="detail-item">
                        <h4 class="text-warning">${passRate}%</h4>
                        <small>通过率</small>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // 显示模态框或提示
    if (typeof showModal === 'function') {
        showModal('用例通过率详情', content);
    } else {
        alert(`${date}\n通过用例: ${passCount}\n执行用例: ${totalCount}\n通过率: ${passRate}%`);
    }
}
```

### ✅ 已修复：API错误处理增强

**修复文件**：`plugins/builtin/dashboard_web/routes/testplan.py`

**修复内容**：
1. 在所有使用`TestCaseManager`的API端点添加了模块可用性检查
2. 提供更详细的错误信息

```python
# 检查TestCaseManager是否可用
if TestCaseManager is None:
    logger.error("TestCaseManager未正确导入")
    return jsonify({
        'success': False,
        'error': 'TestCaseManager模块不可用',
        'message': '请检查models/testplan.py文件是否存在'
    }), 500
```

## 手动修复步骤

如果问题仍然存在，请按以下步骤手动修复：

### 步骤1：检查数据库结构

```python
import sqlite3
import os

db_path = 'plugins/builtin/dashboard_web/data/dashboard.db'
if os.path.exists(db_path):
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 检查test_cases表
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='test_cases'")
    if not cursor.fetchone():
        print("❌ test_cases表不存在")
    else:
        print("✅ test_cases表存在")
    
    conn.close()
else:
    print("❌ 数据库文件不存在")
```

### 步骤2：检查TestCaseManager模块

```python
import sys
import os

# 添加路径
dashboard_path = 'plugins/builtin/dashboard_web'
if dashboard_path not in sys.path:
    sys.path.insert(0, dashboard_path)

try:
    from models.testplan import TestCaseManager
    print("✅ TestCaseManager导入成功")
    
    # 检查关键方法
    methods = ['case_name_exists', 'create_test_case', 'get_test_cases']
    for method in methods:
        if hasattr(TestCaseManager, method):
            print(f"✅ 方法 {method} 存在")
        else:
            print(f"❌ 方法 {method} 不存在")
            
except ImportError as e:
    print(f"❌ TestCaseManager导入失败: {e}")
```

### 步骤3：重启仪表盘服务

```bash
# 停止现有服务
# 然后重新启动
cd plugins/builtin/dashboard_web
python app.py
```

## 验证修复

### 1. 验证JavaScript修复

1. 打开仪表盘页面
2. 查看用例通过率统计图表
3. 点击图表上的数据点
4. 应该显示详情信息而不是错误

### 2. 验证API修复

1. 打开用例管理页面
2. 尝试添加新用例
3. 检查是否能正常保存
4. 查看浏览器控制台是否还有500错误

## 预防措施

### 1. 代码质量检查

- 在添加新的图表交互功能时，确保所有回调函数都已定义
- 使用TypeScript或JSDoc来提供更好的类型检查

### 2. 模块导入检查

- 在API路由中始终检查依赖模块的可用性
- 提供有意义的错误消息

### 3. 测试覆盖

- 为所有API端点添加单元测试
- 为前端交互功能添加集成测试

## 常见问题

### Q: 修复后仍然出现JavaScript错误怎么办？

A: 
1. 清除浏览器缓存
2. 检查浏览器控制台的具体错误信息
3. 确认`dashboard.js`文件已正确更新

### Q: API仍然返回500错误怎么办？

A:
1. 检查服务器日志获取详细错误信息
2. 确认数据库文件存在且可访问
3. 验证`models/testplan.py`文件存在且语法正确

### Q: 如何查看详细的错误日志？

A:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
# 然后重启服务查看详细日志
```

## 联系支持

如果按照本指南操作后问题仍然存在，请提供：

1. 浏览器控制台的完整错误信息
2. 服务器日志文件
3. 数据库文件状态
4. Python环境信息

---

**修复状态：已完成** ✅  
**最后更新：2024年12月20日**
