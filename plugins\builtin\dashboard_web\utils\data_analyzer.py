#!/usr/bin/env python3
"""
数据分析器

提供各种数据分析和统计功能
"""

import sqlite3
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict
import json

logger = logging.getLogger(__name__)

class DataAnalyzer:
    """数据分析器类"""
    
    def __init__(self, db_connection):
        """
        初始化数据分析器
        
        Args:
            db_connection: 数据库连接
        """
        self.conn = db_connection
        self.cursor = db_connection.cursor()
    
    def analyze_test_coverage(self, project_id: Optional[int] = None) -> Dict[str, Any]:
        """
        分析测试覆盖率
        
        Args:
            project_id: 项目ID
            
        Returns:
            Dict: 覆盖率分析结果
        """
        try:
            where_clause = "WHERE project_id = ?" if project_id else ""
            params = [project_id] if project_id else []
            
            # 总体覆盖率
            self.cursor.execute(f'''
                SELECT
                    COUNT(*) as total_cases,
                    SUM(CASE WHEN subsys_status = 'PASS' OR top_status = 'PASS' 
                             OR post_subsys_status = 'PASS' OR post_top_status = 'PASS'
                        THEN 1 ELSE 0 END) as covered_cases
                FROM test_cases {where_clause}
            ''', params)
            
            result = self.cursor.fetchone()
            total_cases = result['total_cases'] or 0
            covered_cases = result['covered_cases'] or 0
            
            coverage_rate = (covered_cases / total_cases * 100) if total_cases > 0 else 0
            
            # 按测试区域分析
            self.cursor.execute(f'''
                SELECT
                    test_areas,
                    COUNT(*) as area_total,
                    SUM(CASE WHEN subsys_status = 'PASS' OR top_status = 'PASS' 
                             OR post_subsys_status = 'PASS' OR post_top_status = 'PASS'
                        THEN 1 ELSE 0 END) as area_covered
                FROM test_cases {where_clause}
                GROUP BY test_areas
                ORDER BY area_total DESC
            ''', params)
            
            area_coverage = []
            for row in self.cursor.fetchall():
                area_total = row['area_total'] or 0
                area_covered = row['area_covered'] or 0
                area_rate = (area_covered / area_total * 100) if area_total > 0 else 0
                
                area_coverage.append({
                    'area': row['test_areas'] or 'Unknown',
                    'total': area_total,
                    'covered': area_covered,
                    'rate': round(area_rate, 2)
                })
            
            return {
                'total_cases': total_cases,
                'covered_cases': covered_cases,
                'coverage_rate': round(coverage_rate, 2),
                'area_coverage': area_coverage,
                'analysis_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"分析测试覆盖率失败: {e}")
            return {}
    
    def analyze_bug_trends(self, days: int = 30) -> Dict[str, Any]:
        """
        分析BUG趋势
        
        Args:
            days: 分析天数
            
        Returns:
            Dict: BUG趋势分析结果
        """
        try:
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=days-1)
            
            # BUG状态分布
            self.cursor.execute('''
                SELECT
                    status,
                    COUNT(*) as count
                FROM bugs
                GROUP BY status
                ORDER BY count DESC
            ''')
            
            status_distribution = {
                row['status']: row['count'] for row in self.cursor.fetchall()
            }
            
            # BUG优先级分布
            self.cursor.execute('''
                SELECT
                    priority,
                    COUNT(*) as count
                FROM bugs
                GROUP BY priority
                ORDER BY 
                    CASE priority
                        WHEN 'Critical' THEN 1
                        WHEN 'High' THEN 2
                        WHEN 'Medium' THEN 3
                        WHEN 'Low' THEN 4
                        ELSE 5
                    END
            ''')
            
            priority_distribution = {
                row['priority']: row['count'] for row in self.cursor.fetchall()
            }
            
            # 每日BUG趋势
            self.cursor.execute('''
                SELECT
                    DATE(created_at) as bug_date,
                    COUNT(*) as new_bugs
                FROM bugs
                WHERE DATE(created_at) >= ? AND DATE(created_at) <= ?
                GROUP BY DATE(created_at)
                ORDER BY bug_date
            ''', (start_date, end_date))
            
            daily_trends = {
                row['bug_date']: row['new_bugs'] for row in self.cursor.fetchall()
            }
            
            # 修复率分析
            self.cursor.execute('''
                SELECT
                    COUNT(*) as total_bugs,
                    SUM(CASE WHEN status = 'Fixed' OR status = 'Closed' THEN 1 ELSE 0 END) as fixed_bugs
                FROM bugs
            ''')
            
            result = self.cursor.fetchone()
            total_bugs = result['total_bugs'] or 0
            fixed_bugs = result['fixed_bugs'] or 0
            fix_rate = (fixed_bugs / total_bugs * 100) if total_bugs > 0 else 0
            
            return {
                'status_distribution': status_distribution,
                'priority_distribution': priority_distribution,
                'daily_trends': daily_trends,
                'total_bugs': total_bugs,
                'fixed_bugs': fixed_bugs,
                'fix_rate': round(fix_rate, 2),
                'analysis_period': f"{start_date} to {end_date}",
                'analysis_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"分析BUG趋势失败: {e}")
            return {}
    
    def analyze_phase_efficiency(self, project_id: Optional[int] = None) -> Dict[str, Any]:
        """
        分析验证阶段效率
        
        Args:
            project_id: 项目ID
            
        Returns:
            Dict: 阶段效率分析结果
        """
        try:
            where_clause = "WHERE project_id = ?" if project_id else ""
            params = [project_id] if project_id else []
            
            phases = ['DVR1', 'DVR2', 'DVR3', 'DVS1', 'DVS2']
            phase_efficiency = {}
            
            for phase in phases:
                # 计算该阶段的通过率
                if phase in ['DVR1', 'DVR2', 'DVR3']:
                    # 前期阶段主要看subsys和top
                    self.cursor.execute(f'''
                        SELECT
                            COUNT(*) as total,
                            SUM(CASE WHEN subsys_status = 'PASS' THEN 1 ELSE 0 END) as subsys_pass,
                            SUM(CASE WHEN top_status = 'PASS' THEN 1 ELSE 0 END) as top_pass
                        FROM test_cases {where_clause}
                    ''', params)
                else:
                    # DVS阶段包括POST
                    self.cursor.execute(f'''
                        SELECT
                            COUNT(*) as total,
                            SUM(CASE WHEN subsys_status = 'PASS' THEN 1 ELSE 0 END) as subsys_pass,
                            SUM(CASE WHEN top_status = 'PASS' THEN 1 ELSE 0 END) as top_pass,
                            SUM(CASE WHEN post_subsys_status = 'PASS' THEN 1 ELSE 0 END) as post_subsys_pass,
                            SUM(CASE WHEN post_top_status = 'PASS' THEN 1 ELSE 0 END) as post_top_pass
                        FROM test_cases {where_clause}
                    ''', params)
                
                result = self.cursor.fetchone()
                total = result['total'] or 0
                
                if phase in ['DVR1', 'DVR2', 'DVR3']:
                    passed = (result['subsys_pass'] or 0) + (result['top_pass'] or 0)
                else:
                    passed = (result['subsys_pass'] or 0) + (result['top_pass'] or 0) + \
                            (result['post_subsys_pass'] or 0) + (result['post_top_pass'] or 0)
                
                efficiency = (passed / total * 100) if total > 0 else 0
                
                phase_efficiency[phase] = {
                    'total_cases': total,
                    'passed_cases': passed,
                    'efficiency': round(efficiency, 2)
                }
            
            return {
                'phase_efficiency': phase_efficiency,
                'analysis_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"分析阶段效率失败: {e}")
            return {}
    
    def generate_summary_report(self, project_id: Optional[int] = None) -> Dict[str, Any]:
        """
        生成汇总报告
        
        Args:
            project_id: 项目ID
            
        Returns:
            Dict: 汇总报告
        """
        try:
            # 获取各种分析结果
            coverage_analysis = self.analyze_test_coverage(project_id)
            bug_analysis = self.analyze_bug_trends()
            phase_analysis = self.analyze_phase_efficiency(project_id)
            
            # 生成关键指标
            key_metrics = {
                'test_coverage': coverage_analysis.get('coverage_rate', 0),
                'bug_fix_rate': bug_analysis.get('fix_rate', 0),
                'total_test_cases': coverage_analysis.get('total_cases', 0),
                'total_bugs': bug_analysis.get('total_bugs', 0),
                'open_bugs': sum(count for status, count in bug_analysis.get('status_distribution', {}).items() 
                               if status in ['Open', 'In Progress']),
            }
            
            # 生成建议
            recommendations = self._generate_recommendations(key_metrics, coverage_analysis, bug_analysis)
            
            return {
                'key_metrics': key_metrics,
                'coverage_analysis': coverage_analysis,
                'bug_analysis': bug_analysis,
                'phase_analysis': phase_analysis,
                'recommendations': recommendations,
                'report_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"生成汇总报告失败: {e}")
            return {}
    
    def _generate_recommendations(self, metrics: Dict, coverage: Dict, bugs: Dict) -> List[str]:
        """
        生成改进建议
        
        Args:
            metrics: 关键指标
            coverage: 覆盖率分析
            bugs: BUG分析
            
        Returns:
            List[str]: 建议列表
        """
        recommendations = []
        
        # 测试覆盖率建议
        if metrics['test_coverage'] < 80:
            recommendations.append("测试覆盖率偏低，建议增加测试用例或提高测试执行率")
        
        # BUG修复率建议
        if metrics['bug_fix_rate'] < 90:
            recommendations.append("BUG修复率需要提升，建议加强BUG处理流程")
        
        # 开放BUG数量建议
        if metrics['open_bugs'] > 10:
            recommendations.append("开放BUG数量较多，建议优先处理高优先级BUG")
        
        # 高优先级BUG建议
        priority_dist = bugs.get('priority_distribution', {})
        critical_bugs = priority_dist.get('Critical', 0) + priority_dist.get('High', 0)
        if critical_bugs > 5:
            recommendations.append("存在较多高优先级BUG，建议立即处理")
        
        # 测试区域覆盖建议
        area_coverage = coverage.get('area_coverage', [])
        low_coverage_areas = [area for area in area_coverage if area['rate'] < 70]
        if low_coverage_areas:
            areas = ', '.join([area['area'] for area in low_coverage_areas[:3]])
            recommendations.append(f"以下测试区域覆盖率较低，需要关注: {areas}")
        
        return recommendations

# 导出类
__all__ = ['DataAnalyzer']
