# RunSim GUI 仪表板快速开始指南

## 1. 概述

本指南提供RunSim GUI仪表板功能的快速实施步骤，基于Web技术栈实现，适用于内网环境。

## 2. 前置条件

### 2.1 环境要求
- Python 3.8+
- Flask（需要安装）
- openpyxl（需要安装）
- 现有的RunSim GUI项目

### 2.2 依赖安装
```bash
pip install flask openpyxl
```

## 3. 快速实施步骤

### 步骤1：创建插件主文件（30分钟）

创建 `plugins/builtin/dashboard_plugin.py`：

```python
import os
import sys
import threading
import webbrowser
from PyQt5.QtWidgets import QAction, QMessageBox
from plugins.base import PluginBase

class DashboardPlugin(PluginBase):
    @property
    def name(self):
        return "项目仪表板"
    
    @property
    def version(self):
        return "1.0.0"
    
    @property
    def description(self):
        return "项目管理仪表板，包括用例管理、BUG记录和进度展示"
    
    def __init__(self):
        super().__init__()
        self.server_thread = None
        self.server_port = 5000
        
    def initialize(self, main_window):
        self.main_window = main_window
        
        # 创建菜单项
        self.menu_action = QAction(self.name, main_window)
        self.menu_action.triggered.connect(self.open_dashboard)
        
        if hasattr(main_window, 'tools_menu'):
            main_window.tools_menu.addAction(self.menu_action)
        
        # 启动Web服务器
        self.start_web_server()
    
    def start_web_server(self):
        if self.server_thread is None or not self.server_thread.is_alive():
            self.server_thread = threading.Thread(target=self._run_server, daemon=True)
            self.server_thread.start()
    
    def _run_server(self):
        try:
            dashboard_path = os.path.join(os.path.dirname(__file__), 'dashboard_web')
            if dashboard_path not in sys.path:
                sys.path.insert(0, dashboard_path)
            
            from dashboard_web.app import create_app
            app = create_app()
            app.run(host='127.0.0.1', port=self.server_port, debug=False)
        except Exception as e:
            print(f"启动Web服务器失败: {str(e)}")
    
    def open_dashboard(self):
        try:
            url = f"http://127.0.0.1:{self.server_port}"
            webbrowser.open(url)
        except Exception as e:
            QMessageBox.warning(self.main_window, "错误", f"无法打开仪表板: {str(e)}")
    
    def cleanup(self):
        pass
```

### 步骤2：创建Web应用结构（45分钟）

创建目录结构：
```
plugins/builtin/dashboard_web/
├── __init__.py
├── app.py
├── models/
│   ├── __init__.py
│   └── database.py
├── routes/
│   ├── __init__.py
│   └── api.py
├── static/
│   ├── css/
│   ├── js/
│   └── uploads/
├── templates/
└── data/
```

### 步骤3：实现Flask应用（60分钟）

创建 `dashboard_web/app.py`：

```python
import os
from flask import Flask, render_template, jsonify
from models.database import init_db

def create_app():
    app = Flask(__name__)
    
    # 配置
    app.config['SECRET_KEY'] = 'dashboard-secret-key'
    app.config['DATABASE_PATH'] = os.path.join(
        os.path.dirname(__file__), 'data', 'dashboard.db'
    )
    
    # 确保目录存在
    os.makedirs(os.path.dirname(app.config['DATABASE_PATH']), exist_ok=True)
    
    # 初始化数据库
    init_db(app.config['DATABASE_PATH'])
    
    @app.route('/')
    def index():
        return render_template('dashboard.html')
    
    @app.route('/api/dashboard/statistics')
    def statistics():
        # 返回模拟数据
        return jsonify({
            'total_cases': 100,
            'passed_cases': 75,
            'failed_cases': 15,
            'running_cases': 10
        })
    
    return app
```

### 步骤4：创建数据库模型（30分钟）

创建 `dashboard_web/models/database.py`：

```python
import sqlite3
from contextlib import contextmanager

def init_db(db_path):
    with sqlite3.connect(db_path) as conn:
        cursor = conn.cursor()
        
        # 创建用例表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_cases (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                case_name TEXT NOT NULL,
                status TEXT,
                start_time TIMESTAMP,
                end_time TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建BUG表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS bugs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                bug_id TEXT NOT NULL,
                description TEXT,
                status TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()

@contextmanager
def get_db():
    from flask import current_app
    conn = sqlite3.connect(current_app.config['DATABASE_PATH'])
    conn.row_factory = sqlite3.Row
    try:
        yield conn
    finally:
        conn.close()
```

### 步骤5：创建前端页面（90分钟）

#### 5.1 下载前端资源

下载以下文件到 `static/` 目录：
- Bootstrap 5 CSS/JS
- Chart.js
- jQuery

或使用CDN链接。

#### 5.2 创建主页面模板

创建 `templates/dashboard.html`：

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RunSim 项目仪表板</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">RunSim 仪表板</a>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-white bg-primary">
                    <div class="card-body">
                        <h5 class="card-title">总用例数</h5>
                        <h2 id="total-cases">0</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-success">
                    <div class="card-body">
                        <h5 class="card-title">通过用例</h5>
                        <h2 id="passed-cases">0</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-danger">
                    <div class="card-body">
                        <h5 class="card-title">失败用例</h5>
                        <h2 id="failed-cases">0</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-warning">
                    <div class="card-body">
                        <h5 class="card-title">进行中</h5>
                        <h2 id="running-cases">0</h2>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>用例状态分布</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="statusChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>项目进度</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="progressChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        $(document).ready(function() {
            loadDashboardData();
            initCharts();
        });

        function loadDashboardData() {
            $.get('/api/dashboard/statistics', function(data) {
                $('#total-cases').text(data.total_cases || 0);
                $('#passed-cases').text(data.passed_cases || 0);
                $('#failed-cases').text(data.failed_cases || 0);
                $('#running-cases').text(data.running_cases || 0);
            });
        }

        function initCharts() {
            // 状态分布图表
            const statusCtx = document.getElementById('statusChart').getContext('2d');
            new Chart(statusCtx, {
                type: 'pie',
                data: {
                    labels: ['Pass', 'Fail', 'Running', 'Pending'],
                    datasets: [{
                        data: [75, 15, 10, 0],
                        backgroundColor: ['#28a745', '#dc3545', '#ffc107', '#6c757d']
                    }]
                },
                options: {
                    responsive: true
                }
            });

            // 进度图表
            const progressCtx = document.getElementById('progressChart').getContext('2d');
            new Chart(progressCtx, {
                type: 'doughnut',
                data: {
                    labels: ['完成', '剩余'],
                    datasets: [{
                        data: [75, 25],
                        backgroundColor: ['#28a745', '#e9ecef']
                    }]
                },
                options: {
                    responsive: true
                }
            });
        }
    </script>
</body>
</html>
```

## 4. 测试和验证

### 4.1 启动测试
1. 启动RunSim GUI
2. 在工具菜单中点击"项目仪表板"
3. 浏览器应该自动打开仪表板页面

### 4.2 验证功能
- [ ] 仪表板页面正常显示
- [ ] 统计卡片显示数据
- [ ] 图表正常渲染
- [ ] 页面响应式布局正常

## 5. 后续扩展

### 5.1 添加Excel导入功能
- 实现Excel文件解析
- 添加文件上传接口
- 创建用例管理页面

### 5.2 添加BUG管理
- 创建BUG录入界面
- 实现BUG统计分析
- 添加BUG趋势图表

### 5.3 集成RunSim GUI
- 监听用例执行事件
- 自动更新用例状态
- 实现实时数据同步

## 6. 常见问题

### Q1: Flask服务器启动失败
**A**: 检查端口是否被占用，可以修改端口号或实现动态端口分配。

### Q2: 图表不显示
**A**: 检查Chart.js是否正确加载，可以使用CDN或下载到本地。

### Q3: 数据库连接失败
**A**: 确保data目录有写权限，检查SQLite数据库文件是否正确创建。

## 7. 总结

通过以上步骤，可以在约4-5小时内完成基础仪表板功能的实现。这个方案解决了内网环境缺少图表库的问题，提供了现代化的Web界面，并且可以根据需要逐步扩展功能。

关键优势：
- 无需额外Python图表库
- 使用成熟的Web技术栈
- 界面美观且响应式
- 易于扩展和维护
- 本地数据安全可控
