# RunSim GUI

RunSim GUI 是一个基于 PyQt5 的图形用户界面应用程序，用于控制和管理仿真运行。它提供了用例管理、配置管理、命令执行、日志查看等功能，并支持插件系统扩展功能。

## 功能特性

- **用例管理**：加载、解析和管理用例文件
- **配置管理**：设置和管理仿真参数
- **命令执行**：生成和执行仿真命令
- **日志查看**：实时查看执行日志
- **历史记录**：记录和管理历史命令
- **插件系统**：支持通过插件扩展功能

## 系统要求

- Python 3.8 或更高版本
- PyQt5 5.15 或更高版本
- 其他依赖项（见 requirements.txt）

## 安装

### 从源代码安装

1. 克隆或下载源代码

```bash
git clone https://github.com/your-organization/runsim-gui.git
cd runsim-gui
```

2. 安装依赖

```bash
pip install -r requirements.txt
```

### 使用安装包安装

1. 下载最新的安装包
2. 解压安装包到目标目录
3. 运行 `python runsim_gui.py` 启动应用程序

## 使用方法

### 启动应用程序

```bash
python runsim_gui.py
```

### 加载用例

1. 在左侧用例管理面板中，点击"添加用例"按钮
2. 在弹出的文件对话框中选择用例文件
3. 用例文件将被添加到用例树中

### 配置参数

在右侧"运行参数配置"标签页中，设置仿真参数：

- **基础参数**：Base、Block、用例、运行目录等
- **仿真选项**：FSDB、VWDB、CL、覆盖率等
- **高级选项**：种子号、Simarg、CFG DEF等

### 执行命令

1. 配置完参数后，点击"执行"按钮
2. 系统将生成命令并在新的日志标签页中执行
3. 执行结果将实时显示在日志标签页中

### 使用插件

1. 点击菜单栏中的"工具"
2. 选择要使用的插件
3. 插件将在新窗口中打开

## Documentation

Detailed documentation can be found in the `docs` directory:

- [Architecture Overview](docs/architecture_overview.md)
- [API Reference](docs/api_reference.md)
- [Development Guide](docs/development_guide.md)
- [User Manual](docs/user_manual.md)
- [Installation Guide](docs/installation_guide.md)
- [Testing Documentation](docs/testing_documentation.md)
- [Class and Flow Diagrams](docs/class_and_flow_diagrams.md)
- [Documentation Index](docs/documentation_index.md)

## 开发

### 项目结构

```
runsim/
├── app.py                  # 应用程序入口点（原始版本）
├── runsim_gui.py           # 应用程序入口点（重构版本）
├── models/                 # 数据模型
├── views/                  # 视图组件
├── controllers/            # 控制器
├── utils/                  # 工具类
├── plugins/                # 插件系统
└── docs/                   # 文档
```

### 开发环境设置

1. 克隆仓库
2. 安装依赖
3. 设置开发环境

```bash
git clone https://github.com/your-organization/runsim-gui.git
cd runsim-gui
pip install -r requirements.txt
pip install -r requirements-dev.txt
```

### 运行测试

```bash
python test_app.py
python test_integration.py
python test_functionality.py
python test_performance.py
```

### 创建发布包

```bash
python create_release_package.py --version 2.0.0 --output dist
```

## 贡献

欢迎贡献代码、报告问题或提出建议。请遵循以下步骤：

1. Fork 仓库
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 联系方式

- 项目维护者：Your Name - <EMAIL>
- 项目链接：https://github.com/your-organization/runsim-gui
