{% extends "base.html" %}

{% block title %}BUG管理 - RunSim 项目仪表板{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12 d-flex justify-content-between align-items-center">
        <div>
            <h1 class="h3 mb-0">BUG管理</h1>
            <p class="text-muted mb-0">记录和管理项目BUG</p>
        </div>
        <div>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addBugModal">
                <i class="fas fa-plus me-1"></i>新增BUG
            </button>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">总BUG数</h5>
                        <h2 id="total-bugs">0</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-bug fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-danger">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">未解决</h5>
                        <h2 id="open-bugs">0</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">已修复</h5>
                        <h2 id="fixed-bugs">0</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-wrench fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">已关闭</h5>
                        <h2 id="closed-bugs">0</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 过滤和搜索 -->
<div class="row mb-3">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label for="statusFilter" class="form-label">状态过滤</label>
                        <select class="form-select" id="statusFilter">
                            <option value="">全部状态</option>
                            <option value="Open">未解决</option>
                            <option value="In Progress">处理中</option>
                            <option value="Fixed">已修复</option>
                            <option value="Closed">已关闭</option>
                            <option value="Rejected">已拒绝</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="severityFilter" class="form-label">严重程度</label>
                        <select class="form-select" id="severityFilter">
                            <option value="">全部级别</option>
                            <option value="Critical">严重</option>
                            <option value="High">高</option>
                            <option value="Medium">中</option>
                            <option value="Low">低</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="searchInput" class="form-label">搜索</label>
                        <input type="text" class="form-control" id="searchInput" placeholder="搜索BUG ID或描述...">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-outline-secondary" onclick="resetFilters()">
                                <i class="fas fa-undo me-1"></i>重置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- BUG列表 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">BUG列表</h5>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="refreshBugList()">
                        <i class="fas fa-sync-alt me-1"></i>刷新
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>BUG ID</th>
                                <th>类型</th>
                                <th>严重程度</th>
                                <th>状态</th>
                                <th>描述</th>
                                <th>提交人</th>
                                <th>提交日期</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="bugTableBody">
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <p class="mt-2 text-muted">正在加载BUG数据...</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <nav aria-label="BUG列表分页" id="paginationContainer" style="display: none;">
                    <ul class="pagination justify-content-center" id="pagination">
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">BUG状态分布</h5>
            </div>
            <div class="card-body">
                <canvas id="bugStatusChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">严重程度分布</h5>
            </div>
            <div class="card-body">
                <canvas id="bugSeverityChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">BUG趋势图</h5>
            </div>
            <div class="card-body">
                <canvas id="bugTrendChart" width="800" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 新增BUG模态框 -->
<div class="modal fade" id="addBugModal" tabindex="-1" aria-labelledby="addBugModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addBugModalLabel">新增BUG</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addBugForm">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="bugId" class="form-label">BUG ID <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="bugId" name="bug_id" required>
                        </div>
                        <div class="col-md-6">
                            <label for="bugType" class="form-label">BUG类型</label>
                            <select class="form-select" id="bugType" name="bug_type">
                                <option value="">请选择</option>
                                <option value="功能缺陷">功能缺陷</option>
                                <option value="性能问题">性能问题</option>
                                <option value="界面问题">界面问题</option>
                                <option value="兼容性问题">兼容性问题</option>
                                <option value="安全问题">安全问题</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="severity" class="form-label">严重程度</label>
                            <select class="form-select" id="severity" name="severity">
                                <option value="Medium">中</option>
                                <option value="Critical">严重</option>
                                <option value="High">高</option>
                                <option value="Low">低</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="status" class="form-label">状态</label>
                            <select class="form-select" id="status" name="status">
                                <option value="Open">未解决</option>
                                <option value="In Progress">处理中</option>
                                <option value="Fixed">已修复</option>
                                <option value="Closed">已关闭</option>
                                <option value="Rejected">已拒绝</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label for="description" class="form-label">BUG描述 <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
                        </div>
                        <div class="col-md-6">
                            <label for="submitSys" class="form-label">提交系统</label>
                            <input type="text" class="form-control" id="submitSys" name="submit_sys">
                        </div>
                        <div class="col-md-6">
                            <label for="verificationStage" class="form-label">验证阶段</label>
                            <select class="form-select" id="verificationStage" name="verification_stage">
                                <option value="">请选择</option>
                                <option value="子系统级">子系统级</option>
                                <option value="TOP级">TOP级</option>
                                <option value="后仿真">后仿真</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="discoveryPlatform" class="form-label">发现平台</label>
                            <input type="text" class="form-control" id="discoveryPlatform" name="discovery_platform">
                        </div>
                        <div class="col-md-6">
                            <label for="discoveryCase" class="form-label">发现用例</label>
                            <input type="text" class="form-control" id="discoveryCase" name="discovery_case">
                        </div>
                        <div class="col-md-6">
                            <label for="submitter" class="form-label">提交人</label>
                            <input type="text" class="form-control" id="submitter" name="submitter">
                        </div>
                        <div class="col-md-6">
                            <label for="verifier" class="form-label">验证人</label>
                            <input type="text" class="form-control" id="verifier" name="verifier">
                        </div>
                        <div class="col-md-6">
                            <label for="submitDate" class="form-label">提交日期</label>
                            <input type="date" class="form-control" id="submitDate" name="submit_date">
                        </div>
                        <div class="col-md-6">
                            <label for="fixDate" class="form-label">修复日期</label>
                            <input type="date" class="form-control" id="fixDate" name="fix_date">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveBug()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑BUG模态框 -->
<div class="modal fade" id="editBugModal" tabindex="-1" aria-labelledby="editBugModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editBugModalLabel">编辑BUG</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editBugForm">
                    <input type="hidden" id="editBugId" name="id">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="editBugIdStr" class="form-label">BUG ID <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editBugIdStr" name="bug_id" required readonly>
                        </div>
                        <div class="col-md-6">
                            <label for="editBugType" class="form-label">BUG类型</label>
                            <select class="form-select" id="editBugType" name="bug_type">
                                <option value="">请选择</option>
                                <option value="功能缺陷">功能缺陷</option>
                                <option value="性能问题">性能问题</option>
                                <option value="界面问题">界面问题</option>
                                <option value="兼容性问题">兼容性问题</option>
                                <option value="安全问题">安全问题</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="editSeverity" class="form-label">严重程度</label>
                            <select class="form-select" id="editSeverity" name="severity">
                                <option value="Critical">严重</option>
                                <option value="High">高</option>
                                <option value="Medium">中</option>
                                <option value="Low">低</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="editStatus" class="form-label">状态</label>
                            <select class="form-select" id="editStatus" name="status">
                                <option value="Open">未解决</option>
                                <option value="In Progress">处理中</option>
                                <option value="Fixed">已修复</option>
                                <option value="Closed">已关闭</option>
                                <option value="Rejected">已拒绝</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label for="editDescription" class="form-label">BUG描述 <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="editDescription" name="description" rows="3" required></textarea>
                        </div>
                        <div class="col-md-6">
                            <label for="editSubmitSys" class="form-label">提交系统</label>
                            <input type="text" class="form-control" id="editSubmitSys" name="submit_sys">
                        </div>
                        <div class="col-md-6">
                            <label for="editVerificationStage" class="form-label">验证阶段</label>
                            <select class="form-select" id="editVerificationStage" name="verification_stage">
                                <option value="">请选择</option>
                                <option value="子系统级">子系统级</option>
                                <option value="TOP级">TOP级</option>
                                <option value="后仿真">后仿真</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="editDiscoveryPlatform" class="form-label">发现平台</label>
                            <input type="text" class="form-control" id="editDiscoveryPlatform" name="discovery_platform">
                        </div>
                        <div class="col-md-6">
                            <label for="editDiscoveryCase" class="form-label">发现用例</label>
                            <input type="text" class="form-control" id="editDiscoveryCase" name="discovery_case">
                        </div>
                        <div class="col-md-6">
                            <label for="editSubmitter" class="form-label">提交人</label>
                            <input type="text" class="form-control" id="editSubmitter" name="submitter">
                        </div>
                        <div class="col-md-6">
                            <label for="editVerifier" class="form-label">验证人</label>
                            <input type="text" class="form-control" id="editVerifier" name="verifier">
                        </div>
                        <div class="col-md-6">
                            <label for="editSubmitDate" class="form-label">提交日期</label>
                            <input type="date" class="form-control" id="editSubmitDate" name="submit_date">
                        </div>
                        <div class="col-md-6">
                            <label for="editFixDate" class="form-label">修复日期</label>
                            <input type="date" class="form-control" id="editFixDate" name="fix_date">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateBug()">更新</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/bug.js') }}"></script>
{% endblock %}