"""
数据导出API路由

该模块提供仪表盘数据导出功能，包括：
- Excel报告导出
- PDF报告导出
- JSON数据导出
- 图表图片导出
"""

import os
import io
import json
import logging
from datetime import datetime
from flask import Blueprint, request, jsonify, send_file, current_app

# 使用importlib动态导入模型，避免模块冲突
def _get_models():
    """动态导入模型"""
    import importlib.util

    models = {}
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)

    # 导入TestCaseManager
    try:
        testplan_file = os.path.join(parent_dir, 'models', 'testplan.py')
        if os.path.exists(testplan_file):
            spec = importlib.util.spec_from_file_location("export_testplan", testplan_file)
            testplan_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(testplan_module)
            models['TestCaseManager'] = getattr(testplan_module, 'TestCaseManager')
    except Exception as e:
        print(f"TestCaseManager导入失败: {e}")
        models['TestCaseManager'] = None

    # 导入BugModel
    try:
        bug_file = os.path.join(parent_dir, 'models', 'bug.py')
        if os.path.exists(bug_file):
            spec = importlib.util.spec_from_file_location("export_bug", bug_file)
            bug_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(bug_module)
            models['BugModel'] = getattr(bug_module, 'BugModel')
    except Exception as e:
        print(f"BugModel导入失败: {e}")
        models['BugModel'] = None

    return models

# 获取模型
_models = _get_models()
TestCaseManager = _models.get('TestCaseManager')
BugModel = _models.get('BugModel')
MODELS_AVAILABLE = TestCaseManager is not None and BugModel is not None

logger = logging.getLogger(__name__)

# 创建蓝图
export_bp = Blueprint('export', __name__)

def get_models():
    """获取模型实例"""
    if not MODELS_AVAILABLE:
        return None, None

    try:
        # TestCaseManager是静态类，BugModel需要实例化
        from flask import current_app
        db_path = current_app.config.get('DATABASE_PATH', 'data/dashboard.db')
        bug_model = BugModel(db_path)
        return TestCaseManager, bug_model
    except Exception as e:
        logger.error(f"获取模型类失败: {e}")
        return None, None

@export_bp.route('/dashboard/export/json', methods=['GET'])
def export_json():
    """导出JSON格式的仪表盘数据"""
    try:
        testplan_model, bug_model = get_models()

        if testplan_model is None or bug_model is None:
            # 使用备用数据
            export_data = {
                'export_info': {
                    'timestamp': datetime.now().isoformat(),
                    'version': '1.0',
                    'description': 'RunSim Dashboard Export Data (Fallback)'
                },
                'testcase_statistics': {
                    'total_testcases': 0,
                    'status_distribution': {}
                },
                'bug_statistics': {
                    'total_bugs': 0,
                    'open_bugs': 0,
                    'fixed_bugs': 0,
                    'closed_bugs': 0
                },
                'bug_trend': {
                    'labels': [],
                    'new_bugs': [],
                    'fixed_bugs': []
                },
                'progress_data': {
                    'subsys_progress': 0,
                    'top_progress': 0,
                    'post_subsys_progress': 0,
                    'post_top_progress': 0
                },
                'summary': {
                    'total_testcases': 0,
                    'total_bugs': 0,
                    'completion_rate': 0,
                    'bug_fix_rate': 0
                }
            }
        else:
            # 获取真实数据
            try:
                testcase_stats = testplan_model.get_case_statistics()
                bug_stats = bug_model.get_bug_statistics() if hasattr(bug_model, 'get_bug_statistics') else {}
                bug_trend = bug_model.get_bug_trend_data(30) if hasattr(bug_model, 'get_bug_trend_data') else {'labels': [], 'new_bugs': [], 'fixed_bugs': []}

                # 从testcase_stats中提取进度数据
                progress_data = {
                    'subsys_progress': testcase_stats.get('subsys_progress', 0),
                    'top_progress': testcase_stats.get('top_progress', 0),
                    'post_subsys_progress': testcase_stats.get('post_subsys_progress', 0),
                    'post_top_progress': testcase_stats.get('post_top_progress', 0)
                }
            except Exception as e:
                logger.warning(f"获取数据失败，使用空数据: {e}")
                testcase_stats = {'total_cases': 0, 'status_distribution': {}}
                bug_stats = {'total_bugs': 0, 'open_bugs': 0, 'fixed_bugs': 0, 'closed_bugs': 0}
                bug_trend = {'labels': [], 'new_bugs': [], 'fixed_bugs': []}
                progress_data = {'subsys_progress': 0, 'top_progress': 0, 'post_subsys_progress': 0, 'post_top_progress': 0}

            # 组装导出数据
            export_data = {
                'export_info': {
                    'timestamp': datetime.now().isoformat(),
                    'version': '1.0',
                    'description': 'RunSim Dashboard Export Data'
                },
                'testcase_statistics': testcase_stats,
                'bug_statistics': bug_stats,
                'bug_trend': bug_trend,
                'progress_data': progress_data,
                'summary': {
                    'total_testcases': testcase_stats.get('total_cases', 0),
                    'total_bugs': bug_stats.get('total_bugs', 0),
                    'completion_rate': calculate_completion_rate(testcase_stats),
                    'bug_fix_rate': calculate_bug_fix_rate(bug_stats)
                }
            }

        return jsonify({
            'success': True,
            'data': export_data
        })

    except Exception as e:
        logger.error(f"JSON导出失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'JSON导出失败: {str(e)}'
        }), 500

@export_bp.route('/dashboard/export/excel', methods=['POST'])
def export_excel():
    """导出Excel格式的仪表盘报告"""
    return jsonify({
        'success': False,
        'message': 'Excel导出功能暂时不可用，请使用JSON导出'
    }), 503

@export_bp.route('/dashboard/export/pdf', methods=['POST'])
def export_pdf():
    """导出PDF格式的仪表盘报告"""
    return jsonify({
        'success': False,
        'message': 'PDF导出功能暂时不可用，请使用JSON导出'
    }), 503

# 辅助函数

def calculate_completion_rate(testcase_stats):
    """计算完成率"""
    total = testcase_stats.get('total_cases', 0)
    if total == 0:
        return 0

    # 使用实际的统计字段
    subsys_pass = testcase_stats.get('subsys_pass', 0)
    subsys_fail = testcase_stats.get('subsys_fail', 0)
    top_pass = testcase_stats.get('top_pass', 0)
    top_fail = testcase_stats.get('top_fail', 0)

    # 计算总完成数（任一阶段完成即算完成）
    completed = max(subsys_pass + subsys_fail, top_pass + top_fail)

    return round((completed / total) * 100, 2) if total > 0 else 0

def calculate_bug_fix_rate(bug_stats):
    """计算BUG修复率"""
    total = bug_stats.get('total_bugs', 0)
    if total == 0:
        return 0

    fixed = bug_stats.get('fixed_bugs', 0)
    closed = bug_stats.get('closed_bugs', 0)
    resolved = fixed + closed

    return round((resolved / total) * 100, 2)

# 注意：Excel和PDF导出功能已暂时禁用，相关辅助函数已移除
