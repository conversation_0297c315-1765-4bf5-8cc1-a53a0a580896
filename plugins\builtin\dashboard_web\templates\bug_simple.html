<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BUG管理 - RunSim 项目仪表板</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>RunSim 仪表板
            </a>
            <div class="navbar-nav">
                <a class="nav-link" href="/">
                    <i class="fas fa-tachometer-alt me-1"></i>仪表板
                </a>
                <a class="nav-link" href="/testplan">
                    <i class="fas fa-list-check me-1"></i>用例管理
                </a>
                <a class="nav-link active" href="/bug">
                    <i class="fas fa-bug me-1"></i>BUG管理
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid mt-4">
        <div class="row mb-4">
            <div class="col-12 d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">BUG管理</h1>
                    <p class="text-muted mb-0">记录和管理项目BUG</p>
                </div>
                <div>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addBugModal">
                        <i class="fas fa-plus me-1"></i>新增BUG
                    </button>
                </div>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-white bg-info">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">总BUG数</h5>
                                <h2 id="total-bugs">0</h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-bug fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-danger">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">未解决</h5>
                                <h2 id="open-bugs">0</h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-exclamation-triangle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-warning">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">已修复</h5>
                                <h2 id="fixed-bugs">0</h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-wrench fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-success">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">已关闭</h5>
                                <h2 id="closed-bugs">0</h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-check-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- BUG列表 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">BUG列表</h5>
                        <div>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="loadBugList()">
                                <i class="fas fa-sync-alt me-1"></i>刷新
                            </button>
                            <button type="button" class="btn btn-sm btn-warning" onclick="testBugCreation()">
                                <i class="fas fa-flask me-1"></i>测试创建
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="bug-list-container">
                            <div class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="mt-2 text-muted">正在加载BUG数据...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 调试信息 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">调试信息</h5>
                    </div>
                    <div class="card-body">
                        <pre id="debug-log" style="background-color: #f8f9fa; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto;"></pre>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 新增BUG模态框 -->
    <div class="modal fade" id="addBugModal" tabindex="-1" aria-labelledby="addBugModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addBugModalLabel">新增BUG</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addBugForm">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="bugId" class="form-label">BUG ID <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="bugId" name="bug_id" required>
                            </div>
                            <div class="col-md-6">
                                <label for="severity" class="form-label">严重程度</label>
                                <select class="form-select" id="severity" name="severity">
                                    <option value="Medium">中</option>
                                    <option value="Critical">严重</option>
                                    <option value="High">高</option>
                                    <option value="Low">低</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label for="description" class="form-label">BUG描述 <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
                            </div>
                            <div class="col-md-6">
                                <label for="submitter" class="form-label">提交人</label>
                                <input type="text" class="form-control" id="submitter" name="submitter">
                            </div>
                            <div class="col-md-6">
                                <label for="submitDate" class="form-label">提交日期</label>
                                <input type="date" class="form-control" id="submitDate" name="submit_date">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveBug()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- 内联JavaScript -->
    <script>
        // 调试日志函数
        function debugLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('debug-log');
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        // 页面加载完成
        $(document).ready(function() {
            debugLog('页面加载完成');
            
            // 设置默认日期
            const today = new Date().toISOString().split('T')[0];
            $('#submitDate').val(today);
            
            // 设置默认BUG ID
            $('#bugId').val('SIMPLE-' + Date.now());
            
            // 加载数据
            loadBugStatistics();
            loadBugList();
            
            debugLog('初始化完成');
        });

        // 加载BUG统计
        function loadBugStatistics() {
            debugLog('开始加载BUG统计...');
            
            $.ajax({
                url: '/api/bugs/statistics',
                method: 'GET',
                success: function(response) {
                    debugLog('统计API响应: ' + JSON.stringify(response));
                    if (response.success) {
                        $('#total-bugs').text(response.data.total_bugs || 0);
                        $('#open-bugs').text(response.data.open_bugs || 0);
                        $('#fixed-bugs').text(response.data.fixed_bugs || 0);
                        $('#closed-bugs').text(response.data.closed_bugs || 0);
                        debugLog('统计数据更新成功');
                    } else {
                        debugLog('统计API返回失败: ' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    debugLog('统计API请求失败: ' + xhr.status + ' - ' + error);
                }
            });
        }

        // 加载BUG列表
        function loadBugList() {
            debugLog('开始加载BUG列表...');
            
            $.ajax({
                url: '/api/bugs',
                method: 'GET',
                success: function(response) {
                    debugLog('列表API响应: ' + JSON.stringify(response));
                    if (response.success) {
                        const bugs = response.data.bugs || [];
                        let html = '';
                        
                        if (bugs.length === 0) {
                            html = '<div class="text-center py-4"><p class="text-muted">暂无BUG数据</p></div>';
                        } else {
                            html = '<div class="table-responsive"><table class="table table-hover"><thead><tr><th>BUG ID</th><th>描述</th><th>严重程度</th><th>状态</th><th>提交人</th></tr></thead><tbody>';
                            bugs.forEach(bug => {
                                html += `<tr><td>${bug.bug_id}</td><td>${bug.description}</td><td>${bug.severity}</td><td>${bug.status}</td><td>${bug.submitter || ''}</td></tr>`;
                            });
                            html += '</tbody></table></div>';
                        }
                        
                        $('#bug-list-container').html(html);
                        debugLog('BUG列表更新成功，共' + bugs.length + '条记录');
                    } else {
                        debugLog('列表API返回失败: ' + response.message);
                        $('#bug-list-container').html('<div class="alert alert-warning">加载失败: ' + response.message + '</div>');
                    }
                },
                error: function(xhr, status, error) {
                    debugLog('列表API请求失败: ' + xhr.status + ' - ' + error);
                    $('#bug-list-container').html('<div class="alert alert-danger">请求失败: ' + error + '</div>');
                }
            });
        }

        // 保存BUG
        function saveBug() {
            debugLog('开始保存BUG...');
            
            const form = $('#addBugForm')[0];
            if (!form.checkValidity()) {
                form.reportValidity();
                debugLog('表单验证失败');
                return;
            }
            
            const formData = new FormData(form);
            const bugData = {};
            
            for (let [key, value] of formData.entries()) {
                bugData[key] = value || null;
            }
            
            debugLog('提交数据: ' + JSON.stringify(bugData));
            
            $.ajax({
                url: '/api/bugs',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(bugData),
                success: function(response) {
                    debugLog('保存API响应: ' + JSON.stringify(response));
                    if (response.success) {
                        $('#addBugModal').modal('hide');
                        alert('BUG创建成功！ID: ' + response.data.id);
                        loadBugList();
                        loadBugStatistics();
                        form.reset();
                        const today = new Date().toISOString().split('T')[0];
                        $('#submitDate').val(today);
                        $('#bugId').val('SIMPLE-' + Date.now());
                    } else {
                        debugLog('保存失败: ' + response.message);
                        alert('创建失败: ' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    debugLog('保存API请求失败: ' + xhr.status + ' - ' + xhr.responseText);
                    alert('请求失败: ' + (xhr.responseText || error));
                }
            });
        }

        // 测试BUG创建
        function testBugCreation() {
            debugLog('开始测试BUG创建...');
            
            const testData = {
                bug_id: 'TEST-' + Date.now(),
                description: '测试BUG - ' + new Date().toLocaleString(),
                severity: 'Medium',
                submitter: '测试用户'
            };
            
            debugLog('测试数据: ' + JSON.stringify(testData));
            
            $.ajax({
                url: '/api/bugs',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(testData),
                success: function(response) {
                    debugLog('测试成功: ' + JSON.stringify(response));
                    alert('测试创建成功！ID: ' + response.data.id);
                    loadBugList();
                    loadBugStatistics();
                },
                error: function(xhr, status, error) {
                    debugLog('测试失败: ' + xhr.status + ' - ' + xhr.responseText);
                    alert('测试失败: ' + (xhr.responseText || error));
                }
            });
        }
    </script>
</body>
</html>
