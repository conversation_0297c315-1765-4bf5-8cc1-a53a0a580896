# RunSim GUI 统计一致性修复报告

## 问题描述

在RunSim GUI的仪表盘和用例管理页面中，用例pass统计存在不一致的问题：

1. **状态值格式不统一**：仪表盘使用 `'Pass'`，用例管理页面使用 `'PASS'`
2. **统计逻辑不同**：
   - 仪表盘：使用OR逻辑，可能重复计算同一用例
   - 用例管理页面：分别统计各阶段，前端相加导致重复计算
3. **显示数据不一致**：两个页面显示的统计数据不匹配

## 修复方案

### 1. 统一状态值格式

将所有状态值统一为标准格式：
- `'PASS'` - 通过
- `'Pending'` - 待处理  
- `'On-Going'` - 进行中
- `'N/A'` - 不适用
- `'Fail'` - 失败

### 2. 修正统计逻辑

采用分级别统计方式，避免重复计算：

#### Subsys级别统计
- 包括 `subsys_status` 和 `post_subsys_status` 阶段
- 一个用例在该级别内只要任一阶段为PASS就计为1个通过用例
- 优先级：PASS > Pending/On-Going/Fail

#### TOP级别统计  
- 包括 `top_status` 和 `post_top_status` 阶段
- 一个用例在该级别内只要任一阶段为PASS就计为1个通过用例
- 优先级：PASS > Pending/On-Going/Fail

#### 总计统计
- 总通过数 = Subsys级别通过数 + TOP级别通过数
- 总待处理数 = Subsys级别待处理数 + TOP级别待处理数
- 总进行中数 = Subsys级别进行中数 + TOP级别进行中数

## 修改的文件

### 1. plugins/builtin/dashboard_web/routes/api.py

**修改内容：**
- 更新仪表盘统计API查询逻辑
- 修正状态值格式为 `'PASS'`
- 实现分级别统计，避免重复计算
- 返回分级别详细统计数据

**关键修改：**
```sql
-- Subsys级别统计（包括subsys和post_subsys阶段，避免重复计算）
SUM(CASE WHEN subsys_status = 'PASS' OR post_subsys_status = 'PASS'
    THEN 1 ELSE 0 END) as subsys_passed_cases,
SUM(CASE WHEN (subsys_status = 'Pending' OR post_subsys_status = 'Pending')
         AND subsys_status != 'PASS' AND post_subsys_status != 'PASS'
    THEN 1 ELSE 0 END) as subsys_pending_cases,
-- TOP级别统计（包括top和post_top阶段，避免重复计算）
SUM(CASE WHEN top_status = 'PASS' OR post_top_status = 'PASS'
    THEN 1 ELSE 0 END) as top_passed_cases,
```

### 2. plugins/builtin/dashboard_web/models/testplan.py

**修改内容：**
- 更新用例管理页面统计逻辑
- 统一状态值格式为 `'PASS'`
- 实现与仪表盘一致的分级别统计
- 添加总计统计字段

**关键修改：**
```python
# 添加总计统计（与仪表盘保持一致）
stats['total_passed'] = stats['subsys_pass'] + stats['top_pass']
stats['total_pending'] = stats.get('subsys_pending', 0) + stats.get('top_pending', 0)
stats['total_ongoing'] = stats.get('subsys_ongoing', 0) + stats.get('top_ongoing', 0)
stats['total_failed'] = stats['subsys_fail'] + stats['top_fail']
```

### 3. plugins/builtin/dashboard_web/templates/testplan.html

**修改内容：**
- 更新前端显示逻辑，使用修正后的统计数据
- 避免重复计算，优先使用总计字段
- 添加对pending状态的支持

**关键修改：**
```javascript
// 使用修正后的统计数据（避免重复计算）
const passCount = stats.total_passed || ((stats.subsys_pass || 0) + (stats.top_pass || 0));
const failCount = stats.total_failed || ((stats.subsys_fail || 0) + (stats.top_fail || 0));
const ongoingCount = stats.total_ongoing || ((stats.subsys_ongoing || 0) + (stats.top_ongoing || 0));
const pendingCount = stats.total_pending || ((stats.subsys_pending || 0) + (stats.top_pending || 0));
```

### 4. plugins/builtin/dashboard_web/templates/dashboard.html

**修改内容：**
- 更新仪表盘前端显示逻辑
- 支持分级别统计显示
- 修正状态图表标签和颜色

**关键修改：**
```javascript
// 状态图表标签修正
labels: ['通过', '待处理', '进行中', '未开始'],
backgroundColor: ['#28a745', '#17a2b8', '#ffc107', '#6c757d'],

// 支持分级别显示
if (cases.subsys && $('#subsys-passed').length) {
    $('#subsys-passed').text(cases.subsys.passed || 0);
    $('#subsys-pending').text(cases.subsys.pending || 0);
    $('#subsys-running').text(cases.subsys.running || 0);
}
```

## 验证结果

通过测试脚本验证，修复后的统计结果：

```
对比结果:
  ✓ 总用例数: 18 (一致)
  ✓ 总通过数: 19 (一致)
  ✓ 总待处理数: 1 (一致)
  ✓ 总进行中数: 2 (一致)
  ✓ Subsys通过数: 15 (一致)
  ✓ Subsys待处理数: 0 (一致)
  ✓ Subsys进行中数: 1 (一致)
  ✓ TOP通过数: 4 (一致)
  ✓ TOP待处理数: 1 (一致)
  ✓ TOP进行中数: 1 (一致)
🎉 所有统计数据完全一致!

状态值格式检查:
✓ 所有状态值格式符合标准
```

## 总结

1. **问题解决**：成功修复了仪表盘和用例管理页面统计不一致的问题
2. **格式统一**：所有状态值使用统一的标准格式
3. **逻辑优化**：实现了合理的分级别统计逻辑，避免重复计算
4. **数据一致**：两个页面现在显示完全一致的统计数据
5. **向后兼容**：保持了对现有功能的兼容性

修复后的系统能够准确、一致地显示用例统计信息，提升了用户体验和数据可靠性。
