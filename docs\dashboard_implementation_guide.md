# RunSim GUI 仪表板功能实施指南

## 1. 概述

本文档详细描述了RunSim GUI仪表板功能的Web实现方案，包括技术架构、实施步骤、文件结构和具体代码实现。

## 2. 技术选型对比

### 2.1 GUI vs Web实现对比

| 方面 | PyQt5 GUI实现 | Web实现 |
|------|---------------|---------|
| **图表库依赖** | ❌ 需要PyQtGraph/matplotlib | ✅ 使用Chart.js（无需安装） |
| **内网兼容性** | ❌ 缺少必要库 | ✅ 完全兼容 |
| **开发复杂度** | 中等 | 中等 |
| **用户体验** | 原生界面 | 现代Web界面 |
| **数据安全** | ✅ 本地数据 | ✅ 本地数据库+本地服务器 |
| **跨平台性** | 一般 | ✅ 优秀 |
| **维护成本** | 中等 | 低 |

### 2.2 最终选择：Web实现

**选择理由**：
1. **无外部依赖**：Chart.js通过CDN或本地文件加载，无需安装额外Python包
2. **丰富的图表功能**：Chart.js提供完整的图表解决方案
3. **现代化界面**：Bootstrap + Chart.js提供美观的现代界面
4. **易于扩展**：Web技术栈更容易添加新功能
5. **本地数据安全**：使用本地Flask服务器和SQLite数据库

## 3. 整体架构设计

### 3.1 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   RunSim GUI    │    │  Flask Server   │    │   Web Frontend  │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │Dashboard    │ │    │ │   API       │ │    │ │  Chart.js   │ │
│ │Plugin       │◄┼────┼►│ Routes      │◄┼────┼►│  Dashboard  │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                 │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ ┌─────────────┐ │    │ │  Database   │ │    │ │  Bootstrap  │ │
│ │Execution    │ │    │ │  Manager    │ │    │ │    UI       │ │
│ │Controller   │◄┼────┼►│             │ │    │ └─────────────┘ │
│ └─────────────┘ │    │ └─────────────┘ │    └─────────────────┘
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────────────────┘
              SQLite Database
```

### 3.2 技术栈

**后端**：
- Flask：轻量级Web框架
- SQLite：本地数据库
- openpyxl：Excel文件处理

**前端**：
- HTML5 + CSS3 + JavaScript
- Bootstrap 5：响应式UI框架
- Chart.js：图表库
- jQuery：DOM操作

## 4. 文件结构设计

```
plugins/builtin/
├── dashboard_plugin.py              # 主插件文件
└── dashboard_web/                   # Web仪表板目录
    ├── __init__.py
    ├── app.py                       # Flask应用主文件
    ├── config.py                    # 配置文件
    ├── models/                      # 数据模型
    │   ├── __init__.py
    │   ├── database.py              # 数据库管理
    │   ├── testplan.py              # TestPlan模型
    │   └── bug.py                   # BUG模型
    ├── routes/                      # API路由
    │   ├── __init__.py
    │   ├── api.py                   # API接口
    │   ├── testplan.py              # 用例管理API
    │   └── bug.py                   # BUG管理API
    ├── utils/                       # 工具类
    │   ├── __init__.py
    │   ├── excel_parser.py          # Excel解析器
    │   └── data_analyzer.py         # 数据分析器
    ├── static/                      # 静态文件
    │   ├── css/
    │   │   ├── bootstrap.min.css    # Bootstrap样式
    │   │   └── dashboard.css        # 自定义样式
    │   ├── js/
    │   │   ├── bootstrap.min.js     # Bootstrap脚本
    │   │   ├── chart.min.js         # Chart.js库
    │   │   ├── jquery.min.js        # jQuery库
    │   │   └── dashboard.js         # 仪表板脚本
    │   └── uploads/                 # 上传文件目录
    ├── templates/                   # HTML模板
    │   ├── base.html                # 基础模板
    │   ├── dashboard.html           # 仪表板页面
    │   ├── testplan.html            # 用例管理页面
    │   └── bug.html                 # BUG管理页面
    └── data/                        # 数据存储
        ├── dashboard.db             # SQLite数据库
        └── exports/                 # 导出文件目录
```

## 5. 数据库设计

### 5.1 表结构设计

```sql
-- 项目信息表
CREATE TABLE projects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    subsystem TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用例信息表
CREATE TABLE test_cases (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER,
    category TEXT,
    number TEXT,
    test_scope TEXT,
    function_point TEXT,
    test_process TEXT,
    check_point TEXT,
    coverage_point TEXT,
    case_name TEXT NOT NULL,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    actual_time INTEGER,
    subsys_stage TEXT,
    subsys_status TEXT,
    top_stage TEXT,
    top_status TEXT,
    post_subsys_stage TEXT,
    post_subsys_status TEXT,
    post_top_stage TEXT,
    post_top_status TEXT,
    remarks TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects (id)
);

-- BUG信息表
CREATE TABLE bugs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    bug_id TEXT NOT NULL,
    bug_type TEXT,
    submit_sys TEXT,
    verification_stage TEXT,
    description TEXT,
    discovery_platform TEXT,
    discovery_case TEXT,
    severity TEXT,
    status TEXT,
    submitter TEXT,
    verifier TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用例状态历史表
CREATE TABLE case_status_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    case_id INTEGER,
    old_status TEXT,
    new_status TEXT,
    stage_type TEXT,  -- 'subsys', 'top', 'post_subsys', 'post_top'
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (case_id) REFERENCES test_cases (id)
);
```

## 6. 实施阶段规划

### 6.1 阶段一：基础框架搭建（3天）

**目标**：建立Web服务基础架构

**任务清单**：
- [ ] 创建Flask应用基础结构
- [ ] 设计数据库表结构
- [ ] 实现基础的HTML模板
- [ ] 配置静态文件服务
- [ ] 创建插件集成接口

**交付物**：
- 可运行的Flask应用
- 基础的HTML页面框架
- 数据库初始化脚本

### 6.2 阶段二：Excel解析和用例管理（4天）

**目标**：实现TestPlan Excel文件的导入导出功能

**任务清单**：
- [ ] 实现Excel文件解析器
- [ ] 创建用例管理API接口
- [ ] 开发用例管理前端界面
- [ ] 实现用例状态更新机制
- [ ] 集成到RunSim GUI执行流程

**交付物**：
- Excel导入导出功能
- 用例管理界面
- 状态自动更新机制

### 6.3 阶段三：BUG管理系统（3天）

**目标**：完整的BUG记录和管理功能

**任务清单**：
- [ ] 设计BUG管理界面
- [ ] 实现BUG CRUD API
- [ ] 开发BUG统计分析功能
- [ ] 创建BUG图表展示

**交付物**：
- BUG管理界面
- BUG统计分析功能
- BUG趋势图表

### 6.4 阶段四：仪表盘可视化（4天）

**目标**：丰富的数据可视化仪表盘

**任务清单**：
- [ ] 实现项目进度图表
- [ ] 创建用例执行统计图表
- [ ] 开发实时数据更新机制
- [ ] 优化图表交互体验
- [ ] 实现数据导出功能

**交付物**：
- 完整的仪表盘界面
- 多种类型的统计图表
- 实时数据更新功能

### 6.5 阶段五：集成测试和优化（2天）

**目标**：系统集成和性能优化

**任务清单**：
- [ ] 完整的系统集成测试
- [ ] 性能优化和内存管理
- [ ] 用户界面优化
- [ ] 编写用户文档
- [ ] Bug修复和功能完善

**交付物**：
- 完整测试的系统
- 用户使用文档
- 部署指南

## 7. 核心接口设计

### 7.1 插件集成接口

```python
class DashboardPlugin(PluginBase):
    def start_web_server(self):
        """启动Web服务器"""

    def stop_web_server(self):
        """停止Web服务器"""

    def update_case_status(self, case_name, status, **kwargs):
        """更新用例状态"""

    def open_dashboard(self):
        """打开仪表板页面"""
```

### 7.2 API接口规范

```python
# 用例管理API
GET    /api/testplan/cases          # 获取用例列表
POST   /api/testplan/import         # 导入Excel文件
GET    /api/testplan/export         # 导出Excel文件
PUT    /api/testplan/case/{id}      # 更新用例状态

# BUG管理API
GET    /api/bugs                    # 获取BUG列表
POST   /api/bugs                    # 创建新BUG
PUT    /api/bugs/{id}               # 更新BUG
DELETE /api/bugs/{id}               # 删除BUG

# 仪表盘数据API
GET    /api/dashboard/progress      # 获取项目进度
GET    /api/dashboard/statistics    # 获取统计数据
GET    /api/dashboard/charts        # 获取图表数据
```

## 8. 关键技术实现

### 8.1 Flask服务器集成

```python
# 在插件中启动Flask服务器
import threading
from flask import Flask

class DashboardPlugin(PluginBase):
    def __init__(self):
        self.app = None
        self.server_thread = None

    def start_web_server(self):
        if self.server_thread is None:
            self.server_thread = threading.Thread(
                target=self._run_server, daemon=True
            )
            self.server_thread.start()

    def _run_server(self):
        from dashboard_web.app import create_app
        self.app = create_app()
        self.app.run(host='127.0.0.1', port=5000, debug=False)
```

### 8.2 实时数据更新

```javascript
// 前端实时数据更新
function updateDashboard() {
    fetch('/api/dashboard/statistics')
        .then(response => response.json())
        .then(data => {
            updateCharts(data);
        });
}

// 每30秒更新一次数据
setInterval(updateDashboard, 30000);
```

## 9. 部署和配置

### 9.1 依赖安装

```bash
# 仅需要Flask和openpyxl
pip install flask openpyxl
```

### 9.2 配置文件

```python
# config.py
class Config:
    SECRET_KEY = 'dashboard-secret-key'
    DATABASE_PATH = 'data/dashboard.db'
    UPLOAD_FOLDER = 'static/uploads'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
```

## 10. 风险评估和解决方案

### 10.1 主要风险

1. **端口冲突**：Flask服务器端口被占用
   - 解决方案：动态端口分配，检测端口可用性

2. **数据同步**：Web界面与RunSim GUI数据同步
   - 解决方案：使用SQLite WAL模式，实现事件通知机制

3. **性能问题**：大量数据时Web界面响应慢
   - 解决方案：实现分页加载，数据缓存机制

### 10.2 安全考虑

1. **本地访问限制**：仅允许本地访问
2. **文件上传安全**：限制文件类型和大小
3. **SQL注入防护**：使用参数化查询

## 11. 具体实现示例

### 11.1 插件主文件实现

```python
# plugins/builtin/dashboard_plugin.py
import os
import sys
import threading
import webbrowser
from PyQt5.QtWidgets import QAction, QMessageBox
from plugins.base import PluginBase

class DashboardPlugin(PluginBase):
    """仪表板插件"""

    @property
    def name(self):
        return "项目仪表板"

    @property
    def version(self):
        return "1.0.0"

    @property
    def description(self):
        return "项目管理仪表板，包括用例管理、BUG记录和进度展示"

    def __init__(self):
        super().__init__()
        self.server_thread = None
        self.server_port = 5000
        self.main_window = None

    def initialize(self, main_window):
        """初始化插件"""
        try:
            self.main_window = main_window

            # 创建菜单项
            self.menu_action = QAction(self.name, main_window)
            self.menu_action.setStatusTip(self.description)
            self.menu_action.triggered.connect(self.open_dashboard)

            # 添加到工具菜单
            if hasattr(main_window, 'tools_menu'):
                main_window.tools_menu.addAction(self.menu_action)

            # 启动Web服务器
            self.start_web_server()

            print(f"成功初始化插件: {self.name}")

        except Exception as e:
            print(f"初始化插件 {self.name} 失败: {str(e)}")

    def start_web_server(self):
        """启动Web服务器"""
        if self.server_thread is None or not self.server_thread.is_alive():
            self.server_thread = threading.Thread(
                target=self._run_server, daemon=True
            )
            self.server_thread.start()

    def _run_server(self):
        """运行Flask服务器"""
        try:
            # 添加dashboard_web目录到Python路径
            dashboard_path = os.path.join(os.path.dirname(__file__), 'dashboard_web')
            if dashboard_path not in sys.path:
                sys.path.insert(0, dashboard_path)

            from dashboard_web.app import create_app
            app = create_app()

            # 查找可用端口
            import socket
            for port in range(5000, 5010):
                try:
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.bind(('127.0.0.1', port))
                    sock.close()
                    self.server_port = port
                    break
                except OSError:
                    continue

            app.run(host='127.0.0.1', port=self.server_port, debug=False)

        except Exception as e:
            print(f"启动Web服务器失败: {str(e)}")

    def open_dashboard(self):
        """打开仪表板页面"""
        try:
            url = f"http://127.0.0.1:{self.server_port}"
            webbrowser.open(url)
        except Exception as e:
            QMessageBox.warning(
                self.main_window, "错误",
                f"无法打开仪表板页面: {str(e)}"
            )

    def update_case_status(self, case_name, status, **kwargs):
        """更新用例状态"""
        try:
            # 通过API更新用例状态
            import requests
            url = f"http://127.0.0.1:{self.server_port}/api/testplan/update_status"
            data = {
                'case_name': case_name,
                'status': status,
                **kwargs
            }
            requests.post(url, json=data, timeout=5)
        except Exception as e:
            print(f"更新用例状态失败: {str(e)}")

    def cleanup(self):
        """清理插件资源"""
        # Web服务器会随着主进程结束而结束
        pass
```

### 11.2 Flask应用主文件

```python
# plugins/builtin/dashboard_web/app.py
import os
from flask import Flask, render_template, request, jsonify
from models.database import init_db, get_db
from routes.api import api_bp
from routes.testplan import testplan_bp
from routes.bug import bug_bp

def create_app():
    """创建Flask应用"""
    app = Flask(__name__)

    # 配置
    app.config['SECRET_KEY'] = 'dashboard-secret-key'
    app.config['DATABASE_PATH'] = os.path.join(
        os.path.dirname(__file__), 'data', 'dashboard.db'
    )
    app.config['UPLOAD_FOLDER'] = os.path.join(
        os.path.dirname(__file__), 'static', 'uploads'
    )
    app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB

    # 确保目录存在
    os.makedirs(os.path.dirname(app.config['DATABASE_PATH']), exist_ok=True)
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

    # 初始化数据库
    init_db(app.config['DATABASE_PATH'])

    # 注册蓝图
    app.register_blueprint(api_bp, url_prefix='/api')
    app.register_blueprint(testplan_bp, url_prefix='/testplan')
    app.register_blueprint(bug_bp, url_prefix='/bug')

    @app.route('/')
    def index():
        """主页"""
        return render_template('dashboard.html')

    @app.route('/testplan')
    def testplan():
        """用例管理页面"""
        return render_template('testplan.html')

    @app.route('/bug')
    def bug():
        """BUG管理页面"""
        return render_template('bug.html')

    return app
```

### 11.3 数据库管理

```python
# plugins/builtin/dashboard_web/models/database.py
import sqlite3
import os
from contextlib import contextmanager

def init_db(db_path):
    """初始化数据库"""
    with sqlite3.connect(db_path) as conn:
        cursor = conn.cursor()

        # 创建项目表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS projects (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                subsystem TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 创建用例表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_cases (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                project_id INTEGER,
                category TEXT,
                number TEXT,
                test_scope TEXT,
                function_point TEXT,
                test_process TEXT,
                check_point TEXT,
                coverage_point TEXT,
                case_name TEXT NOT NULL,
                start_time TIMESTAMP,
                end_time TIMESTAMP,
                actual_time INTEGER,
                subsys_stage TEXT,
                subsys_status TEXT,
                top_stage TEXT,
                top_status TEXT,
                post_subsys_stage TEXT,
                post_subsys_status TEXT,
                post_top_stage TEXT,
                post_top_status TEXT,
                remarks TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (project_id) REFERENCES projects (id)
            )
        ''')

        # 创建BUG表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS bugs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                bug_id TEXT NOT NULL,
                bug_type TEXT,
                submit_sys TEXT,
                verification_stage TEXT,
                description TEXT,
                discovery_platform TEXT,
                discovery_case TEXT,
                severity TEXT,
                status TEXT,
                submitter TEXT,
                verifier TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # 创建用例状态历史表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS case_status_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                case_id INTEGER,
                old_status TEXT,
                new_status TEXT,
                stage_type TEXT,
                changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (case_id) REFERENCES test_cases (id)
            )
        ''')

        conn.commit()

@contextmanager
def get_db():
    """获取数据库连接"""
    from flask import current_app
    conn = sqlite3.connect(current_app.config['DATABASE_PATH'])
    conn.row_factory = sqlite3.Row
    try:
        yield conn
    finally:
        conn.close()
```

### 11.4 前端仪表板页面

```html
<!-- plugins/builtin/dashboard_web/templates/dashboard.html -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RunSim 项目仪表板</title>
    <link href="{{ url_for('static', filename='css/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/dashboard.css') }}" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">RunSim 仪表板</a>
            <div class="navbar-nav">
                <a class="nav-link active" href="/">仪表板</a>
                <a class="nav-link" href="/testplan">用例管理</a>
                <a class="nav-link" href="/bug">BUG管理</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-white bg-primary">
                    <div class="card-body">
                        <h5 class="card-title">总用例数</h5>
                        <h2 id="total-cases">0</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-success">
                    <div class="card-body">
                        <h5 class="card-title">通过用例</h5>
                        <h2 id="passed-cases">0</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-danger">
                    <div class="card-body">
                        <h5 class="card-title">失败用例</h5>
                        <h2 id="failed-cases">0</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-warning">
                    <div class="card-body">
                        <h5 class="card-title">进行中</h5>
                        <h2 id="running-cases">0</h2>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>项目进度</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="progressChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>用例状态分布</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="statusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5>BUG趋势</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="bugTrendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/bootstrap.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/chart.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>
</body>
</html>
```

### 11.5 前端JavaScript实现

```javascript
// plugins/builtin/dashboard_web/static/js/dashboard.js
$(document).ready(function() {
    // 初始化图表
    initCharts();

    // 加载数据
    loadDashboardData();

    // 设置定时刷新
    setInterval(loadDashboardData, 30000); // 30秒刷新一次
});

function initCharts() {
    // 项目进度图表
    const progressCtx = document.getElementById('progressChart').getContext('2d');
    window.progressChart = new Chart(progressCtx, {
        type: 'doughnut',
        data: {
            labels: ['子系统级', 'TOP级', '后仿'],
            datasets: [{
                data: [0, 0, 0],
                backgroundColor: ['#36A2EB', '#4BC0C0', '#9966FF']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // 用例状态分布图表
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    window.statusChart = new Chart(statusCtx, {
        type: 'pie',
        data: {
            labels: ['Pass', 'Fail', 'On-Going', 'Not Started'],
            datasets: [{
                data: [0, 0, 0, 0],
                backgroundColor: ['#4CAF50', '#F44336', '#FF9800', '#9E9E9E']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // BUG趋势图表
    const bugTrendCtx = document.getElementById('bugTrendChart').getContext('2d');
    window.bugTrendChart = new Chart(bugTrendCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '新增BUG',
                data: [],
                borderColor: '#F44336',
                backgroundColor: 'rgba(244, 67, 54, 0.1)',
                tension: 0.4
            }, {
                label: '已修复BUG',
                data: [],
                borderColor: '#4CAF50',
                backgroundColor: 'rgba(76, 175, 80, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function loadDashboardData() {
    // 加载统计数据
    $.get('/api/dashboard/statistics', function(data) {
        $('#total-cases').text(data.total_cases || 0);
        $('#passed-cases').text(data.passed_cases || 0);
        $('#failed-cases').text(data.failed_cases || 0);
        $('#running-cases').text(data.running_cases || 0);
    });

    // 加载项目进度数据
    $.get('/api/dashboard/progress', function(data) {
        window.progressChart.data.datasets[0].data = [
            data.subsys_progress || 0,
            data.top_progress || 0,
            data.post_progress || 0
        ];
        window.progressChart.update();
    });

    // 加载用例状态分布数据
    $.get('/api/dashboard/case_status', function(data) {
        window.statusChart.data.datasets[0].data = [
            data.pass || 0,
            data.fail || 0,
            data.ongoing || 0,
            data.not_started || 0
        ];
        window.statusChart.update();
    });

    // 加载BUG趋势数据
    $.get('/api/dashboard/bug_trend', function(data) {
        window.bugTrendChart.data.labels = data.labels || [];
        window.bugTrendChart.data.datasets[0].data = data.new_bugs || [];
        window.bugTrendChart.data.datasets[1].data = data.fixed_bugs || [];
        window.bugTrendChart.update();
    });
}
```

这个实施指南提供了完整的Web实现方案，既解决了内网环境缺少图表库的问题，又能提供丰富的仪表板功能。通过Flask轻量级Web服务器和Chart.js图表库，可以实现功能完整、界面美观的项目管理仪表板。
