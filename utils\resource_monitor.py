"""
系统资源监控模块
"""
import psutil
import threading
import time
from PyQt5.QtCore import QObject, pyqtSignal

class ResourceMonitor(QObject):
    """系统资源监控类，使用后台线程监控系统资源"""

    # 定义信号
    resources_updated = pyqtSignal(bool, float, float)  # high_usage, cpu_percent, mem_percent

    def __init__(self):
        """初始化资源监控器"""
        super().__init__()
        self._cpu_percent = 0
        self._mem_percent = 0
        self._high_usage = False
        self._lock = threading.Lock()
        self._running = False
        self._thread = None
        self._update_interval = 2.0  # 更新间隔（秒）

    def start_monitoring(self, interval=2.0):
        """
        开始监控系统资源

        Args:
            interval (float): 监控间隔（秒）
        """
        if self._running:
            return

        self._update_interval = interval
        self._running = True
        self._thread = threading.Thread(target=self._monitor_resources, daemon=True)
        self._thread.start()

    def stop_monitoring(self):
        """停止监控系统资源"""
        self._running = False
        # 只在非当前线程时尝试join，避免"cannot join current thread"错误
        if self._thread and self._thread.is_alive() and self._thread is not threading.current_thread():
            try:
                self._thread.join(timeout=1.0)
            except RuntimeError:
                # 忽略"cannot join current thread"错误
                pass

    def _monitor_resources(self):
        """监控系统资源的后台线程"""
        while self._running:
            try:
                # 非阻塞方式获取CPU使用率
                cpu_percent = psutil.cpu_percent(interval=None)
                # 如果是第一次调用，可能返回0，再次获取
                if cpu_percent == 0:
                    time.sleep(0.1)
                    cpu_percent = psutil.cpu_percent(interval=None)

                mem_percent = psutil.virtual_memory().percent
                high_usage = cpu_percent > 80 or mem_percent > 80

                with self._lock:
                    self._cpu_percent = cpu_percent
                    self._mem_percent = mem_percent
                    self._high_usage = high_usage

                # 发出信号通知资源更新
                self.resources_updated.emit(high_usage, cpu_percent, mem_percent)

            except Exception as e:
                print(f"资源监控错误: {str(e)}")

            # 等待下一次更新
            time.sleep(self._update_interval)

    def check_resources(self):
        """
        检查系统资源使用情况（非阻塞）

        Returns:
            tuple: (high_usage, cpu_percent, mem_percent)
        """
        with self._lock:
            return self._high_usage, self._cpu_percent, self._mem_percent

    def __del__(self):
        """析构函数，确保线程被正确终止"""
        try:
            # 只设置运行标志为False，不调用stop_monitoring方法
            # 这样可以避免在析构函数中join线程导致的错误
            self._running = False
        except Exception:
            # 忽略析构函数中的所有错误
            pass
