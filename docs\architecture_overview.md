# RunSim GUI 架构说明

## 1. 整体架构

RunSim GUI 采用 MVC（Model-View-Controller）架构模式，将应用程序分为数据模型、视图和控制器三个部分，实现了关注点分离，提高了代码的可维护性和可扩展性。

### 1.1 架构图

```
app.py (应用程序入口)
  ├── controllers/app_controller.py (应用程序控制器)
  │     ├── controllers/case_controller.py (用例控制器)
  │     │     ├── models/case_model.py (用例数据模型)
  │     │     └── views/case_panel.py (用例管理面板)
  │     ├── controllers/config_controller.py (配置控制器)
  │     │     ├── models/config_model.py (配置数据模型)
  │     │     └── views/config_panel.py (配置面板)
  │     ├── controllers/execution_controller.py (执行控制器)
  │     │     ├── models/command_model.py (命令生成模型)
  │     │     ├── models/history_model.py (历史记录模型)
  │     │     ├── views/execution_panel.py (执行面板)
  │     │     └── views/log_panel.py (日志面板)
  │     └── views/main_window.py (主窗口)
  ├── utils/ (工具类)
  │     ├── resource_monitor.py (资源监控)
  │     ├── case_parser.py (用例文件解析器)
  │     ├── command_generator.py (命令生成器)
  │     ├── path_resolver.py (路径解析器)
  │     ├── event_bus.py (事件总线)
  │     └── resource_optimizer.py (资源优化器)
  ├── plugins/ (插件系统)
  │     ├── manager.py (插件管理器)
  │     ├── base.py (插件基类)
  │     ├── builtin/ (内置插件)
  │     └── user/ (用户插件)
```

### 1.2 模块职责

#### 1.2.1 模型（Models）

模型层负责管理应用程序的数据和业务逻辑，包括：

- **config_model.py**: 配置数据模型，负责管理应用程序配置
- **case_model.py**: 用例数据模型，负责管理用例数据
- **command_model.py**: 命令生成模型，负责生成执行命令
- **history_model.py**: 历史记录模型，负责管理历史记录

#### 1.2.2 视图（Views）

视图层负责用户界面的显示和交互，包括：

- **main_window.py**: 主窗口，提供应用程序的主框架
- **case_panel.py**: 用例管理面板，显示用例树和提供用例管理功能
- **config_panel.py**: 配置面板，显示和编辑配置参数
- **execution_panel.py**: 执行面板，显示执行日志和提供执行控制功能
- **log_panel.py**: 日志面板，显示执行日志

#### 1.2.3 控制器（Controllers）

控制器层负责协调模型和视图，处理用户输入和应用程序逻辑，包括：

- **app_controller.py**: 应用程序控制器，负责协调各个控制器和视图
- **case_controller.py**: 用例控制器，负责管理用例相关操作
- **config_controller.py**: 配置控制器，负责管理配置相关操作
- **execution_controller.py**: 执行控制器，负责管理执行相关操作

#### 1.2.4 工具类（Utils）

工具类提供通用功能和辅助功能，包括：

- **resource_monitor.py**: 资源监控，监控系统资源使用情况
- **case_parser.py**: 用例文件解析器，解析用例文件
- **command_generator.py**: 命令生成器，生成执行命令
- **path_resolver.py**: 路径解析器，解析和处理文件路径
- **event_bus.py**: 事件总线，实现模块间的通信
- **resource_optimizer.py**: 资源优化器，优化资源使用

#### 1.2.5 插件系统（Plugins）

插件系统提供扩展功能，包括：

- **manager.py**: 插件管理器，负责加载和管理插件
- **base.py**: 插件基类，定义插件接口
- **builtin/**: 内置插件，提供基本功能
- **user/**: 用户插件，用户自定义的插件

## 2. 数据流

### 2.1 用例加载流程

1. 用户在用例管理面板中选择用例文件
2. 用例控制器调用用例解析器解析用例文件
3. 用例数据模型存储解析后的用例数据
4. 用例控制器通过事件总线通知配置控制器用例已选择
5. 配置控制器更新配置面板中的用例信息

### 2.2 命令执行流程

1. 用户在配置面板中设置参数并点击执行按钮
2. 配置控制器收集参数并调用命令生成模型生成命令
3. 配置控制器通过事件总线通知执行控制器执行命令
4. 执行控制器创建新的日志标签页并执行命令
5. 执行控制器将执行结果显示在日志面板中
6. 执行控制器通知历史记录模型更新历史记录

## 3. 设计模式

### 3.1 MVC 模式

应用程序整体采用 MVC 模式，将数据、视图和控制逻辑分离。

### 3.2 观察者模式

使用 PyQt 的信号和槽机制实现观察者模式，当数据发生变化时通知相关组件。

### 3.3 单例模式

事件总线采用单例模式，确保全局只有一个事件总线实例。

### 3.4 工厂模式

插件系统使用工厂模式创建插件实例。

### 3.5 策略模式

命令生成器使用策略模式，根据不同的配置生成不同的命令。

## 4. 扩展点

### 4.1 插件系统

插件系统是主要的扩展点，用户可以通过创建自定义插件扩展应用程序功能。

### 4.2 命令生成器

命令生成器可以扩展以支持新的命令格式和参数。

### 4.3 用例解析器

用例解析器可以扩展以支持新的用例文件格式。

## 5. 性能优化

### 5.1 异步处理

使用异步处理机制处理耗时操作，避免阻塞 UI 线程。

### 5.2 资源优化

使用资源优化器监控和优化资源使用，减少内存和 CPU 占用。

### 5.3 缓存机制

使用缓存机制减少重复计算和文件读取，提高性能。
