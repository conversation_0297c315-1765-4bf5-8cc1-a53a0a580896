"""
Excel文件导出器

该模块负责将用例数据导出为Excel文件。
"""

import os
import tempfile
import logging
from datetime import datetime
from typing import List, Dict, Any
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter

# 配置日志
logger = logging.getLogger(__name__)

class TestPlanExporter:
    """TestPlan Excel文件导出器"""

    # 导出列定义 - 与TestPlan_Template.py标准格式保持一致
    EXPORT_COLUMNS = [
        {'field': 'category', 'header': 'Test Category', 'width': 15},
        {'field': 'number', 'header': 'Items', 'width': 20},
        {'field': 'test_areas', 'header': 'Test Areas', 'width': 25},
        {'field': 'function_point', 'header': 'Function points', 'width': 35},
        {'field': 'test_scope', 'header': 'Test Scope', 'width': 15},
        {'field': 'check_point', 'header': 'Check Point', 'width': 15},
        {'field': 'coverage_point', 'header': 'Cover', 'width': 15},
        {'field': 'case_name', 'header': 'TestCase Name', 'width': 20},
        {'field': 'start_time', 'header': 'Start Time', 'width': 12},
        {'field': 'end_time', 'header': 'End Time', 'width': 12},
        {'field': 'actual_time', 'header': 'Actual Time', 'width': 12},
        {'field': 'owner', 'header': 'Owner', 'width': 8},
        {'field': 'subsys_stage', 'header': 'Phase', 'width': 8},
        {'field': 'subsys_status', 'header': 'Status', 'width': 8},
        {'field': 'top_stage', 'header': 'Phase', 'width': 8},
        {'field': 'top_status', 'header': 'Status', 'width': 8},
        {'field': 'post_subsys_phase', 'header': 'Phase', 'width': 8},  # 修正列名
        {'field': 'post_subsys_status', 'header': 'Status', 'width': 8},
        {'field': 'post_top_phase', 'header': 'Phase', 'width': 8},  # 修正列名
        {'field': 'post_top_status', 'header': 'Status', 'width': 8},
        {'field': 'remarks', 'header': 'Note', 'width': 15},
    ]

    # 状态颜色映射 - 与TestPlan标准格式一致
    STATUS_COLORS = {
        'PASS': '00FF00',      # 绿色
        'FAIL': 'FF0000',      # 红色
        'N/A': 'C0C0C0',       # 灰色
        'Pass': '00FF00',      # 兼容旧格式
        'Fail': 'FF0000',      # 兼容旧格式
        'On-Going': 'FFEB9C',  # 黄色
        'Not Started': 'C0C0C0'  # 灰色
    }

    def __init__(self):
        """初始化导出器"""
        self.workbook = None
        self.worksheet = None

    def export_to_excel(self, test_cases: List[Dict[str, Any]],
                       file_format: str = 'xlsx') -> str:
        """
        导出用例数据到Excel文件

        Args:
            test_cases: 用例数据列表
            file_format: 文件格式 ('xlsx' 或 'xls')

        Returns:
            str: 临时文件路径
        """
        try:
            logger.info(f"开始导出 {len(test_cases)} 条用例数据")

            # 创建工作簿
            self.workbook = openpyxl.Workbook()
            self.worksheet = self.workbook.active
            self.worksheet.title = "TP"

            # 写入项目信息
            self._write_project_info()

            # 写入表头
            self._write_header()

            # 写入数据
            self._write_data(test_cases)

            # 应用样式
            self._apply_styles()

            # 保存到临时文件
            temp_path = self._save_to_temp_file(file_format)

            logger.info(f"导出完成，文件保存至: {temp_path}")
            return temp_path

        except Exception as e:
            logger.error(f"导出Excel文件失败: {str(e)}")
            raise
        finally:
            if self.workbook:
                self.workbook.close()

    def _write_project_info(self) -> None:
        """写入项目信息"""
        # 第一行：项目信息
        self.worksheet['A1'] = 'Project: Example_SOC'
        self.worksheet.merge_cells('A1:U1')

        # 第二行：子系统信息
        self.worksheet['A2'] = 'Subsystem: ADCV2_DV'
        self.worksheet.merge_cells('A2:U2')

    def _write_header(self) -> None:
        """写入双行表头"""
        # 第三行表头
        headers_row3 = [
            'Test Category', 'Items', 'Test Areas', 'Function points', 'Test Scope', 'Check Point',
            'Cover', 'TestCase Name', 'Start Time', 'End Time', 'Actual Time', 'Owner',
            'Subsys', '', 'TOP', '', 'POST_Subsys', '', 'POST_TOP', '', 'Note'
        ]

        # 第四行表头
        headers_row4 = [
            '', '', '', '', '', '', '', '', '', '', '', '',
            'Phase', 'Status', 'Phase', 'Status', 'Phase', 'Status', 'Phase', 'Status', ''
        ]

        # 写入第三行表头
        for col, header in enumerate(headers_row3, 1):
            cell = self.worksheet.cell(row=3, column=col, value=header)

        # 写入第四行表头
        for col, header in enumerate(headers_row4, 1):
            cell = self.worksheet.cell(row=4, column=col, value=header)

        # 合并表头单元格
        merge_ranges = [
            'A3:A4', 'B3:B4', 'C3:C4', 'D3:D4', 'E3:E4', 'F3:F4', 'G3:G4', 'H3:H4',
            'I3:I4', 'J3:J4', 'K3:K4', 'L3:L4', 'M3:N3', 'O3:P3', 'Q3:R3', 'S3:T3', 'U3:U4'
        ]

        for range_str in merge_ranges:
            self.worksheet.merge_cells(range_str)

        # 设置列宽
        column_widths = {
            'A': 15, 'B': 20, 'C': 25, 'D': 35, 'E': 15, 'F': 15, 'G': 15, 'H': 20,
            'I': 12, 'J': 12, 'K': 12, 'L': 8, 'M': 8, 'N': 8, 'O': 8, 'P': 8,
            'Q': 8, 'R': 8, 'S': 8, 'T': 8, 'U': 15
        }

        for col_letter, width in column_widths.items():
            self.worksheet.column_dimensions[col_letter].width = width

    def _write_data(self, test_cases: List[Dict[str, Any]]) -> None:
        """写入数据行"""
        for row_idx, case in enumerate(test_cases, 5):  # 从第5行开始（前4行是项目信息和表头）
            # 按照标准TestPlan格式的列顺序写入数据
            data_mapping = [
                case.get('category', ''),
                case.get('number', ''),
                case.get('test_areas', case.get('test_scope', '')),  # test_areas优先，fallback到test_scope
                case.get('function_point', ''),
                case.get('test_scope', ''),
                case.get('check_point', ''),
                case.get('coverage_point', ''),
                case.get('case_name', ''),
                self._format_time(case.get('start_time', '')),
                self._format_time(case.get('end_time', '')),
                self._format_actual_time(case.get('actual_time', '')),
                case.get('owner', ''),
                case.get('subsys_stage', ''),
                case.get('subsys_status', ''),
                case.get('top_stage', ''),
                case.get('top_status', ''),
                case.get('post_subsys_phase', ''),  # 修正列名
                case.get('post_subsys_status', ''),
                case.get('post_top_phase', ''),  # 修正列名
                case.get('post_top_status', ''),
                case.get('remarks', '')
            ]

            for col_idx, value in enumerate(data_mapping, 1):
                cell = self.worksheet.cell(row=row_idx, column=col_idx)
                cell.value = str(value) if value else ''

    def _format_time(self, value):
        """格式化时间字段"""
        if value:
            if isinstance(value, str):
                try:
                    # 尝试解析时间字符串
                    dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
                    return dt.strftime('%Y/%m/%d')
                except:
                    return str(value)
            else:
                return str(value)
        return ''

    def _format_actual_time(self, value):
        """格式化实际时间字段"""
        if value:
            try:
                return str(int(value))
            except:
                return str(value)
        return ''

    def _apply_styles(self) -> None:
        """应用样式"""
        # 定义样式
        header_font = Font(name='Arial', size=10, bold=True, color='000000')
        normal_font = Font(name='Arial', size=9, color='000000')
        yellow_fill = PatternFill(start_color='FFFF00', end_color='FFFF00', fill_type='solid')

        # 边框样式
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # 对齐样式
        center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
        left_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)

        # 应用项目信息样式（第1-2行）
        for row in [1, 2]:
            for col in range(1, 22):  # A-U列
                cell = self.worksheet.cell(row=row, column=col)
                cell.font = header_font
                cell.fill = yellow_fill
                cell.alignment = center_alignment
                cell.border = thin_border

        # 应用表头样式（第3-4行）
        for row in [3, 4]:
            for col in range(1, 22):  # A-U列
                cell = self.worksheet.cell(row=row, column=col)
                cell.font = header_font
                cell.fill = yellow_fill
                cell.alignment = center_alignment
                cell.border = thin_border

        # 应用数据行样式
        max_row = self.worksheet.max_row
        for row_idx in range(5, max_row + 1):  # 从第5行开始
            for col_idx in range(1, 22):  # A-U列
                cell = self.worksheet.cell(row=row_idx, column=col_idx)
                cell.font = normal_font
                cell.border = thin_border

                # 设置对齐方式
                if col_idx in [1, 2, 3, 4]:  # 文本列左对齐
                    cell.alignment = left_alignment
                else:  # 其他列居中对齐
                    cell.alignment = center_alignment

                # 状态列着色（列N, P, R, T对应Status列）
                if col_idx in [14, 16, 18, 20]:  # Status列
                    status = str(cell.value).strip()
                    if status in self.STATUS_COLORS:
                        color = self.STATUS_COLORS[status]
                        cell.fill = PatternFill(start_color=color, end_color=color, fill_type='solid')

        # 设置行高
        for row in range(1, max_row + 1):
            self.worksheet.row_dimensions[row].height = 25

        # 冻结表头
        self.worksheet.freeze_panes = 'A5'

    def _save_to_temp_file(self, file_format: str) -> str:
        """保存到临时文件"""
        # 生成临时文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        temp_dir = tempfile.gettempdir()
        temp_filename = f"testplan_export_{timestamp}.{file_format}"
        temp_path = os.path.join(temp_dir, temp_filename)

        # 保存文件
        self.workbook.save(temp_path)

        return temp_path

    @staticmethod
    def export_template(file_format: str = 'xlsx') -> str:
        """
        导出用例模板文件

        Args:
            file_format: 文件格式

        Returns:
            str: 临时文件路径
        """
        try:
            logger.info("开始导出用例模板")

            # 创建工作簿
            workbook = openpyxl.Workbook()
            worksheet = workbook.active
            worksheet.title = "TP"

            # 创建临时导出器实例来使用其方法
            temp_exporter = TestPlanExporter()
            temp_exporter.workbook = workbook
            temp_exporter.worksheet = worksheet

            # 写入项目信息
            temp_exporter._write_project_info()

            # 写入表头
            temp_exporter._write_header()

            # 添加示例数据
            example_data = [{
                'category': 'MEM',
                'number': 'APC_MEM_BIST_001',
                'test_areas': 'Memory BIST',
                'function_point': 'Function:内存BIST功能验证\nFeature:验证内存BIST基本功能',
                'test_scope': 'BIST测试流程',
                'check_point': '检查BIST结果',
                'coverage_point': 'BIST功能覆盖',
                'case_name': 'apc_mem_bist_001',
                'start_time': '2024/03/15',
                'end_time': '2024/03/16',
                'actual_time': '',
                'owner': 'Owner1',
                'subsys_stage': 'DVR1',
                'subsys_status': 'PASS',
                'top_stage': 'N/A',
                'top_status': 'N/A',
                'post_subsys_stage': 'N/A',
                'post_subsys_status': 'N/A',
                'post_top_stage': 'N/A',
                'post_top_status': 'N/A',
                'remarks': ''
            }]

            # 写入示例数据
            temp_exporter._write_data(example_data)

            # 应用样式
            temp_exporter._apply_styles()

            # 保存到临时文件
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            temp_dir = tempfile.gettempdir()
            temp_filename = f"testplan_template_{timestamp}.{file_format}"
            temp_path = os.path.join(temp_dir, temp_filename)

            workbook.save(temp_path)
            workbook.close()

            logger.info(f"模板导出完成: {temp_path}")
            return temp_path

        except Exception as e:
            logger.error(f"导出模板失败: {str(e)}")
            raise
