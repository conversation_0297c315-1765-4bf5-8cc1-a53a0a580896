from PyQt5.QtWidgets import (QAction, QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                           QTableWidget, QTableWidgetItem, QLabel, QProgressBar,
                           QLineEdit, QMessageBox, QHeaderView, QComboBox, QTreeWidget,
                           QTreeWidgetItem, QSplitter, QFrame, QGroupBox, QTabWidget,
                           QFileDialog, QApplication, QToolButton)
from PyQt5.QtCore import Qt, QProcess, QSize, pyqtSignal
from PyQt5.QtGui import QColor, QFont, QIcon
import os
import re
import glob
import shutil
import time
from datetime import datetime
from plugins.base import PluginBase



class RegressionResultAnalyzerPlugin(PluginBase):
    """回归结果解析插件"""

    # 定义信号
    task_progress = pyqtSignal(str)
    task_completed = pyqtSignal()

    @property
    def name(self):
        return "回归结果解析器"

    @property
    def version(self):
        return "1.0.0"

    @property
    def description(self):
        return "解析回归结果文件，展示PASS和FAIL用例"

    def __init__(self):
        super().__init__()
        # 设置为管理员控制
        self.admin_controlled = True
        # 设置默认为禁用状态
        self.default_enabled = False
        # 对话框引用
        self.current_dialog = None

    def initialize(self, main_window):
        """初始化插件"""
        try:
            self.main_window = main_window

            # 创建菜单项
            self.menu_action = QAction(self.name, main_window)
            self.menu_action.setStatusTip(self.description)
            self.menu_action.triggered.connect(self.show_analyzer)

            # 添加到工具菜单
            if hasattr(main_window, 'tools_menu'):
                self.main_window.tools_menu.addSeparator()
                self.main_window.tools_menu.addAction(self.menu_action)

            # 创建状态指示器（初始不可见）
            self.create_status_indicator()

            print(f"成功初始化插件: {self.name}")

        except Exception as e:
            print(f"初始化插件 {self.name} 失败: {str(e)}")

    def create_status_indicator(self):
        """创建状态指示器"""
        try:
            # 创建状态指示器标签
            self.status_label = QLabel("回归结果解析器运行中")
            self.status_label.setVisible(False)  # 初始不可见

            # 创建一个工具按钮，可以点击
            self.status_button = QToolButton()
            self.status_button.setText("查看")
            self.status_button.setVisible(False)  # 初始不可见

            # 添加点击事件，显示对话框
            self.status_button.clicked.connect(self.show_running_dialog)

            # 添加到状态栏
            if hasattr(self.main_window, 'status_bar'):
                self.main_window.status_bar.addPermanentWidget(QLabel(""))  # 添加间隔
                self.main_window.status_bar.addPermanentWidget(self.status_label)
                self.main_window.status_bar.addPermanentWidget(self.status_button)
        except Exception as e:
            print(f"创建状态指示器失败: {str(e)}")

    def show_running_dialog(self):
        """显示正在运行的对话框"""
        if self.current_dialog:
            # 如果对话框存在但被隐藏，则显示它
            if not self.current_dialog.isVisible():
                self.current_dialog.showNormal()
            # 如果对话框被最小化，则恢复它
            elif self.current_dialog.isMinimized():
                self.current_dialog.showNormal()
            # 将对话框置于前台
            self.current_dialog.raise_()
            self.current_dialog.activateWindow()

    def run_in_background(self, dialog):
        """将窗口隐藏到后台运行"""
        if dialog:
            # 隐藏窗口但不关闭
            dialog.hide()

            # 确保状态指示器可见
            if hasattr(self, 'status_label'):
                self.status_label.setVisible(True)
            if hasattr(self, 'status_button'):
                self.status_button.setVisible(True)

            # 在主窗口状态栏显示提示信息
            if hasattr(self.main_window, 'show_message'):
                self.main_window.show_message("回归结果解析器正在后台运行", 5000)

    def on_dialog_closed(self):
        """处理对话框关闭事件"""
        # 隐藏状态指示器
        if hasattr(self, 'status_label'):
            self.status_label.setVisible(False)
        if hasattr(self, 'status_button'):
            self.status_button.setVisible(False)

        # 清理对话框引用
        self.current_dialog = None

    def cleanup(self):
        """清理插件资源"""
        if hasattr(self, 'menu_action') and hasattr(self.main_window, 'tools_menu'):
            try:
                self.main_window.tools_menu.removeAction(self.menu_action)
            except Exception as e:
                print(f"清理插件菜单失败: {str(e)}")

        # 清理状态指示器
        if hasattr(self.main_window, 'status_bar'):
            try:
                # 清理标签
                if hasattr(self, 'status_label'):
                    self.status_label.setVisible(False)
                    self.main_window.status_bar.removeWidget(self.status_label)

                # 清理按钮
                if hasattr(self, 'status_button'):
                    self.status_button.setVisible(False)
                    self.main_window.status_bar.removeWidget(self.status_button)
            except Exception as e:
                print(f"清理状态指示器失败: {str(e)}")

        # 关闭对话框
        if hasattr(self, 'current_dialog') and self.current_dialog:
            try:
                self.current_dialog.close()
            except Exception as e:
                print(f"关闭对话框失败: {str(e)}")

    def show_analyzer(self):
        """显示回归结果解析器"""
        dialog = RegressionResultDialog(self.main_window)

        # 设置窗口标志，添加最小化按钮，并确保窗口不会始终保持在最上层
        dialog.setWindowFlags(Qt.Window | Qt.WindowMinimizeButtonHint | Qt.WindowCloseButtonHint)

        # 保存对话框引用
        self.current_dialog = dialog

        # 连接对话框关闭事件
        dialog.finished.connect(self.on_dialog_closed)

        # 显示状态指示器
        if hasattr(self, 'status_label'):
            self.status_label.setVisible(True)
        if hasattr(self, 'status_button'):
            self.status_button.setVisible(True)

        # 非模态显示对话框
        dialog.show()

class RegressionResultDialog(QDialog):
    """回归结果解析对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("回归结果解析器")
        self.resize(1200, 800)
        self.regression_data = {}  # 存储回归数据
        self.current_timestamp = None  # 当前选中的时间戳
        self.current_group = None  # 当前选中的用例组
        self.parent_plugin = None  # 父插件引用，用于后台运行

        # 解析环境变量
        self.proj_dir = self.get_proj_dir()

        # 添加缓存机制
        self.log_time_cache = {}  # 缓存日志文件的时间信息 {log_path: (compile_time, sim_time)}
        self.log_mtime_cache = {}  # 缓存日志文件的修改时间 {log_path: mtime}
        self.max_cache_size = 1000  # 最大缓存条目数

        self.init_ui()
        self.scan_regression_files()

    def run_in_background(self):
        """将窗口隐藏到后台运行"""
        # 隐藏窗口但不关闭
        self.hide()

        # 如果有父插件，通知它窗口已隐藏
        if isinstance(self.parent(), QWidget) and hasattr(self.parent(), 'run_in_background'):
            self.parent().run_in_background(self)

    def closeEvent(self, event):
        """
        关闭事件处理

        Args:
            event (QCloseEvent): 关闭事件
        """
        # 发出finished信号，通知插件对话框已关闭
        self.finished.emit(0)
        event.accept()

    def get_proj_dir(self):
        """获取PROJ_DIR环境变量"""
        proj_dir = os.environ.get("PROJ_DIR", "")
        if not proj_dir:
            # 如果环境变量不存在，使用当前目录
            proj_dir = os.getcwd()
        return proj_dir

    def get_cached_time(self, log_path, is_sim_log=False):
        """从缓存中获取时间信息，如果缓存中没有则返回None

        Args:
            log_path: 日志文件路径
            is_sim_log: 是否是仿真日志
        """
        if not log_path or not os.path.exists(log_path):
            return None

        cache_key = f"{log_path}_{is_sim_log}"
        current_mtime = os.path.getmtime(log_path)

        # 检查缓存
        if (cache_key in self.log_time_cache and
            cache_key in self.log_mtime_cache and
            self.log_mtime_cache[cache_key] == current_mtime):

            return self.log_time_cache[cache_key]

        return None

    def update_time_cache(self, log_path, is_sim_log, time_value):
        """更新时间缓存

        Args:
            log_path: 日志文件路径
            is_sim_log: 是否是仿真日志
            time_value: 时间值
        """
        if not log_path or not os.path.exists(log_path):
            return

        cache_key = f"{log_path}_{is_sim_log}"
        current_mtime = os.path.getmtime(log_path)

        self.log_time_cache[cache_key] = time_value
        self.log_mtime_cache[cache_key] = current_mtime

        # 如果缓存过大，删除最早的条目
        if len(self.log_time_cache) > self.max_cache_size:
            # 删除最早的10%条目
            keys_to_remove = list(self.log_time_cache.keys())[:int(self.max_cache_size * 0.1)]
            for key in keys_to_remove:
                del self.log_time_cache[key]
                if key in self.log_mtime_cache:
                    del self.log_mtime_cache[key]

    def init_ui(self):
        """初始化界面"""
        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)

        # 顶部区域 - 回归目录选择
        top_layout = QHBoxLayout()
        dir_label = QLabel("回归目录:")

        # 设置默认回归目录路径
        default_regression_dir = os.path.join(self.proj_dir, "work/regression")
        self.dir_input = QLineEdit(default_regression_dir)
        self.dir_input.setPlaceholderText("输入回归结果目录路径...")

        # 添加浏览按钮
        browse_btn = QPushButton("浏览...")
        browse_btn.clicked.connect(self.browse_regression_dir)

        # 添加扫描按钮
        scan_btn = QPushButton("扫描")
        scan_btn.clicked.connect(self.scan_regression_files)

        # 添加解析时间按钮
        self.parse_time_btn = QPushButton("解析时间")
        self.parse_time_btn.setToolTip("解析编译时间和仿真时间（可能会导致界面短暂卡顿）")
        self.parse_time_btn.clicked.connect(self.parse_all_times)
        self.parse_time_btn.setEnabled(False)  # 初始禁用，直到扫描完成

        top_layout.addWidget(dir_label)
        top_layout.addWidget(self.dir_input, stretch=1)
        top_layout.addWidget(browse_btn)
        top_layout.addWidget(scan_btn)
        top_layout.addWidget(self.parse_time_btn)

        # 中间区域 - 分割器
        splitter = QSplitter(Qt.Horizontal)

        # 左侧 - 时间戳和用例组树
        left_frame = QFrame()
        left_layout = QVBoxLayout(left_frame)

        # 时间戳树
        timestamp_group = QGroupBox("回归时间戳")
        timestamp_layout = QVBoxLayout()
        self.timestamp_tree = QTreeWidget()
        self.timestamp_tree.setHeaderLabels(["时间戳", "PASS", "FAIL"])
        self.timestamp_tree.setColumnWidth(0, 200)
        self.timestamp_tree.setColumnWidth(1, 80)
        self.timestamp_tree.setColumnWidth(2, 80)
        self.timestamp_tree.itemClicked.connect(self.on_timestamp_clicked)
        timestamp_layout.addWidget(self.timestamp_tree)
        timestamp_group.setLayout(timestamp_layout)

        # 用例组树
        group_group = QGroupBox("用例组")
        group_layout = QVBoxLayout()
        self.group_tree = QTreeWidget()
        self.group_tree.setHeaderLabels(["用例组", "PASS", "FAIL"])
        self.group_tree.setColumnWidth(0, 200)
        self.group_tree.setColumnWidth(1, 80)
        self.group_tree.setColumnWidth(2, 80)
        self.group_tree.itemClicked.connect(self.on_group_clicked)
        group_layout.addWidget(self.group_tree)
        group_group.setLayout(group_layout)

        left_layout.addWidget(timestamp_group, stretch=1)
        left_layout.addWidget(group_group, stretch=1)

        # 右侧 - 用例表格
        right_frame = QFrame()
        right_layout = QVBoxLayout(right_frame)

        # 表格标签页
        self.tab_widget = QTabWidget()

        # PASS表格
        self.pass_table = QTableWidget()
        self.setup_table(self.pass_table)

        # FAIL表格
        self.fail_table = QTableWidget()
        self.setup_table(self.fail_table)

        self.tab_widget.addTab(self.pass_table, "PASS用例")
        self.tab_widget.addTab(self.fail_table, "FAIL用例")

        # 搜索区域
        search_layout = QHBoxLayout()
        search_label = QLabel("搜索:")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入关键字搜索...")
        self.search_input.textChanged.connect(self.apply_filter)

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input, stretch=1)

        right_layout.addLayout(search_layout)
        right_layout.addWidget(self.tab_widget, stretch=1)

        # 添加到分割器
        splitter.addWidget(left_frame)
        splitter.addWidget(right_frame)
        splitter.setSizes([300, 900])  # 设置初始分割比例

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)

        # 底部按钮区域
        button_layout = QHBoxLayout()

        # 添加最小化按钮
        minimize_button = QPushButton("最小化窗口")
        minimize_button.clicked.connect(self.showMinimized)
        button_layout.addWidget(minimize_button)

        # 添加后台运行按钮
        background_button = QPushButton("后台运行")
        background_button.clicked.connect(self.run_in_background)
        button_layout.addWidget(background_button)

        # 添加关闭按钮
        close_button = QPushButton("关闭")
        close_button.clicked.connect(self.close)
        button_layout.addWidget(close_button)

        # 添加到主布局
        main_layout.addLayout(top_layout)
        main_layout.addWidget(splitter, stretch=1)
        main_layout.addWidget(self.progress_bar)
        main_layout.addLayout(button_layout)

        self.setLayout(main_layout)

    def setup_table(self, table):
        """设置表格属性"""
        table.setColumnCount(7)
        table.setHorizontalHeaderLabels(["用例名", "仿真状态", "种子", "编译时间(分钟)", "仿真时间(分钟)", "结果日志", "仿真命令"])

        # 设置表格样式
        table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                gridline-color: #d0d0d0;
                border: 1px solid #cccccc;
                border-radius: 3px;
                selection-background-color: #e8f0fe;
                selection-color: #000000;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #f0f0f0;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 5px;
                border: none;
                border-bottom: 2px solid #dee2e6;
                border-right: 1px solid #dee2e6;
                font-weight: bold;
            }
        """)

        # 设置表头和列宽
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Interactive)  # 用例名
        header.setSectionResizeMode(1, QHeaderView.Interactive)  # 仿真状态
        header.setSectionResizeMode(2, QHeaderView.Interactive)  # 种子
        header.setSectionResizeMode(3, QHeaderView.Interactive)  # 编译时间
        header.setSectionResizeMode(4, QHeaderView.Interactive)  # 仿真时间
        header.setSectionResizeMode(5, QHeaderView.Interactive)  # 结果日志
        header.setSectionResizeMode(6, QHeaderView.Stretch)      # 仿真命令

        # 设置初始列宽
        table.setColumnWidth(0, 180)  # 用例名
        table.setColumnWidth(1, 80)   # 仿真状态
        table.setColumnWidth(2, 80)   # 种子
        table.setColumnWidth(3, 100)  # 编译时间
        table.setColumnWidth(4, 100)  # 仿真时间
        table.setColumnWidth(5, 250)  # 结果日志

        # 设置表格属性
        table.setSelectionBehavior(QTableWidget.SelectRows)  # 选择整行
        table.setSelectionMode(QTableWidget.SingleSelection)  # 单行选择
        table.setAlternatingRowColors(True)  # 交替行颜色
        table.setSortingEnabled(True)  # 允许排序
        table.cellDoubleClicked.connect(self.on_cell_clicked)  # 双击事件

    def browse_regression_dir(self):
        """浏览选择回归目录"""
        # 获取当前目录作为起始目录
        current_dir = self.dir_input.text().strip()
        if not current_dir or not os.path.exists(current_dir):
            current_dir = self.proj_dir

        # 打开目录选择对话框
        dir_path = QFileDialog.getExistingDirectory(
            self,
            "选择回归结果目录",
            current_dir,
            QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
        )

        if dir_path:
            self.dir_input.setText(dir_path)
            # 自动扫描新选择的目录
            self.scan_regression_files()

    def scan_regression_files(self):
        """扫描回归结果文件"""
        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            QApplication.processEvents()  # 更新UI

            # 获取回归目录
            regression_dir = self.dir_input.text().strip()
            if not regression_dir:
                # 使用默认路径
                regression_dir = os.path.join(self.proj_dir, "work/regression")
                self.dir_input.setText(regression_dir)

            # 确保目录存在
            if not os.path.exists(regression_dir):
                reply = QMessageBox.question(
                    self,
                    "目录不存在",
                    f"回归目录 {regression_dir} 不存在，是否创建该目录？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )

                if reply == QMessageBox.Yes:
                    try:
                        os.makedirs(regression_dir, exist_ok=True)
                        QMessageBox.information(self, "成功", f"已创建目录: {regression_dir}")
                    except Exception as e:
                        QMessageBox.critical(self, "错误", f"创建目录失败: {str(e)}")
                        self.progress_bar.setVisible(False)
                        return
                else:
                    self.progress_bar.setVisible(False)
                    return

            # 清空现有数据
            self.regression_data = {}
            self.timestamp_tree.clear()
            self.group_tree.clear()
            self.pass_table.setRowCount(0)
            self.fail_table.setRowCount(0)

            # 查找所有回归结果文件
            pass_files = glob.glob(os.path.join(regression_dir, "regr_pass_list_regr_*.lst"))
            fail_files = glob.glob(os.path.join(regression_dir, "regr_fail_list_regr_*.lst"))

            total_files = len(pass_files) + len(fail_files)
            if total_files == 0:
                QMessageBox.warning(self, "警告", f"在 {regression_dir} 中未找到回归结果文件")
                self.progress_bar.setVisible(False)
                return

            # 按时间戳排序文件（从新到旧）
            pass_files.sort(key=os.path.getmtime, reverse=True)
            fail_files.sort(key=os.path.getmtime, reverse=True)

            # 创建文件映射 {timestamp: (pass_file, fail_file)}
            file_map = {}

            # 处理PASS文件
            for pass_file in pass_files:
                timestamp = self.extract_timestamp(pass_file)
                if timestamp not in file_map:
                    file_map[timestamp] = [None, None]
                file_map[timestamp][0] = pass_file

            # 处理FAIL文件
            for fail_file in fail_files:
                timestamp = self.extract_timestamp(fail_file)
                if timestamp not in file_map:
                    file_map[timestamp] = [None, None]
                file_map[timestamp][1] = fail_file

            # 按时间戳排序（从新到旧）
            sorted_timestamps = sorted(file_map.keys(), reverse=True)

            # 解析所有文件
            processed_files = 0

            for timestamp in sorted_timestamps:
                pass_file, fail_file = file_map[timestamp]

                # 初始化数据结构
                if timestamp not in self.regression_data:
                    self.regression_data[timestamp] = {"pass": {}, "fail": {}}

                # 处理PASS文件
                if pass_file:
                    self.parse_regression_file(pass_file, timestamp, "pass")
                    processed_files += 1
                    progress = int(processed_files * 100 / total_files)
                    self.progress_bar.setValue(progress)
                    QApplication.processEvents()  # 更新UI

                # 处理FAIL文件
                if fail_file:
                    self.parse_regression_file(fail_file, timestamp, "fail")
                    processed_files += 1
                    progress = int(processed_files * 100 / total_files)
                    self.progress_bar.setValue(progress)
                    QApplication.processEvents()  # 更新UI

            # 更新时间戳树
            self.update_timestamp_tree()

            # 隐藏进度条
            self.progress_bar.setVisible(False)

            # 启用解析时间按钮
            self.parse_time_btn.setEnabled(True)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"扫描回归文件失败: {str(e)}")
            self.progress_bar.setVisible(False)
            self.parse_time_btn.setEnabled(False)

    def extract_timestamp(self, file_path):
        """从文件名中提取时间戳"""
        match = re.search(r'regr_(?:pass|fail)_list_regr_(\d{8}_\d{6})\.lst', os.path.basename(file_path))
        if match:
            return match.group(1)
        return None

    def parse_regression_file(self, file_path, timestamp, result_type):
        """解析回归结果文件（不包括时间信息）"""
        try:
            current_group = None
            current_log = None

            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line in f:
                    line = line.strip()

                    # 跳过空行和注释行
                    if not line or line.startswith('//') and not line.startswith('//group:') and not line.startswith('//log:'):
                        continue

                    # 解析用例组
                    if line.startswith('//group:'):
                        current_group = line.replace('//group:', '').strip()
                        if current_group not in self.regression_data[timestamp][result_type]:
                            self.regression_data[timestamp][result_type][current_group] = []

                    # 解析日志路径
                    elif line.startswith('//log:'):
                        current_log = line.replace('//log:', '').strip()

                    # 解析用例行
                    elif current_group and ',' in line:
                        parts = [p.strip() for p in line.split(',')]
                        if len(parts) >= 6:
                            # 获取仿真状态
                            tag = parts[5].strip('[]') if len(parts) > 5 else ""

                            # 创建用例信息（不包括时间信息）
                            case_info = {
                                'status': parts[0],  # ON/OFF
                                'block': parts[1],   # block选项
                                'case': parts[2],    # case选项
                                'seed': parts[3],    # seed选项
                                'iterative': parts[4],  # 回归次数
                                'tag': parts[5],     # 仿真状态
                                'log': current_log,  # 日志路径
                                'compile_time': None,  # 编译时间（初始为None）
                                'sim_time': None,  # 仿真时间（初始为None）
                                'command': self.generate_command(parts)  # 生成仿真命令
                            }

                            # 添加到数据结构
                            self.regression_data[timestamp][result_type][current_group].append(case_info)

        except Exception as e:
            print(f"解析文件 {file_path} 失败: {str(e)}")

    def parse_time_from_log(self, log_path, is_sim_log=False):
        """从日志文件中解析时间信息

        Args:
            log_path: 日志文件路径
            is_sim_log: 是否是仿真日志
        """
        try:
            if not log_path or not os.path.exists(log_path):
                return None

            # 只读取文件的最后60行以提高解析速度
            last_lines = []

            # 使用更高效的方式读取最后60行
            with open(log_path, 'rb') as f:
                # 移动到文件末尾
                f.seek(0, os.SEEK_END)
                file_size = f.tell()

                # 如果文件太小，直接读取整个文件
                if file_size < 10000:  # 10KB
                    f.seek(0)
                    content = f.read().decode('utf-8', errors='ignore')
                    last_lines = content.splitlines()[-60:]
                else:
                    # 从文件末尾开始读取
                    block_size = 1024  # 1KB
                    blocks = []
                    lines_count = 0

                    # 从文件末尾开始，每次读取一个块，直到读取了足够的行
                    while lines_count < 60 and f.tell() > 0:
                        # 移动到前一个块
                        pos = max(f.tell() - block_size, 0)
                        f.seek(pos)
                        block = f.read(min(block_size, f.tell())).decode('utf-8', errors='ignore')
                        lines = block.splitlines()

                        # 如果不是第一个块，并且最后一行不完整，则忽略它
                        if blocks and not block.endswith('\n'):
                            lines = lines[:-1]

                        lines_count += len(lines)
                        blocks.insert(0, block)

                        # 如果已经到达文件开头，则退出循环
                        if f.tell() == 0:
                            break

                    # 合并所有块并获取最后60行
                    content = ''.join(blocks)
                    last_lines = content.splitlines()[-60:]

            if not last_lines:
                return None

            content = '\n'.join(last_lines)

            # 尝试匹配xrun格式
            xrun_pattern = r'xrun: Time - (\d+\.?\d*)s'
            if match := re.search(xrun_pattern, content):
                seconds = float(match.group(1))
                return round(seconds / 60, 2)

            # 尝试匹配VCS格式
            if "Compilation Performance Summary" in content:
                if is_sim_log:
                    # 对于仿真日志，先定位到仿真部分
                    if "SimuLation Performance Summary" in content:
                        # 分割内容，只保留仿真部分
                        sim_part = content.split("SimuLation Performance Summary")[-1]
                        # 在仿真部分中查找Elapsed Time
                        sim_pattern = r'Elapsed Time\s+:\s+(\d+)\s+sec'
                        if sim_match := re.search(sim_pattern, sim_part):
                            sim_time = float(sim_match.group(1))
                            return round(sim_time / 60, 2)
                else:
                    # 编译日志，查找第一个Elapsed time
                    compile_pattern = r'Elapsed time\s+:\s+(\d+)\s+sec'
                    if match := re.search(compile_pattern, content):
                        compile_time = float(match.group(1))
                        return round(compile_time / 60, 2)

        except Exception as e:
            print(f"解析日志 {log_path} 时间失败: {str(e)}")
        return None

    def generate_command(self, parts):
        """根据用例信息生成仿真命令"""
        try:
            # 确保有足够的部分
            if len(parts) < 10:
                return "无法生成命令：数据不完整"

            # 提取各个部分
            status = parts[0].strip()  # ON/OFF
            block = parts[1].strip()   # block选项
            case = parts[2].strip()    # case选项
            seed = parts[3].strip()    # seed选项，格式为[123456]
            tag = parts[5].strip()     # 仿真状态，格式为[PASS]
            priority = parts[6].strip() if len(parts) > 6 else ""  # 优先级
            config = parts[7].strip() if len(parts) > 7 else ""    # config文件
            cfg_def = parts[8].strip() if len(parts) > 8 else ""   # cfg_def选项
            base = parts[9].strip() if len(parts) > 9 else ""      # base选项
            plusarg = parts[10].strip() if len(parts) > 10 else "" # simarg选项

            # 构建命令
            cmd = ["runsim"]

            # 添加block选项
            if block:
                cmd.append(f"-block {block}")

            # 添加case选项
            if case:
                cmd.append(f"-case {case}")

            # 添加seed选项，去掉中括号
            if seed and '[' in seed and ']' in seed:
                seed_value = seed.strip('[]')
                cmd.append(f"-seed {seed_value}")

            # 添加config选项
            if config and config.lower() != "default":
                cmd.append(f"-config {config}")

            # 添加cfg_def选项，去掉中括号
            if cfg_def and '[' in cfg_def and ']' in cfg_def:
                cfg_def_value = cfg_def.strip('[]')
                if cfg_def_value.lower() != "default":
                    cmd.append(f"-cfg_def {cfg_def_value}")

            # 添加base选项
            if base:
                cmd.append(f"-base {base}")

            # 添加simarg选项，去掉括号
            if plusarg and '(' in plusarg and ')' in plusarg:
                plusarg_value = plusarg.strip('()')
                # 处理多个simarg参数
                if plusarg_value:
                    for arg in plusarg_value.split(','):
                        if '=' in arg:
                            key, value = arg.split('=', 1)
                            # 处理值中的中括号
                            if '[' in value and ']' in value:
                                value = value.strip('[]')
                            cmd.append(f"-simarg +{key}={value}")
                        else:
                            cmd.append(f"-simarg +{arg}")

            return " ".join(cmd)

        except Exception as e:
            print(f"生成命令失败: {str(e)}")
            return "命令生成失败"

    def parse_all_times(self):
        """解析所有用例的编译时间和仿真时间"""
        try:
            # 确认是否解析时间
            reply = QMessageBox.question(
                self,
                "确认解析时间",
                "解析编译时间和仿真时间可能需要较长时间，并可能导致界面短暂卡顿。确定要继续吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            QApplication.processEvents()  # 更新UI

            # 禁用解析时间按钮
            self.parse_time_btn.setEnabled(False)

            # 创建日志路径到时间的映射，用于避免重复解析相同的日志
            log_time_map = {}  # {log_path: (compile_time, sim_time)}

            # 收集所有需要解析时间的用例
            cases_to_process = []

            for timestamp, data in self.regression_data.items():
                for result_type in ["pass", "fail"]:
                    for group, cases in data[result_type].items():
                        for i, case_info in enumerate(cases):
                            tag = case_info["tag"].strip('[]')
                            log_path = case_info["log"]

                            # 只有PASS、RSF或RSP状态才解析时间
                            if tag in ["PASS", "RSF", "RSP"] and log_path and os.path.exists(log_path):
                                cases_to_process.append((timestamp, result_type, group, i, log_path))

            # 计算总数
            total_cases = len(cases_to_process)
            if total_cases == 0:
                self.progress_bar.setVisible(False)
                self.parse_time_btn.setEnabled(True)
                QMessageBox.information(self, "信息", "没有找到需要解析时间的用例")
                return

            # 解析所有用例的时间
            for i, (timestamp, result_type, group, case_index, log_path) in enumerate(cases_to_process):
                # 更新进度条
                progress = int((i + 1) * 100 / total_cases)
                self.progress_bar.setValue(progress)

                # 每处理10个用例更新一次UI
                if (i + 1) % 10 == 0:
                    QApplication.processEvents()

                # 解析仿真时间
                if log_path not in log_time_map:
                    # 先检查缓存
                    sim_time = self.get_cached_time(log_path, True)
                    if sim_time is None:
                        # 解析仿真时间
                        sim_time = self.parse_time_from_log(log_path, True)
                        # 更新缓存
                        if sim_time is not None:
                            self.update_time_cache(log_path, True, sim_time)

                    # 存储结果以避免重复解析
                    log_time_map[log_path] = (None, sim_time)

                # 更新用例的仿真时间
                self.regression_data[timestamp][result_type][group][case_index]['sim_time'] = log_time_map[log_path][1]

                # 解析编译时间
                log_dir = os.path.dirname(log_path)
                compile_log = os.path.join(log_dir, "irun_compile.log")

                if os.path.exists(compile_log) and compile_log not in log_time_map:
                    # 先检查缓存
                    compile_time = self.get_cached_time(compile_log, False)
                    if compile_time is None:
                        # 解析编译时间
                        compile_time = self.parse_time_from_log(compile_log, False)
                        # 更新缓存
                        if compile_time is not None:
                            self.update_time_cache(compile_log, False, compile_time)

                    # 存储结果以避免重复解析
                    log_time_map[compile_log] = (compile_time, None)

                # 更新用例的编译时间
                if os.path.exists(compile_log):
                    self.regression_data[timestamp][result_type][group][case_index]['compile_time'] = log_time_map[compile_log][0]

            # 更新当前显示的表格
            if self.current_timestamp and self.current_group:
                self.update_tables(self.current_timestamp, self.current_group)

            # 隐藏进度条
            self.progress_bar.setVisible(False)

            # 启用解析时间按钮
            self.parse_time_btn.setEnabled(True)

            QMessageBox.information(self, "完成", f"已成功解析 {total_cases} 个用例的时间信息")

        except Exception as e:
            self.progress_bar.setVisible(False)
            self.parse_time_btn.setEnabled(True)
            QMessageBox.critical(self, "错误", f"解析时间失败: {str(e)}")

    def update_timestamp_tree(self):
        """更新时间戳树"""
        self.timestamp_tree.clear()

        # 按时间戳排序（从新到旧）
        sorted_timestamps = sorted(self.regression_data.keys(), reverse=True)

        for timestamp in sorted_timestamps:
            data = self.regression_data[timestamp]

            # 计算PASS和FAIL用例总数
            pass_count = sum(len(cases) for cases in data["pass"].values())
            fail_count = sum(len(cases) for cases in data["fail"].values())

            # 创建时间戳项
            item = QTreeWidgetItem([
                timestamp,
                str(pass_count),
                str(fail_count)
            ])

            # 设置颜色
            if fail_count > 0:
                item.setForeground(2, QColor(255, 0, 0))  # 红色显示FAIL数量

            self.timestamp_tree.addTopLevelItem(item)

    def on_timestamp_clicked(self, item):
        """处理时间戳点击事件"""
        timestamp = item.text(0)
        self.current_timestamp = timestamp
        self.current_group = None

        # 更新用例组树
        self.update_group_tree(timestamp)

        # 清空表格
        self.pass_table.setRowCount(0)
        self.fail_table.setRowCount(0)

    def update_group_tree(self, timestamp):
        """更新用例组树"""
        self.group_tree.clear()

        if timestamp not in self.regression_data:
            return

        data = self.regression_data[timestamp]

        # 获取所有组
        all_groups = set(list(data["pass"].keys()) + list(data["fail"].keys()))

        for group in sorted(all_groups):
            # 计算PASS和FAIL用例数
            pass_count = len(data["pass"].get(group, []))
            fail_count = len(data["fail"].get(group, []))

            # 创建组项
            item = QTreeWidgetItem([
                group,
                str(pass_count),
                str(fail_count)
            ])

            # 设置颜色
            if fail_count > 0:
                item.setForeground(2, QColor(255, 0, 0))  # 红色显示FAIL数量

            self.group_tree.addTopLevelItem(item)

    def on_group_clicked(self, item):
        """处理用例组点击事件"""
        if not self.current_timestamp:
            return

        group = item.text(0)
        self.current_group = group

        # 更新表格
        self.update_tables(self.current_timestamp, group)

    def update_tables(self, timestamp, group):
        """更新表格内容"""
        if timestamp not in self.regression_data:
            return

        data = self.regression_data[timestamp]

        # 更新PASS表格
        self.pass_table.setRowCount(0)
        if group in data["pass"]:
            for case_info in data["pass"][group]:
                self.add_case_to_table(self.pass_table, case_info)

        # 更新FAIL表格
        self.fail_table.setRowCount(0)
        if group in data["fail"]:
            for case_info in data["fail"][group]:
                self.add_case_to_table(self.fail_table, case_info)

        # 应用过滤器
        self.apply_filter()

    def add_case_to_table(self, table, case_info):
        """添加用例到表格"""
        row = table.rowCount()
        table.insertRow(row)

        # 设置用例名
        case_item = QTableWidgetItem(case_info["case"])
        table.setItem(row, 0, case_item)

        # 设置仿真状态
        tag = case_info["tag"].strip('[]')
        status_item = QTableWidgetItem(tag)
        if tag == "PASS":
            status_item.setBackground(QColor(200, 255, 200))  # 浅绿色
        else:
            status_item.setBackground(QColor(255, 200, 200))  # 浅红色
        table.setItem(row, 1, status_item)

        # 设置种子
        seed = case_info["seed"].strip('[]')
        table.setItem(row, 2, QTableWidgetItem(seed))

        # 设置编译时间
        compile_time = case_info.get("compile_time")
        compile_item = QTableWidgetItem("")
        if compile_time is not None:
            compile_item = QTableWidgetItem(f"{compile_time:.2f}")
            compile_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            # 设置为数字类型以便正确排序
            compile_item.setData(Qt.DisplayRole, float(compile_time))
        table.setItem(row, 3, compile_item)

        # 设置仿真时间
        sim_time = case_info.get("sim_time")
        sim_item = QTableWidgetItem("")
        if sim_time is not None:
            sim_item = QTableWidgetItem(f"{sim_time:.2f}")
            sim_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            # 设置为数字类型以便正确排序
            sim_item.setData(Qt.DisplayRole, float(sim_time))
        table.setItem(row, 4, sim_item)

        # 设置日志路径
        log_item = QTableWidgetItem(case_info["log"])
        log_item.setToolTip("双击打开日志文件")
        log_item.setForeground(QColor(0, 0, 255))  # 蓝色
        table.setItem(row, 5, log_item)

        # 设置仿真命令
        cmd_item = QTableWidgetItem(case_info["command"])
        cmd_item.setToolTip("双击执行命令")
        cmd_item.setForeground(QColor(0, 128, 0))  # 绿色
        table.setItem(row, 6, cmd_item)

    def apply_filter(self):
        """应用搜索过滤器"""
        search_text = self.search_input.text().lower()

        # 过滤PASS表格
        for row in range(self.pass_table.rowCount()):
            visible = True
            if search_text:
                visible = False
                # 检查所有列
                for col in range(self.pass_table.columnCount()):
                    item = self.pass_table.item(row, col)
                    if item and search_text in item.text().lower():
                        visible = True
                        break
            self.pass_table.setRowHidden(row, not visible)

        # 过滤FAIL表格
        for row in range(self.fail_table.rowCount()):
            visible = True
            if search_text:
                visible = False
                # 检查所有列
                for col in range(self.fail_table.columnCount()):
                    item = self.fail_table.item(row, col)
                    if item and search_text in item.text().lower():
                        visible = True
                        break
            self.fail_table.setRowHidden(row, not visible)

    def on_cell_clicked(self, row, col):
        """处理单元格双击事件"""
        table = self.sender()

        # 处理日志路径点击
        if col == 5:  # 日志路径列
            log_path = table.item(row, col).text()
            if log_path and os.path.exists(log_path):
                self.open_log_file(log_path)
            else:
                QMessageBox.warning(self, "警告", f"日志文件不存在: {log_path}")

        # 处理仿真命令点击
        elif col == 6:  # 仿真命令列
            command = table.item(row, col).text()
            if command:
                self.execute_command(command)

    def open_log_file(self, log_path):
        """使用gvim打开日志文件"""
        try:
            process = QProcess()
            process.startDetached("gvim", [log_path])
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开日志文件失败: {str(e)}")

    def execute_command(self, command):
        """执行仿真命令"""
        try:
            # 确认是否执行命令
            reply = QMessageBox.question(
                self,
                "确认执行",
                f"确定要执行以下命令吗？\n{command}",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 获取父窗口（主窗口）
                main_window = self.parent()

                # 如果是对话框，则获取其父窗口
                if isinstance(main_window, QDialog):
                    main_window = main_window.parent()

                # 检查是否有主窗口引用
                if main_window is None:
                    QMessageBox.warning(self, "警告", "无法获取主窗口引用，将在独立进程中执行命令")
                    process = QProcess()
                    process.startDetached(command)
                    return

                # 从命令中提取用例名称
                case_name = None
                for part in command.split():
                    if part.startswith("-case"):
                        parts = command.split()
                        idx = parts.index(part)
                        if idx + 1 < len(parts):
                            case_name = parts[idx + 1]
                            break

                # 如果没有找到用例名称，使用命令的一部分作为标签名
                if not case_name:
                    # 使用命令的前30个字符作为标签名
                    case_name = command[:30] + "..." if len(command) > 30 else command

                # 检查主窗口是否有创建标签页的方法
                if hasattr(main_window, 'case_tabs') and hasattr(main_window, 'tab_widget'):
                    # 创建新标签页并执行命令
                    from runsim_gui import CaseTab

                    # 如果已有同名标签页，先关闭
                    if case_name in main_window.case_tabs:
                        tab_index = main_window.tab_widget.indexOf(main_window.case_tabs[case_name])
                        # 检查是否有关闭标签页的方法
                        if hasattr(main_window, 'close_case_tab'):
                            main_window.close_case_tab(tab_index)
                        else:
                            main_window.tab_widget.removeTab(tab_index)
                            del main_window.case_tabs[case_name]

                    # 创建新标签页
                    case_tab = CaseTab(case_name, command, main_window)
                    main_window.case_tabs[case_name] = case_tab
                    main_window.tab_widget.addTab(case_tab, case_name)
                    main_window.tab_widget.setCurrentWidget(case_tab)

                    # 执行命令
                    case_tab.start_execution()

                    # 保存命令到历史记录（如果主窗口有此方法）
                    if hasattr(main_window, 'save_history'):
                        main_window.save_history(command)

                    # 显示成功消息
                    QMessageBox.information(self, "成功", f"命令已在主窗口的'{case_name}'标签页中启动执行")
                else:
                    # 如果主窗口没有必要的方法，回退到独立进程执行
                    QMessageBox.warning(self, "警告", "主窗口不支持创建标签页，将在独立进程中执行命令")
                    process = QProcess()
                    process.startDetached(command)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"执行命令失败: {str(e)}")
            # 出错时回退到独立进程执行
            try:
                process = QProcess()
                process.startDetached(command)
            except Exception:
                pass
