import sys
import os
import re
import json
import platform
import subprocess
import psutil
from datetime import datetime
import time  # 添加time模块导入
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
    QTreeWidget, QTreeWidgetItem, QLineEdit, QPushButton, QFileDialog,
    QMessageBox, QCheckBox, QLabel, QStatusBar, QComboBox, QMenuBar, QMenu,
    QFormLayout, QTextEdit, QTabWidget, QDialog, QListWidget, QDialogButtonBox,
    QAbstractItemView, QListWidgetItem
)
from PyQt5.QtCore import (
    Qt, QRegExp, QTimer, pyqtSignal, pyqtSlot, QProcess,
    QMetaObject, Q_ARG, QMetaType
)
from PyQt5.QtGui import QRegExpValidator, QIcon, QFont, QColor, QPalette, QTextCursor
# 有条件导入QTermWidget
try:
    from qtermwidget import QTermWidget
    HAS_TERMINAL = True
except ImportError:
    HAS_TERMINAL = False
    print("QTermWidget not found, terminal integration disabled")
from utils.async_logger import AsyncLogger
from utils.cache_manager import CacheManager
from utils.async_task_manager import AsyncTaskManager
from utils.common_widgets import LabeledInput, FileSelector, ProgressWindow

class ResourceMonitor:
    """系统资源监控类"""
    @staticmethod
    def check_resources():
        """检查系统资源使用情况"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            mem_percent = psutil.virtual_memory().percent
            print(f"Debug - CPU使用率: {cpu_percent}%, 内存使用率: {mem_percent}%")
            return cpu_percent > 80 or mem_percent > 80, cpu_percent, mem_percent
        except Exception as e:
            print(f"Debug - 资源监控错误: {str(e)}")
            return False, 0, 0

# CaseTab 类：用于表示单个用例执行的标签页
class CaseTab(QWidget):
    """单个用例执行的标签页"""
    MAX_LOG_LENGTH = 100000  # 设置最大日志长度
    VISIBLE_REFRESH_INTERVAL = 100  # 可见tab刷新间隔(ms)
    HIDDEN_REFRESH_INTERVAL = 1000   # 隐藏tab刷新间隔(ms)
    MIN_BATCH_SIZE = 1000  # 最小批处理日志大小

    def __init__(self, case_name, command, main_window=None, parent=None):
        """
        初始化 CaseTab 实例。

        Args:
            case_name (str): 用例名称。
            command (str): 执行用例的命令。
            main_window (RunSimGUI): 主窗口引用。
            parent (QWidget, optional): 父QWidget。默认为 None。
        """
        super().__init__(parent)
        self.case_name = case_name
        self.command = command
        self.process = None
        self.main_window = main_window
        self.resource_monitor = ResourceMonitor()
        self.log_buffer = []  # 日志缓冲区
        self.log_batch = []   # 批量处理缓冲区
        self.log_timer = QTimer()
        self.log_timer.timeout.connect(self.flush_log_buffer)
        self.last_flush_time = time.time()
        self.log_completed = False
        self.is_visible = True
        self.auto_scroll = True  # 自动滚动标志
        self.tools_menu = None
        self.async_logger = AsyncLogger()
        self.init_ui()

        # 设置初始刷新间隔
        self.log_timer.start(self.VISIBLE_REFRESH_INTERVAL)

    def __del__(self):
        """析构函数，确保进程被正确终止"""
        self.stop_execution()

    def init_ui(self):
        """初始化用户界面布局和组件"""
        layout = QVBoxLayout()
        layout.setSpacing(3)
        layout.setContentsMargins(5, 5, 5, 5)

        # 状态显示区域布局
        status_layout = QHBoxLayout()
        status_layout.setSpacing(5)

        # 状态标签，显示用例执行状态
        self.status_label = QLabel("状态: 准备执行")
        self.status_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #2980b9;
                padding: 3px;
                background-color: #ecf0f1;
                border-radius: 3px;
            }
        """)

        status_layout.addWidget(self.status_label, stretch=3)

        # 重新执行按钮，用于重新执行当前tab指令
        self.re_run_btn = QPushButton("重新执行")
        self.re_run_btn.setIcon(QIcon.fromTheme("view-refresh", QIcon()))
        self.re_run_btn.clicked.connect(self.start_execution)  # 复用start_execution方法
        self.re_run_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 3px 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        status_layout.addWidget(self.re_run_btn, stretch=1)

        # 停止按钮，用于停止用例执行
        self.stop_btn = QPushButton("停止")
        self.stop_btn.setIcon(QIcon.fromTheme("media-playback-stop", QIcon()))
        self.stop_btn.clicked.connect(self.stop_execution)
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 3px 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
        """)
        status_layout.addWidget(self.stop_btn, stretch=1)

        layout.addLayout(status_layout)

        # 添加命令预览文本框
        self.cmd_preview = QTextEdit()
        self.cmd_preview.setReadOnly(True)
        self.cmd_preview.setFixedHeight(60)  # 固定高度
        self.cmd_preview.setStyleSheet("""
            QTextEdit {
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 9pt;
                background-color: #2d2d2d;
                color: #e6e6e6;
                border: 1px solid #1a1a1a;
                border-radius: 3px;
                padding: 5px;
            }
        """)
        self.cmd_preview.setText(f"执行命令:\n{self.command}")
        layout.addWidget(self.cmd_preview)

        # 添加嵌入式终端
        if HAS_TERMINAL and sys.platform != 'win32':
            self.terminal = QTermWidget()
            self.terminal.setColorScheme('Linux')
            self.terminal.setScrollBarPosition(QTermWidget.ScrollBarRight)
            self.terminal.setTerminalFont(QFont('Monospace', 10))
            layout.addWidget(self.terminal)
        else:
            self.terminal = None
            self.log_text = QTextEdit()
            self.log_text.setReadOnly(True)
            self.log_text.setStyleSheet("""
                QTextEdit {
                    font-family: "Consolas", "Courier New", monospace;
                    font-size: 9pt;
                    background-color: #ffffff;
                    color: #333333;
                    border: 1px solid #cccccc;
                    border-radius: 3px;
                }
            """)
            layout.addWidget(self.log_text)

        self.setLayout(layout)

    def start_execution(self):
        """开始执行用例"""
        if self.process is not None:
            self.stop_execution()

        # 清空日志显示和缓冲区
        if hasattr(self, 'log_text'):
            self.log_text.clear()
        # 强制更新UI
            QApplication.processEvents()

        # 确保日志缓冲区被清空
        if hasattr(self, 'log_buffer'):
            self.log_buffer.clear()

        # 重置定时器
        if hasattr(self, 'log_timer'):
            self.log_timer.stop()
            self.log_timer.start(self.VISIBLE_REFRESH_INTERVAL)

        # 清空命令预览，重新显示当前命令
        self.cmd_preview.clear()
        self.cmd_preview.setText(f"执行命令:\n{self.command}")

        # 检查资源使用情况
        high_usage, cpu_percent, mem_percent = self.resource_monitor.check_resources()

        if high_usage:
            self.status_label.setText(f"状态: 资源过高 (CPU:{cpu_percent}%, 内存:{mem_percent}%)")
            if self.terminal is not None:
                # 在嵌入式终端中创建新会话执行命令
                self.terminal.clear()
                self.terminal.sendText(f"bash -c '{self.command}'\n")
            else:
                # 回退到原有的QProcess执行方式
                self.process = QProcess(self)
                self.process.setProcessChannelMode(QProcess.MergedChannels)
                self.process.readyReadStandardOutput.connect(self.handle_output)
                self.process.finished.connect(self.handle_finished)

                current_dir = os.getcwd()
                self.process.setWorkingDirectory(current_dir)

                if sys.platform == 'win32':
                        self.process.start('cmd.exe', ['/c', self.command])
                else:
                    self.process.start('/bin/bash', ['-c', self.command])
        else:
                # 更新状态标签
            self.status_label.setText("状态: 执行中...")
            if self.terminal is not None:
                self.terminal.clear()
                self.terminal.sendText(f"{self.command}\n")
            else:
                # 原有的执行方式
                self.process = QProcess(self)
                self.process.setProcessChannelMode(QProcess.MergedChannels)
                self.process.readyReadStandardOutput.connect(self.handle_output)
                self.process.finished.connect(self.handle_finished)

                current_dir = os.getcwd()
                self.process.setWorkingDirectory(current_dir)

                if sys.platform == 'win32':
                    self.process.start('cmd.exe', ['/c', self.command])
                else:
                    self.process.start('/bin/bash', ['-c', self.command])

        # 强制更新界面
        QApplication.processEvents()

    def stop_execution(self):
        """停止进程执行"""
        if self.process is not None and self.process.state() == QProcess.Running:
            if sys.platform == 'win32':
                self.process.kill()
            else:
                # Linux下使用terminate()更温和地结束进程
                self.process.terminate()

            self.log_text.append("\n[执行已终止]\n")
            self.status_label.setText("状态: 已终止")

    def handle_output(self):
        """处理进程输出"""
        if self.process is None:
            return

        try:
            # 根据操作系统选择解码方式
            if sys.platform == 'win32':
                output = bytes(self.process.readAllStandardOutput()).decode('gbk', errors='ignore')
            else:
                output = bytes(self.process.readAllStandardOutput()).decode('utf-8', errors='ignore')

            # 添加到批处理缓冲区
            self.log_batch.append(output)

            # 如果批处理缓冲区够大了，就添加到主缓冲区
            if sum(len(text) for text in self.log_batch) >= self.MIN_BATCH_SIZE:
                self.log_buffer.append(''.join(self.log_batch))
                self.log_batch.clear()

        except Exception as e:
            print(f"处理输出时出错: {str(e)}")

    def flush_final_log(self):
        """确保所有日志都被刷新显示"""
        try:
            # 读取所有剩余输出
            if self.process:
                remaining_output = self.process.readAllStandardOutput()
                if remaining_output:
                    if sys.platform == 'win32':
                        output = bytes(remaining_output).decode('gbk', errors='ignore')
                    else:
                        output = bytes(remaining_output).decode('utf-8', errors='ignore')
                    self.log_batch.append(output)

            # 合并所有待处理的日志
            if self.log_batch:
                self.log_buffer.append(''.join(self.log_batch))
                self.log_batch.clear()

            if self.log_buffer:
                log_text = ''.join(self.log_buffer)
                self.log_buffer.clear()

                if hasattr(self, 'log_text'):
                    current_text = self.log_text.toPlainText()
                    total_text = current_text + log_text

                    # 如果总长度超过限制，只保留后半部分
                    if len(total_text) > self.MAX_LOG_LENGTH:
                        total_text = total_text[-self.MAX_LOG_LENGTH:]
                        self.log_text.clear()

                    self.log_text.append(log_text)

                    # 根据自动滚动设置决定是否滚动到底部
                    if self.auto_scroll:
                        self.log_text.verticalScrollBar().setValue(
                            self.log_text.verticalScrollBar().maximum()
                        )

            # 强制更新界面
            QApplication.processEvents()

        except Exception as e:
            print(f"刷新最终日志时出错: {str(e)}")

        finally:
            # 确保计时器停止
            self.log_timer.stop()
            self.log_completed = True

    def flush_log_buffer(self):
        """刷新日志缓冲区"""
        if not self.log_buffer and not self.log_batch:
            return

        try:
            current_time = time.time()
            refresh_interval = self.VISIBLE_REFRESH_INTERVAL / 1000.0 if self.is_visible else self.HIDDEN_REFRESH_INTERVAL / 1000.0

            # 检查是否应该刷新
            if not self.log_completed and not self.is_visible and (current_time - self.last_flush_time < refresh_interval):
                return

            # 合并所有待处理的日志
            if self.log_batch:
                self.log_buffer.append(''.join(self.log_batch))
                self.log_batch.clear()

            if self.log_buffer:
                log_text = ''.join(self.log_buffer)
                self.log_buffer.clear()

                if hasattr(self, 'log_text'):
                    current_text = self.log_text.toPlainText()

                    # 检查是否需要裁剪日志
                    if len(current_text) + len(log_text) > self.MAX_LOG_LENGTH:
                        # 保留后半部分的日志
                        total_text = current_text + log_text
                        keep_length = self.MAX_LOG_LENGTH // 2
                        current_text = total_text[-keep_length:]
                        self.log_text.clear()
                        self.log_text.append(current_text)
                    else:
                        # 直接追加新日志
                        self.log_text.append(log_text)

                    # 处理滚动条
                    scrollbar = self.log_text.verticalScrollBar()
                    at_bottom = scrollbar.value() >= scrollbar.maximum() - 10

                    # 如果需要自动滚动
                    if self.auto_scroll or at_bottom:
                        scrollbar.setValue(scrollbar.maximum())

            self.last_flush_time = current_time

        except Exception as e:
            print(f"刷新日志缓冲区时出错: {str(e)}")

    def handle_finished(self):
        """处理进程结束事件，更新状态标签但保持标签页"""
        if self.process is None:
            return

        try:
            exit_code = self.process.exitCode()

            # 确保最后的日志被完全刷新
            self.flush_final_log()

            if exit_code == 0:
                self.status_label.setText("状态: 执行完成")
                # 添加提示信息
                self.log_text.append("\n[执行完成] 可以使用工具栏按钮查看波形或日志\n")
            else:
                self.status_label.setText(f"状态: 执行失败 (退出码: {exit_code})")
                # 添加错误提示
                self.log_text.append(f"\n[执行失败] 退出码: {exit_code}\n")
        except Exception as e:
            self.status_label.setText("状态: 执行异常")
            self.log_text.append(f"\n[错误] 处理进程结束时发生错误: {str(e)}\n")
        finally:
            # 释放进程资源但不关闭标签页
            self.process = None
            self.log_completed = True
            # 强制更新界面
            QApplication.processEvents()

    def cleanup(self):
        """清理资源"""
        self.async_logger.stop()

    def showEvent(self, event):
        """标签页显示时恢复快速更新"""
        super().showEvent(event)
        self.is_visible = True
        self.log_timer.setInterval(self.VISIBLE_REFRESH_INTERVAL)
        # 立即刷新所有缓冲的日志
        self.flush_log_buffer()

    def hideEvent(self, event):
        """标签页隐藏时切换到慢速更新"""
        super().hideEvent(event)
        self.is_visible = False
        self.log_timer.setInterval(self.HIDDEN_REFRESH_INTERVAL)

# RunSimGUI 类：主窗口类，包含GUI的所有组件和逻辑
class RunSimGUI(QMainWindow):  # 正确继承QMainWindow
    """RunSimGUI 主窗口类，用于构建和控制GUI界面"""
    CONFIG_FILE = "runsim_config.json"  # 配置文件名
    HISTORY_FILE = "runsim_command_history.json"  # 命令历史文件名

    def __init__(self):
        """初始化 RunSimGUI 实例"""
        super().__init__()
        self.case_files = []
        self.regr_file = ""
        self.fsdb_file = ""
        self.history_limit = 50
        self.process = None
        self.case_tabs = {}
        self.max_tabs = 10  # 定义最大标签页数量
        self.cache_manager = CacheManager()
        self.async_task_manager = AsyncTaskManager()
        self.async_task_manager.task_completed.connect(self.handle_task_completed)
        self.async_task_manager.task_error.connect(self.handle_task_error)

        # 初始化日志标签页控件
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabsClosable(True)
        self.tab_widget.tabCloseRequested.connect(self.close_case_tab)
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #cccccc;
                background: white;
                border-radius: 3px;
            }
            QTabWidget::tab-bar {
                left: 5px;
            }
            QTabBar::tab {
                background: #f0f0f0;
                border: 1px solid #cccccc;
                padding: 5px 10px;
                margin-right: 2px;
                border-top-left-radius: 3px;
                border-top-right-radius: 3px;
            }
            QTabBar::tab:selected {
                background: white;
                border-bottom: 1px solid white;
            }
            QTabBar::tab:hover {
                background: #e0e0e0;
            }
        """)

        # 设置TabWidget的属性以优化性能
        self.tab_widget.setMovable(True)
        self.tab_widget.setUsesScrollButtons(True)
        self.tab_widget.setElideMode(Qt.ElideRight)
        self.tab_widget.setDocumentMode(True)

# 添加预览区域
        self.preview_edit = QTextEdit()
        self.preview_edit.setReadOnly(True)
        self.preview_edit.setFixedHeight(60)
        self.preview_edit.setStyleSheet("""
            QTextEdit {
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 9pt;
                background-color: #2d2d2d;
                color: #e6e6e6;
                border: 1px solid #1a1a1a;
                border-radius: 3px;
                padding: 5px;
            }
        """)

        # 添加实时预览计时器
        self.preview_timer = QTimer()
        self.preview_timer.timeout.connect(self.update_preview)
        self.preview_timer.start(500)  # 每500ms更新一次预览

        self.init_ui()
        self.load_config()
        self.load_history()

        # 添加自动清理定时器
        self.cleanup_timer = QTimer()
        self.cleanup_timer.timeout.connect(self.auto_cleanup)
        self.cleanup_timer.start(60000)  # 每分钟检查一次

        # 初始化插件系统
        from plugins.manager import PluginManager
        self.plugin_manager = PluginManager(self)
        self.plugin_manager.load_plugins()
        self.plugin_manager.setup_plugin_menu()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle('runsim 控制台')
        # 设置默认窗口大小和位置
        screen = QApplication.primaryScreen().geometry()
        default_width = min(1200, int(screen.width() * 0.75))
        default_height = min(800, int(screen.height() * 0.75))
        self.setGeometry(
            int((screen.width() - default_width) / 2),
            int((screen.height() - default_height) / 2),
            default_width,
            default_height
        )
        # 设置最小窗口尺寸
        self.setMinimumSize(800, 600)

        # 应用样式表
        self.init_styles()

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局，水平布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setSpacing(5)
        main_layout.setContentsMargins(5, 5, 5, 5)

        # 左面板，用例管理面板
        left_panel = self.create_case_panel()
        main_layout.addWidget(left_panel, stretch=2)

        # 右面板，配置参数面板
        right_panel = self.create_config_panel()
        main_layout.addWidget(right_panel, stretch=3)

        # 状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # 菜单栏
        self.create_menu()

    def init_styles(self):
        """初始化应用样式表，设置现代化GUI风格"""
        # 设置全局样式
        self.setStyleSheet("""
/* 主窗口背景 */
            QMainWindow {
                background-color: #f5f5f5;
            }

            /* 分组框样式 */
            QGroupBox {
font-family: "Microsoft YaHei";
                font-weight: bold;
                border: 2px solid #d0d0d0;
                border-radius: 6px;
                margin-top: 12px;
                padding: 10px;
                background-color: white;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                left: 10px;
                padding: 0 5px;
                color: #444;
            }

            /* 输入框样式 */
            QLineEdit {
                                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 5px;
                background: white;
                selection-background-color: #4a9eff;
                font-family: "Microsoft YaHei";
            }

            QLineEdit:focus {
                border: 2px solid #4a9eff;
            }

            /* 按钮样式 */
            QPushButton {
                background-color: #4a9eff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
font-family: "Microsoft YaHei";
                min-width: 80px;
            }

            QPushButton:hover {
                background-color: #3d8ced;
            }

            QPushButton:pressed {
                background-color: #3274bf;
            }

            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }

            /* 复选框样式 */
            QCheckBox {
                font-family: "Microsoft YaHei";
                spacing: 5px;
            }

            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border: 2px solid #ccc;
                border-radius: 4px;
            }

            QCheckBox::indicator:checked {
                background-color: #4a9eff;
                border-color: #4a9eff;
                image: url(resources/check.png);
            }

            /* 树形控件样式 */
            QTreeWidget {
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                background-color: white;
                font-family: "Microsoft YaHei";
            }

            QTreeWidget::item {
                height: 25px;
                color: #333;
            }

            QTreeWidget::item:selected {
                background-color: #e6f3ff;
                color: #000;
            }

            QTreeWidget::item:hover {
                background-color: #f5f5f5;
            }

            /* 标签页样式 */
            QTabWidget::pane {
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                top: -1px;
            }

            QTabBar::tab {
                font-family: "Microsoft YaHei";
                padding: 8px 15px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                border: 1px solid #d0d0d0;
                background-color: #f5f5f5;
            }

            QTabBar::tab:selected {
                background-color: white;
                border-bottom-color: white;
            }

            QTabBar::tab:hover:!selected {
                background-color: #e6f3ff;
            }

            /* 状态栏样式 */
            QStatusBar {
                background-color: #f8f9fa;
                color: #666;
                font-family: "Microsoft YaHei";
                padding: 2px;
                border-top: 1px solid #e4e4e4;
            }

            /* 菜单样式 */
            QMenuBar {
                background-color: #f8f9fa;
                border-bottom: 1px solid #e4e4e4;
            }

            QMenuBar::item {
                padding: 6px 10px;
                background-color: transparent;
            }

            QMenuBar::item:selected {
                background-color: #e6f3ff;
                border-radius: 4px;
            }

            QMenu {
                background-color: white;
                border: 1px solid #d0d0d0;
                padding: 5px;
            }

            QMenu::item {
                padding: 6px 25px 6px 20px;
                border-radius: 4px;
            }

            QMenu::item:selected {
                background-color: #e6f3ff;
                color: #000;
            }

            /* 滚动条样式 */
            QScrollBar:vertical {
                border: none;
                background: #f5f5f5;
                width: 10px;
                margin: 0;
            }

            QScrollBar::handle:vertical {
                background: #c1c1c1;
                min-height: 30px;
                border-radius: 5px;
            }

            QScrollBar::handle:vertical:hover {
                background: #a8a8a8;
            }

            /* 文本编辑器样式 */
            QTextEdit {
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                padding: 5px;
                font-family: "Microsoft YaHei";
                selection-background-color: #4a9eff;
            }
        """)

        # 设置应用程序字体
        app_font = QFont("Microsoft YaHei", 9)
        QApplication.setFont(app_font)

        # 设置应用程序风格为Fusion
        QApplication.setStyle("Fusion")

    def create_menu(self):
        """创建菜单栏"""
        menu_bar = self.menuBar()

        # 文件菜单
        file_menu = QMenu("文件", self)
        save_action = file_menu.addAction("保存配置")
        save_action.triggered.connect(self.save_config)
        load_action = file_menu.addAction("加载配置")
        load_action.triggered.connect(self.load_config)
        file_menu.addSeparator()
        history_action = file_menu.addAction("清除历史")
        history_action.triggered.connect(self.clear_history)

        # 工具菜单 - 保存为类属性
        self.tools_menu = QMenu("工具", self)

        # 添加覆盖率工具选项
        coverage_action = self.tools_menu.addAction("打开覆盖率工具(IMC)")
        coverage_action.triggered.connect(self.open_coverage)
        coverage_action.setShortcut("Ctrl+I")

        self.tools_menu.addSeparator()

        # 其他工具选项
        verdi_action = self.tools_menu.addAction("打开Verdi")
        verdi_action.triggered.connect(self.open_verdi)
        verisium_action = self.tools_menu.addAction("打开Verisium")
        verisium_action.triggered.connect(self.open_verisium)

        self.tools_menu.addSeparator()
        compile_log_action = self.tools_menu.addAction("查看编译日志")
        compile_log_action.triggered.connect(self.open_compile_log)
        sim_log_action = self.tools_menu.addAction("查看仿真日志")
        sim_log_action.triggered.connect(self.open_sim_log)

        asm_action = self.tools_menu.addAction("反汇编文件")
        asm_action.triggered.connect(self.open_asm_file)

        # 添加菜单到菜单栏
        menu_bar.addMenu(file_menu)
        menu_bar.addMenu(self.tools_menu)

    def create_case_panel(self):
        """创建用例管理面板"""
        panel = QGroupBox("用例管理")
        layout = QVBoxLayout()
        layout.setSpacing(10)
        layout.setContentsMargins(10, 15, 10, 10)

        # 文件加载区域
        file_layout = QHBoxLayout()
        file_layout.setSpacing(8)

        # 增加环境解析按钮
        self.parse_env_btn = QPushButton("解析环境")
        self.parse_env_btn.setIcon(QIcon.fromTheme("system-search", QIcon()))
        self.parse_env_btn.clicked.connect(self.show_env_parse_dialog)

        # 加载用例文件按钮
        self.case_file_btn = QPushButton("加载用例文件")
        self.case_file_btn.setIcon(QIcon.fromTheme("document-open", QIcon()))
        self.case_file_btn.clicked.connect(self.load_case_file)

        # 删除用例文件按钮
        self.remove_case_file_btn = QPushButton("删除用例文件")
        self.remove_case_file_btn.setIcon(QIcon.fromTheme("edit-delete", QIcon()))
        self.remove_case_file_btn.clicked.connect(self.remove_case_file)

        # 添加刷新按钮
        self.refresh_btn = QPushButton("刷新用例")
        self.refresh_btn.setIcon(QIcon.fromTheme("view-refresh", QIcon()))
        self.refresh_btn.clicked.connect(self.parse_case_files)

        file_layout.addWidget(self.parse_env_btn)
        file_layout.addWidget(self.case_file_btn)
        file_layout.addWidget(self.remove_case_file_btn)
        file_layout.addWidget(self.refresh_btn)

        # 搜索过滤区域
        self.search_box = QLineEdit()
        self.search_box.setPlaceholderText("过滤用例...")
        self.search_box.textChanged.connect(self.filter_cases)

        # 添加搜索图标
        search_label = QLabel()
        search_label.setText("🔍")  # 使用Unicode搜索图标
        search_layout = QHBoxLayout()
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_box)
        search_layout.setSpacing(5)
        search_layout.setContentsMargins(0, 5, 0, 5)

        # 用例列表，树形结构展示用例
        self.case_tree = QTreeWidget()
        self.case_tree.setHeaderLabels(["用例名称", "基础用例"])
        self.case_tree.setAlternatingRowColors(True)  # 交替行颜色

        # 用例列表，树形结构展示用例
        self.case_tree = QTreeWidget()
        self.case_tree.setHeaderLabels(["用例名称", "基础用例"])
        self.case_tree.setAlternatingRowColors(True)  # 交替行颜色
        self.case_tree.setUniformRowHeights(True)  # 统一行高
        self.case_tree.setSortingEnabled(True)  # 允许排序
        self.case_tree.setSelectionMode(QTreeWidget.ExtendedSelection)  # 允许多选
        self.case_tree.itemSelectionChanged.connect(self.update_case_input)

        # 设置表头样式
        header = self.case_tree.header()
        header.setDefaultSectionSize(200)  # 设置默认列宽
        header.setStretchLastSection(True)  # 最后一列自动拉伸

        # 添加右键菜单
        self.case_tree.setContextMenuPolicy(Qt.CustomContextMenu)
        self.case_tree.customContextMenuRequested.connect(self.show_case_context_menu)

        layout.addLayout(file_layout)
        layout.addLayout(search_layout)
        layout.addWidget(self.case_tree)
        panel.setLayout(layout)
        return panel

    def create_config_panel(self):
        """创建运行参数配置面板"""
        # 创建右侧的主Tab控件
        right_panel = QTabWidget()
        right_panel.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #cccccc;
                background: white;
                border-radius: 3px;
            }
            QTabBar::tab {
                background: #f0f0f0;
                border: 1px solid #cccccc;
                padding: 5px 10px;
                border-top-left-radius: 3px;
                border-top-right-radius: 3px;
            }
            QTabBar::tab:selected {
                background: white;
                border-bottom: 1px solid white;
            }
        """)

        # 创建参数配置tab页
        param_tab = QWidget()
        param_layout = QVBoxLayout()
        param_layout.setSpacing(5)
        param_layout.setContentsMargins(5, 5, 5, 5)

        # 添加原有的参数配置组件
        param_layout.addLayout(self.create_history_layout())
        param_layout.addWidget(self.create_basic_group())
        param_layout.addWidget(self.create_wave_group())
        param_layout.addWidget(self.create_post_group())
        param_layout.addWidget(self.create_regr_group())
        param_layout.addLayout(self.create_tool_btn_layout())

        # 设置参数配置tab的布局
        param_tab.setLayout(param_layout)

        # 创建日志tab页
        log_tab = QWidget()
        log_layout = QVBoxLayout()
        log_layout.setSpacing(2)
        log_layout.setContentsMargins(5, 5, 5, 5)

        # 添加工具按钮布局
        tool_layout = QHBoxLayout()
        tool_layout.setSpacing(5)

        # 工具按钮样式
        tool_btn_style = """
            QPushButton {
                background-color: #4c8bf5;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 4px 8px;
                min-width: 80px;
            }
            QPushButton:hover { background-color: #3d7ce4; }
            QPushButton:pressed { background-color: #3069c6; }
        """

        # 创建按钮
        verdi_btn = QPushButton("打开Verdi")
        verdi_btn.setStyleSheet(tool_btn_style)
        verdi_btn.clicked.connect(self.open_verdi)

        verisium_btn = QPushButton("打开Verisium")
        verisium_btn.setStyleSheet(tool_btn_style)
        verisium_btn.clicked.connect(self.open_verisium)

        compile_log_btn = QPushButton("编译日志")
        compile_log_btn.setStyleSheet(tool_btn_style)
        compile_log_btn.clicked.connect(self.open_compile_log)

        sim_log_btn = QPushButton("仿真日志")
        sim_log_btn.setStyleSheet(tool_btn_style)
        sim_log_btn.clicked.connect(self.open_sim_log)

        asm_btn = QPushButton("反汇编文件")
        asm_btn.setStyleSheet(tool_btn_style)
        asm_btn.clicked.connect(self.open_asm_file)

        # 添加按钮到布局
        tool_layout.addWidget(verdi_btn)
        tool_layout.addWidget(verisium_btn)
        tool_layout.addWidget(compile_log_btn)
        tool_layout.addWidget(sim_log_btn)
        tool_layout.addWidget(asm_btn)
        tool_layout.addStretch()  # 添加弹簧,使按钮靠左对齐

        # 添加工具按钮布局和tab_widget到日志tab布局
        log_layout.addLayout(tool_layout)
        log_layout.addWidget(self.tab_widget)
        log_tab.setLayout(log_layout)

        # 添加两个主tab页
        right_panel.addTab(param_tab, "运行参数配置")
        right_panel.addTab(log_tab, "执行日志")

        return right_panel

    # 新增方法,从原create_config_panel中拆分出来
    def create_history_layout(self):
        """创建历史命令区域"""
        history_layout = QHBoxLayout()
        history_layout.setSpacing(5)

        history_label = QLabel("历史命令:")
        history_label.setMinimumWidth(70)

        self.history_combo = QComboBox()
        self.history_combo.setEditable(True)
        self.history_combo.currentIndexChanged.connect(self.apply_history)

        history_btn = QPushButton("↺")
        history_btn.setToolTip("重新执行该命令")
        history_btn.setFixedWidth(30)
        history_btn.clicked.connect(self.re_run_history)

        history_layout.addWidget(history_label)
        history_layout.addWidget(self.history_combo)
        history_layout.addWidget(history_btn)

        return history_layout

    def create_tool_btn_layout(self):
        """创建工具按钮区域"""
        tool_btn_layout = QVBoxLayout()  # 改为垂直布局

        # 添加命令预览
        preview_group = QGroupBox("命令预览")
        preview_layout = QVBoxLayout()
        preview_layout.addWidget(self.preview_edit)
        preview_group.setLayout(preview_layout)
        tool_btn_layout.addWidget(preview_group)

        # 按钮布局
        btn_layout = QHBoxLayout()

        # 使用原有的按钮样式定义
        main_btn_style = """
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 4px 8px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover { background-color: #45a049; }
            QPushButton:pressed { background-color: #3d8b40; }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #888888;
            }
        """

        self.run_btn = QPushButton("执行仿真和编译")
        self.run_btn.setStyleSheet(main_btn_style)
        self.run_btn.setIcon(QIcon.fromTheme("media-playback-start", QIcon()))
        self.run_btn.clicked.connect(lambda: self.execute_command("normal"))

        btn_layout.addWidget(self.run_btn)

        tool_btn_layout.addLayout(btn_layout)
        return tool_btn_layout

    def create_basic_group(self):
        """创建基础参数组"""
        basic_group = QGroupBox("基础参数")
        basic_layout = QFormLayout()
        basic_layout.setSpacing(5)
        basic_layout.setContentsMargins(5, 10, 5, 5)

        # 使用LabeledInput替换原有输入框
        self.base_input = LabeledInput("BASE:", "输入BASE参数（可选）")
        self.block_input = LabeledInput("BLOCK:", "输入BLOCK参数（必填）")
        self.case_input = LabeledInput("CASE:", "")

        basic_layout.addRow(self.base_input)
        basic_layout.addRow(self.block_input)
        basic_layout.addRow(self.case_input)

        rundir_other_layout = QHBoxLayout()
        rundir_other_layout.setSpacing(8)

        self.rundir_input = QLineEdit()
        self.rundir_input.setPlaceholderText("输入工作目录（可选）")

        self.other_options_input = QLineEdit()
        self.other_options_input.setPlaceholderText("输入其他runsim选项（可选）")

        rundir_form = QFormLayout()
        rundir_form.setSpacing(3)
        rundir_form.addRow("工作目录（-rundir）:", self.rundir_input)

        other_options_form = QFormLayout()
        other_options_form.setSpacing(3)
        other_options_form.addRow("其他选项:", self.other_options_input)

        rundir_other_layout.addLayout(rundir_form)
        rundir_other_layout.addLayout(other_options_form)

        seed_layout = QHBoxLayout()
        seed_layout.setSpacing(8)

        self.seed_input = QLineEdit()
        self.seed_input.setPlaceholderText("输入仿真种子号（可选）")
        self.seed_input.setValidator(QRegExpValidator(QRegExp("[0-9]+")))

        self.get_seed_btn = QPushButton("获取种子号")
        self.get_seed_btn.clicked.connect(self.get_seed_from_log)
        self.get_seed_btn.setIcon(QIcon.fromTheme("edit-find", QIcon()))

        seed_layout.addWidget(self.seed_input)
        seed_layout.addWidget(self.get_seed_btn)

        basic_layout.addRow("", rundir_other_layout)
        basic_layout.addRow("种子号（-seed）:", seed_layout)

        basic_group.setLayout(basic_layout)
        return basic_group

    def create_wave_group(self):
        """创建波形配置组，包含FSDB, VWDB, SVA, COV等选项"""
        wave_group = QGroupBox("波形配置")
        wave_layout = QVBoxLayout()
        wave_layout.setSpacing(4)
        wave_layout.setContentsMargins(5, 10, 5, 5)

        fsdb_layout = QVBoxLayout()
        fsdb_layout.setSpacing(3)

        fsdb_check_layout = QHBoxLayout()
        fsdb_check_layout.setSpacing(5)

        self.fsdb_check = QCheckBox("Dump FSDB波形（-fsdb）")
        self.vwdb_check = QCheckBox("Dump VWDB波形（-vwdb）")
        self.cl_check = QCheckBox("Clean INCA_libs（-cl）")
        self.fsdb_check.stateChanged.connect(self.toggle_fsdb_input)
        self.vwdb_check.stateChanged.connect(self.toggle_fsdb_input)
        fsdb_check_layout.addWidget(self.fsdb_check)
        fsdb_check_layout.addWidget(self.vwdb_check)
        fsdb_check_layout.addWidget(self.cl_check)
        fsdb_layout.addLayout(fsdb_check_layout)

        fsdb_file_layout = QHBoxLayout()
        fsdb_file_layout.setSpacing(5)

        self.fsdb_btn = QPushButton("选择TCL文件")
        self.fsdb_btn.setIcon(QIcon.fromTheme("document-open", QIcon()))
        self.fsdb_btn.clicked.connect(self.select_fsdb_file)

        self.fsdb_label = QLabel("未选择TCL文件")
        self.fsdb_label.setStyleSheet("color: #888; font-style: italic;")

        self.fsdb_clear_btn = QPushButton("清除")
        self.fsdb_clear_btn.clicked.connect(self.clear_fsdb_file)
        self.fsdb_btn.setEnabled(False)
        self.fsdb_clear_btn.setEnabled(False)

        fsdb_file_layout.addWidget(self.fsdb_btn)
        fsdb_file_layout.addWidget(self.fsdb_label)
        fsdb_file_layout.addWidget(self.fsdb_clear_btn)
        fsdb_layout.addLayout(fsdb_file_layout)
        wave_layout.addLayout(fsdb_layout)

        check_layout = QHBoxLayout()
        check_layout.setSpacing(5)

        self.sva_check = QCheckBox("Dump SVA断言（-dump_sva）")
        self.sim_only_check = QCheckBox("仅仿真（-R）")
        self.compile_only_check = QCheckBox("仅编译（-C）")
        self.cov_check = QCheckBox("收集覆盖率（-cov）")
        self.upf_check = QCheckBox("UPF仿真（-upf）")

        check_layout.addWidget(self.sva_check)
        check_layout.addWidget(self.sim_only_check)
        check_layout.addWidget(self.compile_only_check)
        check_layout.addWidget(self.cov_check)
        check_layout.addWidget(self.upf_check)
        wave_layout.addLayout(check_layout)

        self.sim_only_check.stateChanged.connect(self.handle_mode_change)
        self.compile_only_check.stateChanged.connect(self.handle_mode_change)

        self.dump_mem_input = QLineEdit()
        self.wdd_input = QLineEdit()
        self.wdd_input.setPlaceholderText("输入时间（如：1ns）")
        self.simarg_input = QLineEdit()
        self.simarg_input.setPlaceholderText("输入仿真参数（可选）")
        self.cfg_def_input = QLineEdit()
        self.cfg_def_input.setPlaceholderText("输入配置定义（可选）")

        dump_row_layout = QHBoxLayout()
        dump_mem_form = QFormLayout()
        dump_mem_form.setSpacing(3)
        dump_mem_form.addRow("Dump Memory（-dump_mem）:", self.dump_mem_input)
        wdd_form = QFormLayout()
        wdd_form.setSpacing(3)
        wdd_form.addRow("波形Dump起始时间（-wdd）:", self.wdd_input)
        dump_row_layout.addLayout(dump_mem_form)
        dump_row_layout.addLayout(wdd_form)
        wave_layout.addLayout(dump_row_layout)

        param_row_layout = QHBoxLayout()
        simarg_form = QFormLayout()
        simarg_form.setSpacing(3)
        simarg_form.addRow("仿真参数（-simarg）:", self.simarg_input)
        cfg_def_form = QFormLayout()
        cfg_def_form.setSpacing(3)
        cfg_def_form.addRow("配置定义（-cfg_def）:", self.cfg_def_input)
        param_row_layout.addLayout(simarg_form)
        param_row_layout.addLayout(cfg_def_form)
        wave_layout.addLayout(param_row_layout)

        wave_group.setLayout(wave_layout)
        return wave_group

    def create_post_group(self):
        """创建后仿配置组，包含后仿参数输入框"""
        post_group = QGroupBox("后仿配置")
        post_layout = QHBoxLayout()
        post_layout.setSpacing(2)
        post_layout.setContentsMargins(2, 2, 2, 2)

        post_layout.addWidget(QLabel("后仿（-post）:"))
        self.post_input = QLineEdit()
        self.post_input.setPlaceholderText("sdf=CORNER_NAME")
        post_layout.addWidget(self.post_input)

        post_group.setLayout(post_layout)
        return post_group

    def create_regr_group(self):
        """创建回归测试配置组，包含回归列表文件选择功能和指令解析功能"""
        regr_group = QGroupBox("回归测试")
        regr_layout = QVBoxLayout()
        regr_layout.setSpacing(5)
        regr_layout.setContentsMargins(5, 10, 5, 5)

        # 回归文件选择区域
        file_layout = QHBoxLayout()
        file_layout.setSpacing(5)

        self.regr_btn = QPushButton("选择回归列表")
        self.regr_btn.setIcon(QIcon.fromTheme("document-open", QIcon()))
        self.regr_btn.clicked.connect(self.select_regr_file)

        self.regr_label = QLabel("未选择回归文件")
        self.regr_label.setStyleSheet("color: #888; font-style: italic;")

        self.clear_regr_btn = QPushButton("清除")
        self.clear_regr_btn.clicked.connect(self.clear_regr_file)
        self.clear_regr_btn.setEnabled(False)

        file_layout.addWidget(self.regr_btn)
        file_layout.addWidget(self.regr_label)
        file_layout.addWidget(self.clear_regr_btn)

        # 添加-fm复选框和-bp输入框
        options_layout = QHBoxLayout()
        options_layout.setSpacing(5)

        # 添加-fm复选框
        self.fm_check = QCheckBox("回归FAIL用例(-fm)")
        self.fm_check.setToolTip("只回归之前失败的用例")
        options_layout.addWidget(self.fm_check)

        # 添加-bp输入框
        bp_layout = QHBoxLayout()
        bp_layout.setSpacing(3)
        bp_label = QLabel("提交服务器(-bp):")
        self.bp_input = QLineEdit()
        self.bp_input.setPlaceholderText("输入服务器名称")
        bp_layout.addWidget(bp_label)
        bp_layout.addWidget(self.bp_input)
        options_layout.addLayout(bp_layout)

        # 添加解析回归指令按钮
        parse_layout = QHBoxLayout()
        parse_layout.setSpacing(5)

        self.parse_regr_btn = QPushButton("解析回归指令")
        self.parse_regr_btn.setIcon(QIcon.fromTheme("document-edit", QIcon()))
        self.parse_regr_btn.clicked.connect(self.parse_regr_command)
        parse_layout.addWidget(self.parse_regr_btn)

        regr_layout.addLayout(file_layout)
        regr_layout.addLayout(options_layout)
        regr_layout.addLayout(parse_layout)

        regr_group.setLayout(regr_layout)
        return regr_group

    # ------------------ 核心功能 ------------------
    def parse_base_block_from_path(self, file_path):
        """
        根据用例配置文件路径解析 base 和 block 参数。

        Args:
            file_path (str): 用例配置文件的路径。

        Returns:
            tuple: (base, block) 参数，如果解析失败返回 (None, None)。
        """
        try:
            # 将路径转换为标准格式，统一路径分隔符
            path = os.path.normpath(file_path)
            parts = path.split(os.sep)

            # 查找 "dv" 目录位置，作为路径解析的起点
            if "dv" not in parts:
                return None, None

            dv_index = parts.index("dv")

            # 情况1: dv/{subsys}/bin/case_cfg/xxx_case.cfg 路径格式
            if len(parts) > dv_index + 4 and parts[dv_index + 3] == "case_cfg":
                subsys = parts[dv_index + 1]
                return "", subsys  # base 为空，block 为 subsys

            # 情况2: dv/udtb/{subsys}/子环境名/bin/xxx.cfg 路径格式
            if "udtb" in parts and "bin" in parts:
                udtb_index = parts.index("udtb")
                if len(parts) > udtb_index + 3:
                    subsys = parts[udtb_index + 1]
                    if subsys == "usvp":
                        # 特殊处理 udtb/usvp 路径，base 为 top，block 为 udtb/usvp
                        return "top", "udtb/usvp"
                    else:
                        subenv = parts[udtb_index + 2]
                        return subsys, f"udtb/{subsys}/{subenv}" # base 为 subsys，block 为 udtb/{subsys}/{subenv}

            # 情况3: dv/udtb/usvp/bin/case_cfg/xxx_subsys_case.cfg 路径格式
            if "udtb" in parts and "usvp" in parts and "case_cfg" in parts:
                filename = os.path.basename(file_path) # 获取文件名

                # 调试输出实际解析的文件名
                print(f"Debug - Parsing filename: {filename}")

                # 更灵活的正则表达式匹配多种可能的命名格式，例如 xxx_subsys_case.cfg, xxx-subsys_case.cfg, xxx_case.cfg
                subsys_match = re.match(
                    r"^(?P<subsys>\w+?)(?:[-_]subsys|_case)?(?:_case)?\.cfg$",
                    filename,
                    re.IGNORECASE # 忽略大小写
                )

                if subsys_match:
                    subsys = subsys_match.group("subsys") # 提取 subsys 名称
                    print(f"Debug - Matched subsys: {subsys}")
                    return f"{subsys}_sys", "udtb/usvp" # base 为 {subsys}_sys，block 为 udtb/usvp

                # 如果正则匹配失败，尝试更安全的提取方式，通过文件名分割提取 subsys
                try:
                    # 移除扩展名后按分隔符拆分，分隔符包括下划线、连字符和点
                    base_name = os.path.splitext(filename)[0]
                    parts = re.split(r"[_\-.]", base_name)
                    subsys = next(part for part in parts if part.lower() not in {'case', 'subsys', 'cfg'}) # 找到第一个不是 case, subsys, cfg 的部分作为 subsys
                    print(f"Debug - Fallback extracted subsys: {subsys}")
                    return f"{subsys}_sys", "udtb/usvp" # base 为 {subsys}_sys，block 为 udtb/usvp
                except Exception as e:
                    print(f"Debug - Extraction failed: {str(e)}")
                    return "top", "udtb/usvp" # 默认 base 为 top，block 为 udtb/usvp

            return None, None # 无法解析时返回 None, None
        except Exception:
            return None, None # 发生异常时返回 None, None

    def load_case_file(self):
        """加载用例文件，支持多选文件"""
        paths, _ = QFileDialog.getOpenFileNames(
            self,
            "选择用例文件",
            "",
            "用例文件 (*.txt *.cfg);;所有文件 (*.*)" # 文件过滤器
        )
        if not paths: return # 如果用户取消选择，直接返回

        for path in paths: # 遍历用户选择的所有文件路径
            if path not in self.case_files: # 避免重复加载同一个文件
                self.case_files.append(path) # 添加到用例文件列表
                # 尝试解析 base 和 block 参数
                base, block = self.parse_base_block_from_path(path)
                if base is not None:
                    self.base_input.setText(base) # 设置 BASE 输入框
                if block is not None:
                    self.block_input.setText(block) # 设置 BLOCK 输入框

        self.parse_case_files()  # 解析所有已加载的用例文件，更新用例树
        self.save_config() # 保存配置

    def parse_case_files(self):
        """解析用例文件"""
        progress = ProgressWindow(self, "解析用例", "正在解析用例文件...")

        # 在子线程中执行文件解析
        def parse_task():
            results = []
            total = len(self.case_files)

            try:
                for i, case_file in enumerate(self.case_files):
                    # 使用缓存数据或解析新文件
                    cached_data = self.cache_manager.get_cached_data(case_file)
                    if cached_data:
                        results.append(cached_data)
                    else:
                        file_data = self.parse_single_file(case_file)
                        self.cache_manager.save_cache(case_file, file_data)
                        results.append(file_data)

                    # 使用 QMetaObject.invokeMethod 更新进度
                    QMetaObject.invokeMethod(
                        progress.progress,
                        "setValue",
                        Qt.ConnectionType.QueuedConnection,
                        Q_ARG(int, (i + 1) * 100 // total)
                    )

                return results

            except Exception as e:
                print(f"解析文件失败: {str(e)}")
                return []

        # 清空树形控件的操作确保在主线程中执行
        self.case_tree.clear()

        # 启动异步任务
        self.async_task_manager.run_async(parse_task)

    def handle_task_completed(self, result):
        """处理异步任务完成，在主线程中执行GUI更新"""
        try:
            if isinstance(result, list):
                # 在主线程中更新GUI
                for file_data in result:
                    self.build_tree_from_cache(file_data)
                self.status_bar.showMessage("用例解析完成", 3000)
            else:
                print(f"收到未知类型的任务结果: {type(result)}")

        except Exception as e:
            print(f"处理任务结果时出错: {str(e)}")
            self.status_bar.showMessage("处理任务结果失败", 3000)

    def handle_task_error(self, error_msg):
        """处理异步任务错误，在主线程中显示错误消息"""
        print(f"任务执行错误: {error_msg}")
        self.status_bar.showMessage("任务执行失败", 3000)
        QMessageBox.critical(self, "错误", f"任务执行失败：\n{error_msg}")

    def parse_single_file(self, case_file, pattern=r'\[case\s+([\w_]+)(?:\s*:\s*([\w_]+))?.*'):
        """解析单个文件"""
        file_data = {
            'name': os.path.basename(case_file),
            'nodes': {},
            'child_cases': []
        }

        # 使用生成器读取文件，减少内存使用
        def read_large_file(file_path, chunk_size=8192):
            with open(file_path, 'r') as f:
                buffer = ''
                while True:
                    chunk = f.read(chunk_size)
                    if not chunk:
                        if buffer:
                            yield buffer
                        break
                    buffer += chunk
                    while '\n' in buffer:
                        line, buffer = buffer.split('\n', 1)
                        yield line

        # 解析文件内容
        for line in read_large_file(case_file):
            if match := re.match(pattern, line.strip()):
                case, base = match.groups()
                if base:
                    file_data['child_cases'].append((case, base))
                else:
                    file_data['nodes'][case] = []

        return file_data

    def build_tree_from_cache(self, file_data):
        """从缓存数据构建树"""
        file_root = QTreeWidgetItem([file_data['name'], ""])
        self.case_tree.addTopLevelItem(file_root)

        # 添加根节点
        nodes = {}
        for case in file_data['nodes']:
            item = QTreeWidgetItem([case, ""])
            nodes[case] = item
            file_root.addChild(item)

        # 添加子节点
        for case, base in file_data['child_cases']:
            if base in nodes:
                parent = nodes[base]
                item = QTreeWidgetItem(parent, [case, base])
                nodes[case] = item
            else:
                parent_item = QTreeWidgetItem([base, ""])
                nodes[base] = parent_item
                file_root.addChild(parent_item)
                item = QTreeWidgetItem(parent_item, [case, base])
                nodes[case] = item

        # 展开树
        file_root.setExpanded(True)

    def find_parent(self, base_case):
        """根据基础用例名查找父节点（未使用）"""
        items = self.case_tree.findItems(base_case, Qt.MatchContains|Qt.MatchRecursive, 0)
        return items if items else self.case_tree

    def filter_cases(self, text):
        """过滤用例树，根据关键字显示或隐藏用例"""
        keyword = text.strip().lower() # 获取过滤关键字，转换为小写

        def filter_item(item):
            """递归过滤树节点"""
            # 获取当前项的文本，转换为小写
            item_text = item.text(0).lower()
            item_visible = keyword in item_text # 检查关键字是否在用例名中

            # 遍历所有子项，递归过滤
            child_visible = False
            for i in range(item.childCount()):
                child = item.child(i)
                # 递归处理子项
                if filter_item(child):
                    child_visible = True # 子项可见，则父项也应该可见

            # 如果当前项或任何子项可见，则显示当前项
            visible = item_visible or child_visible
            item.setHidden(not visible) # 设置节点隐藏状态

            # 如果当前项可见，确保其所有父节点也可见，逐级向上显示父节点
            if visible:
                parent = item.parent()
                while parent:
                    parent.setHidden(False)
                    parent = parent.parent()

            return visible # 返回当前项是否可见

        # 从根节点开始过滤
        root = self.case_tree.invisibleRootItem() # 获取不可见的根节点
        for i in range(root.childCount()): # 遍历顶层节点（文件节点）
            filter_item(root.child(i)) # 过滤每个文件节点下的用例

    def update_case_input(self):
        """更新用例输入框，显示选中的用例名"""
        # 获取选中项列表（可能是多个项）
        selected_items = self.case_tree.selectedItems()

        if selected_items:  # 确保列表不为空
            # 过滤掉文件名节点（顶层节点），只处理用例节点
            valid_items = [item for item in selected_items if item.parent() is not None]
            if valid_items:
                # 显示选中的用例数量
                if len(valid_items) == 1: # 如果只选中一个用例
                    self.case_input.setText(valid_items[0].text(0)) # 显示用例名
                    # 获取用例所在的文件（顶层节点）
                    top_item = valid_items[0]
                    while top_item.parent():
                        top_item = top_item.parent() # 向上找到顶层文件节点

                    # 查找对应的文件路径
                    file_name = top_item.text(0) # 获取文件名
                    for file_path in self.case_files: # 遍历已加载的用例文件列表
                        if os.path.basename(file_path) == file_name: # 找到匹配的文件路径
                            # 解析并更新 base 和 block 参数
                            base, block = self.parse_base_block_from_path(file_path)
                            if base is not None:
                                self.base_input.setText(base) # 更新 BASE 输入框
                            if block is not None:
                                self.block_input.setText(block) # 更新 BLOCK 输入框
                            break # 找到文件后跳出循环
                else:
                    self.case_input.setText(f"已选择 {len(valid_items)} 个用例") # 显示已选择用例数量
            else:
                self.case_input.clear() # 如果只选中文件名节点，清空用例输入框
        else:
            self.case_input.clear()  # 没有选中任何用例，清空用例输入框

    def auto_cleanup(self):
        """自动清理已完成或失败的标签页 - 仅清理进程和内存资源，不关闭标签页"""
        for i in range(self.tab_widget.count()):
            tab = self.tab_widget.widget(i)
            if isinstance(tab, CaseTab):
                # 只清理进程资源，不关闭标签页
                if not tab.process or tab.process.state() != QProcess.Running:
                    # 停止定时器
                    if hasattr(tab, 'log_timer'):
                        tab.log_timer.stop()
                    # 清空缓冲区
                    if hasattr(tab, 'log_buffer'):
                        tab.log_buffer.clear()
                    # 释放进程
                    tab.process = None

                    # 建议进行垃圾回收
                    import gc
                    gc.collect()

    def update_preview(self):
        """更新命令预览"""
        try:
            # 获取当前模式
            mode = "normal"
            if hasattr(self, 'sim_only_check') and self.sim_only_check.isChecked():
                mode = "R"
            elif hasattr(self, 'compile_only_check') and self.compile_only_check.isChecked():
                mode = "C"

            # 获取当前用例名称
            case_name = None
            if hasattr(self, 'case_input'):
                case_name = self.case_input.text().strip()
                if case_name and case_name.startswith("已选择"):
                    case_name = None

            # 生成命令
            command = self.generate_command(mode, case_name)

            # 更新预览文本
            if hasattr(self, 'preview_edit'):
                self.preview_edit.setText(f"当前命令预览:\n{command}")

                # 根据命令长度调整颜色
                if len(command) > 200:
                    self.preview_edit.setStyleSheet("""
                        QTextEdit {
                            font-family: 'Consolas', 'Courier New', monospace;
                            font-size: 9pt;
                            background-color: #2d2d2d;
                            color: #ffab91;
                        }
                    """)
                else:
                    self.preview_edit.setStyleSheet("""
                        QTextEdit {
                            font-family: 'Consolas', 'Courier New', monospace;
                            font-size: 9pt;
                            background-color: #2d2d2d;
                            color: #e6e6e6;
                        }
                    """)
        except Exception as e:
            if hasattr(self, 'preview_edit'):
                self.preview_edit.setText(f"预览生成失败: {str(e)}")
                self.preview_edit.setStyleSheet("""
                    QTextEdit {
                        font-family: 'Consolas', 'Courier New', monospace;
                        font-size: 9pt;
                        background-color: #2d2d2d;
                        color: #ff7043;
                    }
                """)

    # ------------------ 配置管理 ------------------
    def toggle_fsdb_input(self, state=None):
        """启用/禁用 FSDB tcl 文件选择按钮，当勾选 FSDB 或 VWDB 时启用"""
        # 当 FSDB 或 VWDB 任一被选中时，启用 TCL 文件选择
        enabled = self.fsdb_check.isChecked() or self.vwdb_check.isChecked()
        self.fsdb_btn.setEnabled(enabled) # 设置 FSDB 文件选择按钮状态
        if not enabled:
            self.clear_fsdb_file() # 如果都不选中，清除 FSDB 文件

    def select_fsdb_file(self):
        """选择 FSDB tcl 文件"""
        path, _ = QFileDialog.getOpenFileName(
            self,
            "选择TCL文件",
            "",
            "TCL文件 (*.tcl);;所有文件 (*.*)" # 文件过滤器
        )
        if path: # 如果用户选择了文件
            self.fsdb_file = path # 记录 FSDB 文件路径
            self.fsdb_label.setText(os.path.basename(path)) # 显示文件名
            self.fsdb_clear_btn.setEnabled(True) # 启用清除按钮
            self.save_config() # 保存配置

    def clear_fsdb_file(self):
        """清除 FSDB tcl 文件路径和显示"""
        self.fsdb_file = "" # 清空 FSDB 文件路径
        self.fsdb_label.setText("未选择TCL文件") # 更新标签显示
        self.fsdb_clear_btn.setEnabled(False) # 禁用清除按钮
        self.save_config() # 保存配置

    def handle_mode_change(self, state):
        """处理仅仿真和仅编译复选框的互斥，只允许单选"""
        sender = self.sender() # 获取信号发送者
        if state == Qt.Checked: # 如果复选框被选中
            if sender == self.sim_only_check: # 如果是仅仿真复选框
                self.compile_only_check.setChecked(False) # 取消选中仅编译复选框
            else: # 如果是仅编译复选框
                self.sim_only_check.setChecked(False) # 取消选中仅仿真复选框

    def save_config(self):
        """保存配置到文件"""
        config = {}

        # 保存窗口状态
        window_config = {
            'maximized': self.isMaximized(),
        }

        if not self.isMaximized():
            # 只在非最大化状态下保存窗口位置和大小
            geometry = self.geometry()
            window_config.update({
                'x': geometry.x(),
                'y': geometry.y(),
                'width': geometry.width(),
                'height': geometry.height()
            })

        config['window'] = window_config

        config.update({
            "base": self.base_input.text(), # BASE 参数
            "block": self.block_input.text(), # BLOCK 参数
            "case_files": self.case_files,  # 保存所有用例文件路径列表
            "rundir": self.rundir_input.text(),  # 工作目录
            "other_options": self.other_options_input.text(),  # 其他选项
            "fsdb": self.fsdb_check.isChecked(), # FSDB 波形选项
            "vwdb": self.vwdb_check.isChecked(),  # VWDB 波形选项
            "cl": self.cl_check.isChecked(),  # Clean INCA_libs 选项
            "fsdb_file": self.fsdb_file,  # FSDB tcl 文件路径
            "dump_sva": self.sva_check.isChecked(), # SVA 断言选项
            "cov": self.cov_check.isChecked(), # 覆盖率选项
            "upf": self.upf_check.isChecked(),  # UPF 仿真选项
            "sim_only": self.sim_only_check.isChecked(),  # 仅仿真选项
            "compile_only": self.compile_only_check.isChecked(),  # 仅编译选项
            "dump_mem": self.dump_mem_input.text(), # Dump Memory 参数
            "wdd": self.wdd_input.text(),  # 波形 Dump 起始时间
            "seed": self.seed_input.text(),  # 种子号
            "simarg": self.simarg_input.text(), # 仿真参数
            "cfg_def": self.cfg_def_input.text(), # 配置定义
            "post": self.post_input.text(),  # 后仿参数
            "regr_file": self.regr_file, # 回归列表文件路径
            "fm_checked": self.fm_check.isChecked(),
            "bp_server": self.bp_input.text(),
            "last_command": self.history_combo.currentText() if self.history_combo.count() > 0 else "", # 最后执行的命令
        })

        with open('runsim_config.json', 'w') as f:
            json.dump(config, f, indent=4)

    def load_config(self):
        """从文件加载配置"""
        if not os.path.exists('runsim_config.json'):
            return

        try:
            with open('runsim_config.json', 'r') as f:
                config = json.load(f)

            # 设置默认窗口大小和位置
            default_width = 1200
            default_height = 800
            screen = QApplication.primaryScreen()
            screen_geometry = screen.availableGeometry()

            if 'window' in config:
                window_config = config['window']

                # 检查是否最大化
                if window_config.get('maximized', False):
                    self.showMaximized()
                else:
                    # 获取保存的窗口位置和大小，并确保在合理范围内
                    width = min(window_config.get('width', default_width), screen_geometry.width())
                    height = min(window_config.get('height', default_height), screen_geometry.height())
                    x = window_config.get('x', (screen_geometry.width() - width) // 2)
                    y = window_config.get('y', (screen_geometry.height() - height) // 2)

                    # 确保窗口完全在屏幕内
                    x = max(0, min(x, screen_geometry.width() - width))
                    y = max(0, min(y, screen_geometry.height() - height))

                    self.setGeometry(x, y, width, height)
            else:
                # 如果没有窗口配置，使用默认值
                x = (screen_geometry.width() - default_width) // 2
                y = (screen_geometry.height() - default_height) // 2
                self.setGeometry(x, y, default_width, default_height)

            # 恢复基本参数
            self.base_input.setText(config.get("base", "")) # 恢复 BASE 参数
            self.block_input.setText(config.get("block", "")) # 恢复 BLOCK 参数
            self.rundir_input.setText(config.get("rundir", ""))  # 恢复工作目录
            self.other_options_input.setText(config.get("other_options", ""))  # 恢复其他选项

            # 恢复用例文件和相关状态
            if config.get("case_files"):
                self.case_files = config["case_files"] # 恢复用例文件列表
                self.parse_case_files() # 解析用例文件，更新用例树

            # 恢复波形配置
            self.fsdb_check.setChecked(config.get("fsdb", False)) # 恢复 FSDB 波形选项
            self.vwdb_check.setChecked(config.get("vwdb", False))  # 恢复 VWDB 波形选项
            self.cl_check.setChecked(config.get("cl", False))  # 恢复 Clean INCA_libs 选项
            # 恢复 FSDB tcl 文件
            self.fsdb_file = config.get("fsdb_file", "") # 恢复 FSDB 文件路径
            self.fsdb_label.setText(os.path.basename(self.fsdb_file) if self.fsdb_file else "未选择TCL文件") # 更新 FSDB 文件标签显示
            self.fsdb_btn.setEnabled(config.get("fsdb", False)) # 恢复 FSDB 文件按钮状态
            self.fsdb_clear_btn.setEnabled(bool(self.fsdb_file)) # 恢复 FSDB 清除按钮状态
            self.sva_check.setChecked(config.get("dump_sva", False)) # 恢复 SVA 断言选项
            self.sim_only_check.setChecked(config.get("sim_only", False))  # 恢复仅仿真选项
            self.compile_only_check.setChecked(config.get("compile_only", False))  # 恢复仅编译选项
            self.cov_check.setChecked(config.get("cov", False))  # 恢复覆盖率选项
            self.upf_check.setChecked(config.get("upf", False))  # 恢复 UPF 选项
            self.dump_mem_input.setText(config.get("dump_mem", "")) # 恢复 Dump Memory 参数
            self.wdd_input.setText(config.get("wdd", ""))  # 恢复波形 Dump 起始时间
            self.seed_input.setText(config.get("seed", ""))  # 恢复种子号
            self.simarg_input.setText(config.get("simarg", "")) # 恢复仿真参数
            self.cfg_def_input.setText(config.get("cfg_def", "")) # 恢复配置定义
            self.post_input.setText(config.get("post", ""))  # 恢复后仿参数

            # 加载回归测试相关配置
            self.fm_check.setChecked(config.get("fm_checked", False))
            self.bp_input.setText(config.get("bp_server", ""))

            # 恢复回归文件
            self.regr_file = config.get("regr_file", "") # 恢复回归列表文件路径
            self.regr_label.setText(os.path.basename(self.regr_file) if self.regr_file else "未选择回归文件") # 更新回归文件标签显示
            self.clear_regr_btn.setEnabled(bool(self.regr_file))  # 根据是否有回归文件设置清除按钮状态

            # 恢复最后一条命令到历史记录下拉框
            if last_cmd := config.get("last_command"):
                if self.history_combo.findText(last_cmd) == -1: # 避免重复添加
                    self.history_combo.insertItem(0, last_cmd) # 插入到最前面
                    self.history_combo.setCurrentIndex(0) # 设置为当前项

            self.status_bar.showMessage("配置加载完成", 2000) # 状态栏显示加载完成信息
        except Exception as e:
            QMessageBox.warning(self, "加载失败", f"配置加载错误: {str(e)}") # 弹窗显示加载失败信息

    # ------------------ 历史记录管理 ------------------
    def save_history(self, command):
        """保存命令到历史记录，并更新历史记录下拉框和文件"""
        try:
            # 避免重复添加相同的命令
            if self.history_combo.findText(command) == -1:
                # 添加到下拉框
                self.history_combo.insertItem(0, command)
                self.history_combo.setCurrentIndex(0)

                # 限制下拉框中的历史记录数量
                while self.history_combo.count() > self.history_limit:
                    self.history_combo.removeItem(self.history_combo.count() - 1)

                # 从文件加载现有历史记录
                history = []
                if os.path.exists(self.HISTORY_FILE):
                    try:
                        with open(self.HISTORY_FILE, 'r', encoding='utf-8') as f:
                            history = json.load(f)
                    except:
                        history = []

                # 确保 history 是列表类型
                if not isinstance(history, list):
                    history = []

                # 添加新命令，包含时间戳
                new_record = {
                    "command": command,
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }

                # 将新命令添加到列表开头
                history.insert(0, new_record)

                # 限制文件中的历史记录数量
                history = history[:self.history_limit]

                # 保存到文件
                with open(self.HISTORY_FILE, 'w', encoding='utf-8') as f:
                    json.dump(history, f, indent=4, ensure_ascii=False)

            # 保存配置以更新最后使用的命令
            self.save_config()

        except Exception as e:
            print(f"保存历史记录失败: {str(e)}")

    def load_history(self):
        """从文件加载命令历史记录"""
        self.history_combo.clear()

        try:
            if os.path.exists(self.HISTORY_FILE):
                with open(self.HISTORY_FILE, 'r', encoding='utf-8') as f:
                    history = json.load(f)

                # 确保 history 是列表类型
                if isinstance(history, list):
                    # 按时间戳倒序排序
                    history.sort(key=lambda x: x.get('timestamp', ''), reverse=True)

                    # 添加到下拉框
                    for record in history:
                        if isinstance(record, dict) and 'command' in record:
                            self.history_combo.addItem(record['command'])

                    # 限制数量
                    while self.history_combo.count() > self.history_limit:
                        self.history_combo.removeItem(self.history_combo.count() - 1)
        except Exception as e:
            print(f"加载历史记录失败: {str(e)}")

    def clear_history(self):
        """清除命令历史记录，删除历史记录文件并清空下拉框"""
        if os.path.exists(self.HISTORY_FILE): # 检查历史记录文件是否存在
            os.remove(self.HISTORY_FILE) # 删除历史记录文件
        self.history_combo.clear() # 清空历史记录下拉框

    def apply_history(self, index):
        """应用选中的历史命令到 GUI 输入框"""
        if 0 <= index < self.history_combo.count():
            cmd = self.history_combo.itemText(index)
            if not cmd:
                return

            # 清空所有输入框
            self.base_input.clear()
            self.block_input.clear()
            self.case_input.clear()
            self.rundir_input.clear()
            self.other_options_input.clear()
            self.fsdb_check.setChecked(False)
            self.vwdb_check.setChecked(False)
            self.cl_check.setChecked(False)
            self.sva_check.setChecked(False)
            self.cov_check.setChecked(False)
            self.upf_check.setChecked(False)
            self.sim_only_check.setChecked(False)
            self.compile_only_check.setChecked(False)
            self.dump_mem_input.clear()
            self.wdd_input.clear()
            self.seed_input.clear()
            self.simarg_input.clear()
            self.cfg_def_input.clear()
            self.post_input.clear()
            self.clear_regr_file()
            self.clear_fsdb_file()

            # 解析命令参数
            parts = cmd.split()
            i = 0
            while i < len(parts):
                part = parts[i]
                if part == "-base":
                    self.base_input.setText(parts[i+1])
                    i += 2
                elif part == "-block":
                    self.block_input.setText(parts[i+1])
                    i += 2
                elif part == "-case":
                    self.case_input.setText(parts[i+1])
                    i += 2
                elif part == "-rundir":
                    self.rundir_input.setText(parts[i+1])
                    i += 2
                elif part == "-fsdb":
                    self.fsdb_check.setChecked(True)
                    if i+1 < len(parts) and not parts[i+1].startswith("-"):
                        self.fsdb_file = parts[i+1]
                        self.fsdb_label.setText(os.path.basename(parts[i+1]))
                        self.fsdb_clear_btn.setEnabled(True)
                        i += 2
                    else:
                        i += 1
                elif part == "-vwdb":
                    self.vwdb_check.setChecked(True)
                    if i+1 < len(parts) and not parts[i+1].startswith("-"):
                        self.fsdb_file = parts[i+1]
                        self.fsdb_label.setText(os.path.basename(parts[i+1]))
                        self.fsdb_clear_btn.setEnabled(True)
                        i += 2
                    else:
                        i += 1
                elif part == "-cl":
                    self.cl_check.setChecked(True)
                    i += 1
                elif part == "-dump_sva":
                    self.sva_check.setChecked(True)
                    i += 1
                elif part == "-cov":
                    self.cov_check.setChecked(True)
                    i += 1
                elif part == "-upf":
                    self.upf_check.setChecked(True)
                    i += 1
                elif part == "-R":
                    self.sim_only_check.setChecked(True)
                    i += 1
                elif part == "-C":
                    self.compile_only_check.setChecked(True)
                    i += 1
                elif part == "-dump_mem":
                    self.dump_mem_input.setText(parts[i+1])
                    i += 2
                elif part == "-wdd":
                    self.wdd_input.setText(parts[i+1])
                    i += 2
                elif part == "-seed":
                    self.seed_input.setText(parts[i+1])
                    i += 2
                elif part == "-simarg":
                    # 处理带引号的参数
                    if i+1 < len(parts) and parts[i+1].startswith('"'):
                        simarg = parts[i+1][1:]
                        j = i+2
                        while j < len(parts) and not parts[j].endswith('"'):
                            simarg += " " + parts[j]
                            j += 1
                        if j < len(parts):
                            simarg += " " + parts[j][:-1]
                        self.simarg_input.setText(simarg)
                        i = j + 1
                    else:
                        self.simarg_input.setText(parts[i+1])
                        i += 2
                elif part == "-cfg_def":
                    # 收集所有后续的非选项参数作为 cfg_def 的值
                    cfg_def_values = []
                    j = i + 1
                    while j < len(parts) and not parts[j].startswith('-'):
                        cfg_def_values.append(parts[j])
                        j += 1

                    # 如果找到了值
                    if cfg_def_values:
                        current = self.cfg_def_input.text()
                        new_values = ' '.join(cfg_def_values)
                        if current:
                            # 如果已有值，则追加新值
                            self.cfg_def_input.setText(f"{current} {new_values}")
                        else:
                            # 如果没有现有值，直接设置
                            self.cfg_def_input.setText(new_values)
                    i = j  # 更新索引到下一个选项
                elif part == "-post":
                    self.post_input.setText(parts[i+1])
                    i += 2
                elif part == "-regr":
                    self.regr_file = parts[i+1]
                    self.regr_label.setText(os.path.basename(parts[i+1]))
                    self.clear_regr_btn.setEnabled(True)
                    i += 2
                else:
                    # 其他参数作为额外选项
                    if part.startswith("-"):
                        self.other_options_input.setText(" ".join(parts[i:]))
                        break
                    i += 1

    def re_run_history(self):
        """重新执行当前选中的历史命令"""
        current_cmd = self.history_combo.currentText() # 获取当前选中的历史命令
        if not current_cmd:
            return

        # 先应用历史命令到输入框
        index = self.history_combo.currentIndex()
        self.apply_history(index)

        # 确定执行模式
        mode = "normal"
        if self.sim_only_check.isChecked():
            mode = "R"
        elif self.compile_only_check.isChecked():
            mode = "C"

        # 执行命令
        if self.regr_file:
            # 回归模式
            regr_name = os.path.basename(self.regr_file)
            command = self.generate_command(mode)

            # 如果已有该回归的标签页，先关闭
            if regr_name in self.case_tabs:
                tab_index = self.tab_widget.indexOf(self.case_tabs[regr_name])
                self.close_case_tab(tab_index)

            # 创建新标签页并执行
            case_tab = CaseTab(regr_name, command)
            self.case_tabs[regr_name] = case_tab
            self.tab_widget.addTab(case_tab, regr_name)
            self.tab_widget.setCurrentWidget(case_tab)
            case_tab.start_execution()
        else:
            # 单用例模式
            case_name = self.case_input.text()
            if not case_name:
                QMessageBox.warning(self, "错误", "请先选择用例或回归文件")
                return

            # 生成命令
            command = self.generate_command(mode, case_name)

            # 确定标签页名称
            rundir = self.rundir_input.text().strip()
            tab_name = rundir if rundir else case_name

            # 如果已有该用例的标签页，先关闭
            if tab_name in self.case_tabs:
                tab_index = self.tab_widget.indexOf(self.case_tabs[tab_name])
                self.close_case_tab(tab_index)

            # 创建新标签页并执行
            case_tab = CaseTab(tab_name, command, self)
            self.case_tabs[tab_name] = case_tab
            self.tab_widget.addTab(case_tab, tab_name)
            self.tab_widget.setCurrentWidget(case_tab)
            case_tab.start_execution()

    # ------------------ 命令执行 ------------------
    def generate_command(self, mode, case_name=None):
        """
        生成 runsim 命令。

        Args:
            mode (str): 执行模式，例如 "normal", "R", "C" 等。
            case_name (str, optional): 指定的用例名称，默认为 None。

        Returns:
            str: 生成的 runsim 命令字符串。
        """
        cmd = ["runsim"] # 初始化命令列表，runsim 命令主体

        if not self.regr_file: # 如果没有选择回归列表文件，则使用基本参数和用例参数
            if self.base_input.text(): # 如果 BASE 参数输入框不为空
                cmd += ["-base", self.base_input.text()] # 添加 -base 参数
            if self.block_input.text(): # 如果 BLOCK 参数输入框不为空
                cmd += ["-block", self.block_input.text()] # 添加 -block 参数
            if case_name:  # 如果指定了用例名称
                cmd += ["-case", case_name] # 添加 -case 参数
        else: # 如果选择了回归列表文件
            cmd += ["-regr", self.regr_file] # 添加 -regr 参数，指定回归列表文件
            # 添加-fm参数
            if self.fm_check.isChecked():
                cmd.append("-fm")
            # 添加-bp参数
            if bp_server := self.bp_input.text().strip():
                cmd += ["-bp", bp_server]

        # 添加 rundir 参数，指定工作目录
        if self.rundir_input.text().strip(): # 如果 rundir 输入框不为空
            cmd += ["-rundir", self.rundir_input.text().strip()] # 添加 -rundir 参数

        # 添加波形 dump 相关参数
        if self.fsdb_check.isChecked(): # 如果勾选了 Dump FSDB 波形
            cmd.append("-fsdb") # 添加 -fsdb 参数
            if self.fsdb_file: # 如果选择了 FSDB tcl 文件
                cmd.append(self.fsdb_file) # 添加 FSDB tcl 文件路径
        if self.vwdb_check.isChecked():  # 如果勾选了 Dump VWDB 波形
            cmd.append("-vwdb") # 添加 -vwdb 参数
            if self.fsdb_file:  # 如果选择了 FSDB tcl 文件 (复用 FSDB 的 TCL 文件)
                cmd.append(self.fsdb_file) # 添加 FSDB tcl 文件路径
        if self.cl_check.isChecked():  # 如果勾选了 Clean INCA_libs
            cmd.append("-cl") # 添加 -cl 参数
        if self.sva_check.isChecked(): # 如果勾选了 Dump SVA 断言
            cmd.append("-dump_sva") # 添加 -dump_sva 参数
        if self.cov_check.isChecked(): # 如果勾选了 收集覆盖率
            cmd.append("-cov") # 添加 -cov 参数
        if self.upf_check.isChecked():  # 如果勾选了 UPF 仿真
            cmd.append("-upf") # 添加 -upf 参数
        if self.dump_mem_input.text(): # 如果 Dump Memory 输入框不为空
            cmd += ["-dump_mem", self.dump_mem_input.text()] # 添加 -dump_mem 参数
        if self.wdd_input.text().strip():  # 如果 波形 Dump 起始时间 输入框不为空
            cmd += ["-wdd", self.wdd_input.text().strip()] # 添加 -wdd 参数
        if self.seed_input.text().strip():  # 如果 种子号 输入框不为空
            cmd += ["-seed", self.seed_input.text().strip()] # 添加 -seed 参数
        if self.simarg_input.text().strip(): # 如果 仿真参数 输入框不为空
            cmd += ["-simarg", f'"{self.simarg_input.text().strip()}"'] # 添加 -simarg 参数，参数值用双引号包裹
        if self.cfg_def_input.text().strip(): # 如果 配置定义 输入框不为空
            cmd += ["-cfg_def", self.cfg_def_input.text().strip()] # 添加 -cfg_def 参数
        if self.post_input.text().strip():  # 如果 后仿参数 输入框不为空
            cmd += ["-post", self.post_input.text().strip()] # 添加 -post 参数

        if mode != "normal": # 如果执行模式不是 normal
            cmd.append(f"-{mode}") # 添加模式参数，例如 -R, -C

        # 添加其他选项，直接添加到命令末尾
        if other_options := self.other_options_input.text().strip(): # 如果 其他选项 输入框不为空
            cmd.append(other_options) # 添加其他选项

        # 针对终端集成,添加必要的环境设置
        if HAS_TERMINAL and sys.platform != 'win32':
            # 添加工作目录切换
            if self.rundir_input.text().strip():
                prefix = f"cd {self.rundir_input.text().strip()} && "
            else:
                prefix = ""

            # 构建完整命令
            final_cmd = prefix + " ".join(cmd)

            # 添加命令执行完成提示
            final_cmd += ' ; echo "\n[Command completed]"'

            return final_cmd
        else:
            return " ".join(cmd)

    def execute_command(self, mode):
        """执行 runsim 命令，根据选择的用例和配置参数"""
        # 保存当前配置
        self.save_config()

        # 根据复选框状态确定执行模式
        if self.sim_only_check.isChecked():
            mode = "R"
        elif self.compile_only_check.isChecked():
            mode = "C"

        # 检查标签页数量
        if self.tab_widget.count() >= self.max_tabs:
            reply = QMessageBox.question(
                self,
                "标签页数量超限",
                f"已达到最大标签页数量({self.max_tabs})，是否关闭最早的标签页？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                # 关闭最早的标签页
                self.close_case_tab(0)
            else:
                return

        # 检查是否有回归列表文件
        if self.regr_file:
            regr_name = os.path.basename(self.regr_file)
            command = self.generate_command(mode)

            if regr_name in self.case_tabs:
                tab_index = self.tab_widget.indexOf(self.case_tabs[regr_name])
                self.close_case_tab(tab_index)

            case_tab = CaseTab(regr_name, command)
            self.case_tabs[regr_name] = case_tab
            self.tab_widget.addTab(case_tab, regr_name)
            self.tab_widget.setCurrentWidget(case_tab)
            case_tab.start_execution()

            self.save_history(command)
            return

        # 检查是否通过手动输入或解析指令填写了用例名称
        case_name = self.case_input.text().strip()
        if case_name and not case_name.startswith("已选择"):
            # 检查 BLOCK 参数
            if not self.block_input.text():
                QMessageBox.warning(self, "参数错误", "必须填写BLOCK参数")
                return

            # 生成命令并执行
            command = self.generate_command(mode, case_name)

            # 确定标签页名称
            rundir = self.rundir_input.text().strip()
            tab_name = rundir if rundir else case_name

            # 如果已有标签页，先关闭
            if tab_name in self.case_tabs:
                tab_index = self.tab_widget.indexOf(self.case_tabs[tab_name])
                self.close_case_tab(tab_index)

            # 创建新标签页并执行
            case_tab = CaseTab(tab_name, command, self)
            self.case_tabs[tab_name] = case_tab
            self.tab_widget.addTab(case_tab, tab_name)
            self.tab_widget.setCurrentWidget(case_tab)
            case_tab.start_execution()

            # 保存命令到历史记录
            self.save_history(command)
            return

        # 如果没有手动输入的用例名称，则检查用例树选择
        selected_items = self.case_tree.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "选择错误", "请选择要执行的用例或直接输入用例名称")
            return

        # 检查 BLOCK 参数
        if not self.block_input.text():
            QMessageBox.warning(self, "参数错误", "必须填写BLOCK参数")
            return

        # 执行选中的用例
        for item in selected_items:
            if item.parent() is None:  # 跳过文件名节点
                continue

            case_name = item.text(0)
            command = self.generate_command(mode, case_name)
            rundir = self.rundir_input.text().strip()
            tab_name = rundir if rundir else case_name

            if tab_name in self.case_tabs:
                tab_index = self.tab_widget.indexOf(self.case_tabs[tab_name])
                self.close_case_tab(tab_index)

            case_tab = CaseTab(tab_name, command, self)
            self.case_tabs[tab_name] = case_tab
            self.tab_widget.addTab(case_tab, tab_name)
            self.tab_widget.setCurrentWidget(case_tab)
            case_tab.start_execution()

            self.save_history(command)

    def stop_execution(self):
        """停止当前正在执行的进程（未使用）"""
        if self.process and self.process.state() == QProcess.Running: # 检查进程是否存在且正在运行
            if sys.platform == 'win32': # Windows 系统
                self.process.kill() # 强制结束进程
            else: # Linux/Unix 系统
                # Linux 下使用 terminate() 更温和地结束进程
                self.process.terminate() # 尝试温和结束进程
            self.log_text.append("\n[执行已终止]\n") # 日志显示已终止
            self.status_label.setText("状态: 已终止") # 状态栏显示已终止

    def handle_output(self):
        """处理进程输出（未使用）"""
        # 根据操作系统选择解码方式
        if sys.platform == 'win32': # Windows 系统
            output = bytes(self.process.readAllStandardOutput()).decode('gbk', errors='ignore') # GBK 解码
        else: # Linux/Unix 系统
            output = bytes(self.process.readAllStandardOutput()).decode('utf-8', errors='ignore') # UTF-8 解码
        self.log_text.append(output) # 添加输出到日志文本框
        self.log_text.verticalScrollBar().setValue(
            self.log_text.verticalScrollBar().maximum() # 滚动条滚动到底部
        )

    def handle_finished(self):
        """处理进程结束事件（未使用）"""
        # 重置按钮状态
        self.run_btn.setEnabled(True) # 启用执行按钮

        # 更新状态栏
        if self.process.exitCode() == 0: # 检查进程退出码
            self.status_label.setText("状态: 执行完成") # 状态栏显示执行完成
        else:
            self.status_label.setText("状态: 执行失败") # 状态栏显示执行失败

        self.process = None # 清空进程对象

    def select_regr_file(self):
        """选择回归列表文件"""
        path, _ = QFileDialog.getOpenFileName(
            self,
            "选择回归列表",
            "",
            "回归列表 (*.regr *.lst *.list *.txt);;所有文件 (*.*)" # 文件过滤器
        )
        if path: # 如果用户选择了文件
            self.regr_file = path # 记录回归列表文件路径
            self.regr_label.setText(os.path.basename(path)) # 显示文件名
            self.clear_regr_btn.setEnabled(True)  # 选择文件后启用清除按钮
            self.save_config() # 保存配置

    def clear_regr_file(self):
        """清除回归列表文件路径和显示"""
        self.regr_file = "" # 清空回归列表文件路径
        self.regr_label.setText("未选择回归文件") # 更新标签显示
        self.clear_regr_btn.setEnabled(False) # 禁用清除按钮
        self.save_config() # 保存配置

    def close_case_tab(self, index):
        """关闭标签页时清理资源"""
        tab = self.tab_widget.widget(index)
        if isinstance(tab, CaseTab):
            # 停止定时器
            if hasattr(tab, 'log_timer'):
                tab.log_timer.stop()
            # 清空缓冲区
            if hasattr(tab, 'log_buffer'):
                tab.log_buffer.clear()
            # 停止进程
            tab.stop_execution()
            # 从字典中删除
            case_name = tab.case_name
            if case_name in self.case_tabs:
                del self.case_tabs[case_name]

        self.tab_widget.removeTab(index)

        # 建议进行垃圾回收
        import gc
        gc.collect()

    def get_case_directories(self):
        """获取当前工作目录下的所有用例目录"""
        try:
            # 获取当前工作目录
            current_dir = os.getcwd()
            # 获取所有子目录
            directories = [d for d in os.listdir(current_dir)
                          if os.path.isdir(d) and
                          (os.path.exists(os.path.join(d, "INCA_libs")) or
                           os.path.exists(os.path.join(d, "log")))]
            return directories
        except Exception as e:
            print(f"获取用例目录失败: {str(e)}")
            return []

    def show_directory_dialog(self, tool_name):
        """显示目录选择对话框"""
        dialog = QDialog(self)
        dialog.setWindowTitle(f"选择要打开{tool_name}的用例目录")
        dialog.setMinimumWidth(400)

        layout = QVBoxLayout()

        # 添加目录列表
        dir_list = QListWidget()
        dir_list.setAlternatingRowColors(True)

        # 获取并添加用例目录
        directories = self.get_case_directories()
        dir_list.addItems(directories)

        layout.addWidget(dir_list)

        # 按钮区域
        btn_layout = QHBoxLayout()
        open_btn = QPushButton("打开")
        open_btn.setEnabled(False)  # 初始禁用
        cancel_btn = QPushButton("取消")

        btn_layout.addWidget(open_btn)
        btn_layout.addWidget(cancel_btn)
        layout.addLayout(btn_layout)

        dialog.setLayout(layout)

        # 连接信号
        dir_list.itemSelectionChanged.connect(
            lambda: open_btn.setEnabled(bool(dir_list.selectedItems()))
        )
        cancel_btn.clicked.connect(dialog.reject)

        # 返回选中的目录
        result = [None]
        def on_open():
            if selected := dir_list.selectedItems():
                result[0] = selected[0].text()
                dialog.accept()

        open_btn.clicked.connect(on_open)

        dialog.exec_()
        return result[0]

    def open_verdi(self):
        """打开 Verdi 波形工具"""
        # 如果当前有选中的用例标签页，优先使用该用例
        current_tab = self.tab_widget.currentWidget()
        if isinstance(current_tab, CaseTab):
            case_dir = current_tab.case_name
        else:
            # 否则弹出目录选择对话框
            case_dir = self.show_directory_dialog("Verdi")
            if not case_dir:  # 用户取消选择
                return

        # 执行打开 Verdi 的命令
        command = f'cd {case_dir} && run_verdi comp_load'
        verdi_tab = CaseTab(f"Verdi_{case_dir}", command, self)
        self.case_tabs[f"Verdi_{case_dir}"] = verdi_tab
        self.tab_widget.addTab(verdi_tab, f"Verdi_{case_dir}")
        self.tab_widget.setCurrentWidget(verdi_tab)
        verdi_tab.start_execution()

    def open_verisium(self):
        """打开 Verisium 波形工具"""
        # 如果当前有选中的用例标签页，优先使用该用例
        current_tab = self.tab_widget.currentWidget()
        if isinstance(current_tab, CaseTab):
            case_dir = current_tab.case_name
        else:
            # 否则弹出目录选择对话框
            case_dir = self.show_directory_dialog("Verisium")
            if not case_dir:  # 用户取消选择
                return

        # 执行打开 Verisium 的命令
        command = f'cd {case_dir} && run_verisium'
        verisium_tab = CaseTab(f"Verisium_{case_dir}", command, self)
        self.case_tabs[f"Verisium_{case_dir}"] = verisium_tab
        self.tab_widget.addTab(verisium_tab, f"Verisium_{case_dir}")
        self.tab_widget.setCurrentWidget(verisium_tab)
        verisium_tab.start_execution()

    def open_compile_log(self):
        """打开编译日志文件"""
        # 获取当前选中的标签页
        current_tab = self.tab_widget.currentWidget()
        if isinstance(current_tab, CaseTab):
            case_name = current_tab.case_name
            log_file = f"{case_name}/log/irun_compile.log"

            # 检查日志文件是否存在
            if not os.path.exists(log_file):
                QMessageBox.warning(self, "错误", f"找不到编译日志文件：{log_file}")
                return

            # 根据操作系统选择不同的文本编辑器并直接打开
            if sys.platform == 'win32':
                os.startfile(log_file)  # Windows下直接用默认程序打开
            else:
                subprocess.Popen(['gvim', log_file])  # Linux下使用gvim打开
        else:
            QMessageBox.warning(self, "错误", "请先选择一个用例标签页")

    def open_sim_log(self):
        """打开仿真日志文件"""
        # 获取当前选中的标签页
        current_tab = self.tab_widget.currentWidget()
        if isinstance(current_tab, CaseTab):
            case_name = current_tab.case_name
            log_file = f"{case_name}/log/irun_sim.log"

            # 检查日志文件是否存在
            if not os.path.exists(log_file):
                QMessageBox.warning(self, "错误", f"找不到仿真日志文件：{log_file}")
                return

            # 根据操作系统选择不同的文本编辑器并直接打开
            if sys.platform == 'win32':
                os.startfile(log_file)  # Windows下直接用默认程序打开
            else:
                subprocess.Popen(['gvim', log_file])  # Linux下使用gvim打开
        else:
            QMessageBox.warning(self, "错误", "请先选择一个用例标签页")

    def open_coverage(self):
        """直接打开IMC覆盖率工具"""
        try:
            # 直接使用QProcess执行imc命令
            process = QProcess()
            process.startDetached('imc')
            self.status_bar.showMessage("已启动IMC覆盖率工具", 3000)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动IMC工具失败: {str(e)}")

    def open_asm_file(self):
        """打开反汇编文件"""
        # 获取当前选中的标签页
        current_tab = self.tab_widget.currentWidget()
        if not isinstance(current_tab, CaseTab):
            QMessageBox.warning(self, "错误", "请先选择一个用例标签页")
            return

        # 获取用例目录
        case_name = current_tab.case_name
        case_dir = os.path.abspath(case_name)

        try:
            # 扫描所有sw_build目录
            sw_build_dirs = []
            for root, dirs, _ in os.walk(case_dir):
                for dir_name in dirs:
                    if dir_name.endswith("_sw_build"):
                        sw_build_dirs.append(os.path.join(root, dir_name))

            if not sw_build_dirs:
                QMessageBox.warning(self, "错误", f"在{case_name}目录下未找到*_sw_build目录")
                return

            # 收集所有.asm文件
            asm_files = []
            for sw_dir in sw_build_dirs:
                for root, _, files in os.walk(sw_dir):
                    for file in files:
                        if file.endswith(".asm"):
                            asm_files.append(os.path.join(root, file))

            if not asm_files:
                QMessageBox.warning(self, "错误", "未找到任何反汇编文件")
                return

            # 如果只找到一个文件,直接打开
            if len(asm_files) == 1:
                if sys.platform == 'win32':
                    os.startfile(asm_files[0])  # Windows下用默认程序打开
                else:
                    subprocess.Popen(['gvim', asm_files[0]])  # Linux下使用gvim打开
                return

            # 有多个文件,显示选择对话框
            dialog = QDialog(self)
            dialog.setWindowTitle("选择反汇编文件")
            dialog.setMinimumWidth(500)

            layout = QVBoxLayout()

            # 添加文件列表
            list_widget = QListWidget()
            list_widget.setAlternatingRowColors(True)

            # 按目录分组添加文件
            for sw_dir in sw_build_dirs:
                sw_dir_files = [f for f in asm_files if f.startswith(sw_dir)]
                if sw_dir_files:
                    # 添加目录分组
                    dir_item = QListWidgetItem(os.path.relpath(sw_dir, case_dir))
                    dir_item.setFlags(Qt.ItemIsEnabled)
                    dir_item.setBackground(QColor(240, 240, 240))
                    list_widget.addItem(dir_item)

                    # 添加该目录下的文件
                    for asm_file in asm_files:
                        item = QListWidgetItem("    " + os.path.basename(asm_file))
                        item.setData(Qt.UserRole, asm_file)  # 存储完整路径
                        list_widget.addItem(item)

            layout.addWidget(list_widget)

            # 按钮区域
            btn_layout = QHBoxLayout()
            open_btn = QPushButton("打开")
            open_btn.setEnabled(False)  # 初始禁用
            cancel_btn = QPushButton("取消")

            btn_layout.addWidget(open_btn)
            btn_layout.addWidget(cancel_btn)
            layout.addLayout(btn_layout)

            dialog.setLayout(layout)

            # 连接信号
            list_widget.itemSelectionChanged.connect(
                lambda: open_btn.setEnabled(bool(list_widget.selectedItems()))
            )
            cancel_btn.clicked.connect(dialog.reject)

            def open_selected():
                selected = list_widget.selectedItems()
                if selected and selected[0].data(Qt.UserRole):  # 确保选中的是文件而不是目录
                    file_path = selected[0].data(Qt.UserRole)
                    if sys.platform == 'win32':
                        os.startfile(file_path)  # Windows下用默认程序打开
                    else:
                        subprocess.Popen(['gvim', file_path])  # Linux下使用gvim打开
                    dialog.accept()

            open_btn.clicked.connect(open_selected)
            list_widget.itemDoubleClicked.connect(lambda item: open_selected() if item.data(Qt.UserRole) else None)

            # 显示对话框
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开反汇编文件时出错：{str(e)}")

    # 添加新方法
    def show_case_context_menu(self, position):
        """显示用例树的右键菜单"""
        menu = QMenu() # 创建右键菜单

        # 获取选中的项
        selected_items = self.case_tree.selectedItems() # 获取选中的用例项
        top_level_items = [] # 存储顶层文件节点

        for item in selected_items: # 遍历选中的用例项
            # 找到顶级父项，即文件节点
            parent = item # 当前项作为起始父节点
            while parent.parent(): # 向上查找父节点，直到找到顶层节点（parent 为 None）
                parent = parent.parent() # 向上移动到父节点

            if parent not in top_level_items: # 避免重复添加同一个文件节点
                top_level_items.append(parent) # 添加到顶层文件节点列表

        # 如果有选中的顶级项，添加菜单选项
        if top_level_items: # 检查是否有顶层文件节点被选中
            # 添加自动解析参数选项
            parse_action = menu.addAction("自动解析Base/Block参数") # 创建 自动解析Base/Block参数 菜单项
            parse_action.triggered.connect(lambda: self.parse_params_from_selected(top_level_items)) # 连接到解析参数方法

            menu.addSeparator() # 添加分隔符

            remove_action = menu.addAction("删除用例文件") # 创建 删除用例文件 菜单项
            remove_action.triggered.connect(lambda: self.remove_selected_case_files(top_level_items)) # 连接到删除文件方法

        # 显示菜单
        if not menu.isEmpty(): # 检查菜单是否为空，避免空菜单
            menu.exec_(self.case_tree.viewport().mapToGlobal(position)) # 在鼠标点击位置显示右键菜单

    def parse_params_from_selected(self, items):
        """从选中的用例文件解析 Base/Block 参数，并更新到输入框"""
        if not items: # 检查是否有选中的文件节点
            return # 没有选中文件，直接返回

        for item in items: # 遍历选中的文件节点
            file_name = item.text(0) # 获取文件名
            # 查找对应的文件路径
            for file_path in self.case_files[:]: # 遍历已加载的用例文件列表
                if os.path.basename(file_path) == file_name: # 找到匹配的文件路径
                    base, block = self.parse_base_block_from_path(file_path) # 解析 Base/Block 参数
                    if base is not None:
                        self.base_input.setText(base) # 更新 BASE 输入框
                    if block is not None:
                        self.block_input.setText(block) # 更新 BLOCK 输入框
                    break # 找到文件后跳出循环

        self.status_bar.showMessage("参数解析完成", 3000) # 状态栏显示解析完成信息

    def remove_selected_case_files(self, items):
        """删除选中的用例文件，从列表和 GUI 中移除"""
        if not items: # 检查是否有选中的文件节点
            return # 没有选中文件，直接返回

        # 确认删除，弹出确认对话框
        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除选中的 {len(items)} 个用例文件吗？", # 提示信息，显示要删除的文件数量
            QMessageBox.Yes | QMessageBox.No, # 按钮类型：Yes 和 No
            QMessageBox.No # 默认选择 No
        )

        if reply != QMessageBox.Yes: # 如果用户选择 No，取消删除
            return # 函数返回

        # 删除选中的文件
        for item in items: # 遍历选中的文件节点
            file_name = item.text(0) # 获取文件名
            # 查找对应的文件路径
            for file_path in self.case_files[:]: # 遍历用例文件列表的副本，避免删除时索引错乱
                if os.path.basename(file_path) == file_name: # 找到匹配的文件路径
                    self.case_files.remove(file_path) # 从用例文件列表中删除
                    break # 找到文件后跳出循环

        # 重新解析剩余文件，更新用例树
        self.parse_case_files() # 重新解析用例文件
        self.save_config() # 保存配置
        self.status_bar.showMessage("用例文件已删除", 3000) # 状态栏显示删除成功信息

    def remove_case_file(self):
        """通过按钮删除用例文件，弹出文件选择对话框"""
        if not self.case_files: # 检查是否有已加载的用例文件
            QMessageBox.information(self, "提示", "没有可删除的用例文件") # 弹窗提示没有可删除的文件
            return # 函数返回

        # 创建文件选择对话框
        dialog = QDialog(self) # 创建 QDialog 实例
        dialog.setWindowTitle("选择要删除的用例文件") # 设置对话框标题
        dialog.setMinimumWidth(400) # 设置最小宽度

        layout = QVBoxLayout() # 垂直布局

        # 创建文件列表，用于显示可删除的文件
        file_list = QTreeWidget() # 创建 QTreeWidget 实例
        file_list.setHeaderLabels(["文件名", "路径"]) # 设置表头
        file_list.setAlternatingRowColors(True) # 隔行变色
        file_list.setSelectionMode(QTreeWidget.ExtendedSelection) # 允许多选

        # 添加文件到列表
        for file_path in self.case_files: # 遍历用例文件列表
            item = QTreeWidgetItem([os.path.basename(file_path), file_path]) # 创建 QTreeWidgetItem 实例，显示文件名和路径
            file_list.addTopLevelItem(item) # 添加到文件列表

        # 调整列宽以适应内容
        file_list.resizeColumnToContents(0) # 调整第一列宽度
        file_list.resizeColumnToContents(1) # 调整第二列宽度

        layout.addWidget(file_list) # 添加文件列表到布局

        # 按钮区域，水平布局
        btn_layout = QHBoxLayout() # 水平布局
        delete_btn = QPushButton("删除选中") # 删除选中按钮
        cancel_btn = QPushButton("取消") # 取消按钮

        btn_layout.addWidget(delete_btn) # 添加删除按钮到布局
        btn_layout.addWidget(cancel_btn) # 添加取消按钮到布局

        layout.addLayout(btn_layout) # 添加按钮布局到对话框主布局
        dialog.setLayout(layout) # 设置对话框布局

        # 连接信号和槽
        delete_btn.clicked.connect(lambda: self.delete_files_from_dialog(file_list, dialog)) # 连接删除按钮点击信号到删除文件方法
        cancel_btn.clicked.connect(dialog.reject) # 连接取消按钮点击信号到对话框拒绝方法

        # 显示对话框，模态显示
        dialog.exec_() # 模态显示对话框，阻塞主线程，直到对话框关闭

    def delete_files_from_dialog(self, file_list, dialog):
        """从文件选择对话框中删除选中的文件"""
        selected_items = file_list.selectedItems() # 获取文件列表中选中的项

        if not selected_items: # 检查是否有选中的文件
            QMessageBox.information(dialog, "提示", "请选择要删除的文件") # 弹窗提示选择要删除的文件
            return # 没有选中文件，直接返回

        # 获取选中的文件路径
        selected_paths = [item.text(1) for item in selected_items] # 提取选中项的文件路径

        # 从用例文件列表中删除选中的文件路径
        for path in selected_paths: # 遍历选中的文件路径
            if path in self.case_files: # 检查文件路径是否在用例文件列表中
                self.case_files.remove(path) # 从用例文件列表中删除

        # 重新解析剩余文件，更新用例树
        self.parse_case_files() # 重新解析用例文件
        self.save_config() # 保存配置
        self.status_bar.showMessage("用例文件已删除", 3000) # 状态栏显示删除成功信息

        # 关闭对话框
        dialog.accept() # 关闭对话框，返回 accept 结果

    def get_seed_from_log(self):
        """从仿真日志文件中获取种子号，并更新到种子号输入框"""
        # 获取当前选中的标签页
        current_tab = self.tab_widget.currentWidget() # 获取当前标签页
        if not isinstance(current_tab, CaseTab): # 检查是否为 CaseTab 实例
            QMessageBox.warning(self, "错误", "请先选择一个用例标签页") # 弹窗提示先选择用例标签页
            return # 没有选择用例标签页，直接返回

        case_name = current_tab.case_name # 获取用例名称
        log_file = f"{case_name}/log/irun_sim.log" # 构建仿真日志文件路径

        if not os.path.exists(log_file): # 检查日志文件是否存在
            QMessageBox.warning(self, "错误", f"找不到仿真日志文件：{log_file}") # 弹窗提示找不到日志文件
            return # 日志文件不存在，直接返回

        try:
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f: # 打开日志文件，UTF-8 编码，忽略错误
                content = f.read() # 读取日志文件内容
                # 使用正则表达式查找种子号，匹配 -seed 后面的数字
                if match := re.search(r'-seed\s+(\d+)', content): # 正则表达式搜索种子号
                    seed = match.group(1) # 提取匹配到的种子号
                    self.seed_input.setText(seed) # 更新种子号输入框
                    self.status_bar.showMessage(f"已获取种子号：{seed}", 3000) # 状态栏显示获取成功信息
                else:
                    QMessageBox.warning(self, "错误", "在仿真日志中未找到种子号") # 弹窗提示未找到种子号
        except Exception as e:
            QMessageBox.critical(self, "错误", f"读取日志文件失败：{str(e)}") # 弹窗提示读取日志文件失败

    def parse_regr_command(self):
        """显示回归指令输入对话框并解析指令"""
        dialog = QDialog(self)
        dialog.setWindowTitle("解析回归指令")
        dialog.setMinimumWidth(600)

        layout = QVBoxLayout()

        # 添加说明标签
        hint_label = QLabel("请输入回归用例指令:")
        hint_label.setStyleSheet("color: #666;")
        layout.addWidget(hint_label)

        # 添加文本输入框
        text_edit = QTextEdit()
        text_edit.setPlaceholderText("示例: runsim -base top -block udtb/top/lowpower -case top_frc_pd_wkup_seq ...")
        text_edit.setMinimumHeight(100)
        layout.addWidget(text_edit)

        # 按钮区域
        btn_layout = QHBoxLayout()
        parse_btn = QPushButton("解析")
        cancel_btn = QPushButton("取消")

        btn_layout.addWidget(parse_btn)
        btn_layout.addWidget(cancel_btn)
        layout.addLayout(btn_layout)

        dialog.setLayout(layout)

        # 连接信号
        parse_btn.clicked.connect(lambda: self.do_parse_command(text_edit.toPlainText(), dialog))
        cancel_btn.clicked.connect(dialog.reject)

        dialog.exec_()

    def do_parse_command(self, command_text, dialog):
        """解析回归指令并填充到界面"""
        if not command_text.strip():
            QMessageBox.warning(dialog, "错误", "请输入回归指令")
            return

        try:
            # 分割命令参数
            parts = command_text.split()
            i = 0

            # 重置所有输入框和选项
            self.reset_all_inputs()

            while i < len(parts):
                part = parts[i]
                if part.startswith("-"):
                    # 移除开头的横杠以便匹配
                    option = part[1:]

                    # 处理基本参数
                    if option == "base" and i + 1 < len(parts):
                        self.base_input.setText(parts[i+1])
                        i += 2
                    elif option == "block" and i + 1 < len(parts):
                        self.block_input.setText(parts[i+1])
                        i += 2
                    elif option == "case" and i + 1 < len(parts):
                        self.case_input.setText(parts[i+1])
                        i += 2
                    elif option == "R":
                        self.sim_only_check.setChecked(True)
                        i += 1
                    elif option == "C":
                        self.compile_only_check.setChecked(True)
                        i += 1
                    elif option == "cl":
                        self.cl_check.setChecked(True)
                        i += 1
                    elif option == "fsdb":
                        self.fsdb_check.setChecked(True)
                        if i + 1 < len(parts) and not parts[i+1].startswith("-"):
                            self.fsdb_file = parts[i+1]
                            self.fsdb_label.setText(os.path.basename(parts[i+1]))
                            self.fsdb_clear_btn.setEnabled(True)
                            i += 2
                        else:
                            i += 1
                    elif option == "vwdb":
                        self.vwdb_check.setChecked(True)
                        if i + 1 < len(parts) and not parts[i+1].startswith("-"):
                            self.fsdb_file = parts[i+1]
                            self.fsdb_label.setText(os.path.basename(parts[i+1]))
                            self.fsdb_clear_btn.setEnabled(True)
                            i += 2
                        else:
                            i += 1
                    elif option == "dump_sva":
                        self.sva_check.setChecked(True)
                        i += 1
                    elif option == "cov":
                        self.cov_check.setChecked(True)
                        i += 1
                    elif option == "upf":
                        self.upf_check.setChecked(True)
                        i += 1
                    elif option == "seed" and i + 1 < len(parts):
                        self.seed_input.setText(parts[i+1])
                        i += 2
                    elif option == "dump_mem" and i + 1 < len(parts):
                        self.dump_mem_input.setText(parts[i+1])
                        i += 2
                    elif option == "wdd" and i + 1 < len(parts):
                        self.wdd_input.setText(parts[i+1])
                        i += 2
                    elif option == "cfg_def":
                        # 收集所有后续的非选项参数作为 cfg_def 的值
                        cfg_def_values = []
                        j = i + 1
                        while j < len(parts) and not parts[j].startswith('-'):
                            cfg_def_values.append(parts[j])
                            j += 1

                        # 如果找到了值
                        if cfg_def_values:
                            current = self.cfg_def_input.text()
                            new_values = ' '.join(cfg_def_values)
                            if current:
                                # 如果已有值，则追加新值
                                self.cfg_def_input.setText(f"{current} {new_values}")
                            else:
                                # 如果没有现有值，直接设置
                                self.cfg_def_input.setText(new_values)
                        i = j  # 更新索引到下一个选项
                    elif option == "simarg" and i + 1 < len(parts):
                        # 处理带引号的参数
                        if parts[i+1].startswith('"'):
                            simarg = parts[i+1][1:]
                            j = i + 2
                            while j < len(parts) and not parts[j].endswith('"'):
                                simarg += " " + parts[j]
                                j += 1
                            if j < len(parts):
                                simarg += " " + parts[j][:-1]
                            self.simarg_input.setText(simarg)
                            i = j + 1
                        else:
                            self.simarg_input.setText(parts[i+1])
                            i += 2
                    elif option == "post" and i + 1 < len(parts):
                        self.post_input.setText(parts[i+1])
                        i += 2
                    else:
                        # 其他选项作为额外选项
                        self.other_options_input.setText(part + " " + " ".join(parts[i+1:]))
                        break
                else:
                    i += 1

            dialog.accept()
            self.status_bar.showMessage("回归指令解析完成", 3000)

        except Exception as e:
            QMessageBox.critical(dialog, "错误", f"解析指令失败: {str(e)}")

    def reset_all_inputs(self):
        """重置所有输入框和选项，确保不影响多值参数的处理"""
        # 清空输入框，但保留值分隔符
        self.base_input.clear()
        self.block_input.clear()
        self.case_input.clear()
        self.rundir_input.clear()
        self.other_options_input.clear()
        self.dump_mem_input.clear()
        self.wdd_input.clear()
        self.seed_input.clear()
        self.simarg_input.clear()
        self.cfg_def_input.clear()  # 清空时不会影响多个值的处理逻辑
        self.post_input.clear()
        self.bp_input.clear()

        # 取消所有复选框
        self.fsdb_check.setChecked(False)
        self.vwdb_check.setChecked(False)
        self.cl_check.setChecked(False)
        self.sva_check.setChecked(False)
        self.sim_only_check.setChecked(False)
        self.compile_only_check.setChecked(False)
        self.cov_check.setChecked(False)
        self.upf_check.setChecked(False)
        self.fm_check.setChecked(False)

        # 清除文件选择
        self.clear_fsdb_file()
        self.clear_regr_file()

    def closeEvent(self, event):
        """关闭事件，清理资源"""
        self.async_task_manager.cleanup()
        # 清理插件
        self.plugin_manager.cleanup_plugins()
        super().closeEvent(event)

    def changeEvent(self, event):
        """处理窗口状态改变事件"""
        if event.type() == Qt.WindowState:  # 使用 Qt.WindowState 代替 Qt.WindowStateChange
            # 当窗口状态改变（最大化/还原）时保存配置
            self.save_config()
        super().changeEvent(event)

    def show_env_parse_dialog(self):
        """显示环境解析对话框"""
        dialog = QDialog(self)
        dialog.setWindowTitle("解析环境")
        dialog.setMinimumWidth(600)

        layout = QVBoxLayout()

        # 获取环境变量并显示路径
        proj_env = os.getenv('PROJ_ENV', '')
        if not proj_env:
            QMessageBox.warning(self, "警告", "未找到环境变量 $PROJ_ENV，请先设置环境变量")
            return

        # 添加环境路径显示，使用美化的框架显示
        env_frame = QGroupBox("环境信息")
        env_frame_layout = QVBoxLayout()
        env_label = QLabel(f"环境根目录: {proj_env}")
        env_label.setWordWrap(True)
        env_label.setStyleSheet("color: #444; padding: 5px;")
        env_frame_layout.addWidget(env_label)
        env_frame.setLayout(env_frame_layout)
        layout.addWidget(env_frame)

        # 扫描所有子系统目录
        subsystems = []
        try:
            for item in os.listdir(proj_env):
                full_path = os.path.join(proj_env, item)
                if os.path.isdir(full_path) and (item.endswith('_sys') or item == 'top'):
                    subsystems.append(item)

            if not subsystems:
                QMessageBox.warning(self, "警告", "未找到任何有效的子系统目录")
                return

        except Exception as e:
            QMessageBox.critical(self, "错误", f"扫描目录失败: {str(e)}")
            return

        # 创建子系统选择组
        subsys_group = QGroupBox("可用子系统")
        subsys_layout = QVBoxLayout()

        # 添加搜索过滤功能
        filter_layout = QHBoxLayout()
        filter_label = QLabel("🔍")
        filter_input = QLineEdit()
        filter_input.setPlaceholderText("输入关键字过滤子系统...")
        filter_layout.addWidget(filter_label)
        filter_layout.addWidget(filter_input)
        subsys_layout.addLayout(filter_layout)

        # 创建子系统选择列表
        subsys_list = QTreeWidget()
        subsys_list.setHeaderLabels(["子系统"])
        subsys_list.setSelectionMode(QTreeWidget.MultiSelection)
        subsys_list.setAlternatingRowColors(True)
        subsys_list.setStyleSheet("""
            QTreeWidget {
                border: 1px solid #cccccc;
                border-radius: 4px;
                background-color: white;
            }
            QTreeWidget::item {
                height: 25px;
                padding: 2px;
            }
            QTreeWidget::item:selected {
                background-color: #e6f3ff;
                color: #000;
            }
            QTreeWidget::item:hover {
                background-color: #f5f5f5;
            }
        """)

        # 添加子系统到列表
        system_items = {}
        for subsys in sorted(subsystems):
            item = QTreeWidgetItem([subsys])
            item.setToolTip(0, f"目录: {os.path.join(proj_env, subsys)}")
            subsys_list.addTopLevelItem(item)
            system_items[subsys] = item

        subsys_layout.addWidget(subsys_list)

        # 添加选择工具按钮
        tool_layout = QHBoxLayout()
        select_all_btn = QPushButton("全选")
        clear_all_btn = QPushButton("清空选择")
        tool_layout.addWidget(select_all_btn)
        tool_layout.addWidget(clear_all_btn)
        subsys_layout.addLayout(tool_layout)

        # 添加统计信息标签
        self.stats_label = QLabel(f"找到 {len(subsystems)} 个子系统")
        self.stats_label.setStyleSheet("color: #666; margin-top: 5px;")
        subsys_layout.addWidget(self.stats_label)

        subsys_group.setLayout(subsys_layout)
        layout.addWidget(subsys_group)

        # 按钮区域
        btn_layout = QHBoxLayout()
        parse_btn = QPushButton("解析")
        parse_btn.setDefault(True)
        parse_btn.setStyleSheet("""
            QPushButton {
                background-color: #4a9eff;
                color: white;
                padding: 6px 20px;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #3d8ced;
            }
            QPushButton:pressed {
                background-color: #3274bf;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)

        cancel_btn = QPushButton("取消")

        # 根据选择状态启用/禁用解析按钮
        parse_btn.setEnabled(False)

        def on_selection_changed():
            selected_count = len(subsys_list.selectedItems())
            parse_btn.setEnabled(selected_count > 0)
            self.stats_label.setText(
                f"找到 {len(subsystems)} 个子系统，已选择 {selected_count} 个"
            )

        def on_filter_changed(text):
            filter_text = text.lower()
            for subsys, item in system_items.items():
                item.setHidden(not subsys.lower().__contains__(filter_text))

        def select_all():
            subsys_list.selectAll()

        def clear_selection():
            subsys_list.clearSelection()

        # 连接信号
        subsys_list.itemSelectionChanged.connect(on_selection_changed)
        filter_input.textChanged.connect(on_filter_changed)
        select_all_btn.clicked.connect(select_all)
        clear_all_btn.clicked.connect(clear_selection)

        btn_layout.addWidget(parse_btn)
        btn_layout.addWidget(cancel_btn)
        layout.addLayout(btn_layout)

        dialog.setLayout(layout)

        # 连接信号
        parse_btn.clicked.connect(lambda: self.parse_selected_subsystems(subsys_list, dialog, proj_env))
        cancel_btn.clicked.connect(dialog.reject)

        # 设置默认大小并显示
        dialog.resize(800, 600)
        dialog.exec_()

    def parse_selected_subsystems(self, subsys_list, dialog, proj_env):
        """解析选中的子系统，生成用例树"""
        selected_items = subsys_list.selectedItems()
        if not selected_items:
            QMessageBox.warning(dialog, "警告", "请选择至少一个子系统")
            return

        # 清空原有的用例文件列表
        self.case_files.clear()

        # 创建进度窗口
        progress = ProgressWindow(self, "解析子系统", "正在解析环境配置文件...")
        progress.progress.setMinimum(0)
        progress.progress.setMaximum(100)

        try:
            total_subsys = len(selected_items)
            found_files = 0
            skipped_files = 0

            for i, item in enumerate(selected_items):
                subsys = item.text(0)
                progress.progress.setLabelText(f"正在分析 {subsys} ...")
                progress_value = (i + 1) * 100 // total_subsys
                QMetaObject.invokeMethod(
                    progress.progress,
                    "setValue",
                    Qt.ConnectionType.QueuedConnection,
                    Q_ARG(int, progress_value)
                )

                # 处理子系统目录下的配置文件
                subsys_cfg_path = os.path.join(proj_env, subsys, 'bin', 'case_cfg')
                if os.path.exists(subsys_cfg_path):
                    try:
                        for cfg in os.listdir(subsys_cfg_path):
                            if cfg.endswith('.cfg'):
                                cfg_path = os.path.join(subsys_cfg_path, cfg)
                                if cfg_path not in self.case_files:
                                    self.case_files.append(cfg_path)
                                    found_files += 1
                                else:
                                    skipped_files += 1
                    except Exception as e:
                        print(f"警告: 处理目录 {subsys_cfg_path} 时出错: {str(e)}")

                # 处理 udtb 目录，让用户选择要解析的子目录
                udtb_dirs = self.show_udtb_selection_dialog(subsys, proj_env)
                for udtb_dir in udtb_dirs:
                    try:
                        # 只在选定的目录中查找 .cfg 文件，不再递归查找
                        bin_dir = os.path.join(udtb_dir, 'bin')
                        if os.path.exists(bin_dir):
                            for file in os.listdir(bin_dir):
                                if file.endswith('.cfg'):
                                    cfg_path = os.path.join(bin_dir, file)
                                    if cfg_path not in self.case_files:
                                        self.case_files.append(cfg_path)
                                        found_files += 1
                                    else:
                                        skipped_files += 1
                    except Exception as e:
                        print(f"警告: 处理 UDTB 目录 {udtb_dir} 时出错: {str(e)}")

                # 特殊处理 usvp 目录下的配置文件
                if subsys in ['apcpu_sys', 'aon_sys', 'ch_sys', 'sp_sys', 'phy_cp_sys', 'ps_cp_sys']:
                    usvp_cfg_path = os.path.join(proj_env, 'udtb', 'usvp', 'bin', 'case_cfg')
                    if os.path.exists(usvp_cfg_path):
                        # 定义每个子系统需要解析的特定配置文件
                        cfg_patterns = {
                            'apcpu_sys': ['apcpu_subsys_case.cfg', 'apcpu_top_case.cfg'],
                            'aon_sys': ['ch_subsys_case.cfg', 'ch_top_case.cfg',
                                       'sp_subsys_case.cfg', 'sp_top_case.cfg'],
                            'ch_sys': ['ch_subsys_case.cfg', 'ch_top_case.cfg'],
                            'sp_sys': ['sp_subsys_case.cfg', 'sp_top_case.cfg'],
                            'phy_cp_sys': ['phycp_subsys_case.cfg', 'phycp_top_case.cfg'],
                            'ps_cp_sys': ['pscp_subsys_case.cfg', 'pscp_top_case.cfg']
                        }

                        try:
                            # 添加匹配的配置文件
                            for pattern in cfg_patterns.get(subsys, []):
                                cfg_path = os.path.join(usvp_cfg_path, pattern)
                                if os.path.exists(cfg_path):
                                    if cfg_path not in self.case_files:
                                        self.case_files.append(cfg_path)
                                        found_files += 1
                                    else:
                                        skipped_files += 1
                        except Exception as e:
                            print(f"警告: 处理 USVP 配置文件时出错: {str(e)}")

            if not self.case_files:
                QMessageBox.warning(dialog, "警告", "未找到任何配置文件！")
                return

            # 解析完成后更新用例树
            self.parse_case_files()
            self.save_config()
            dialog.accept()

            # 清空用例输入框,以避免冲突
            self.case_input.clear()

            # 更新状态栏，显示详细信息
            status_msg = f"环境解析完成: 发现 {found_files} 个配置文件"
            if skipped_files > 0:
                status_msg += f", 跳过 {skipped_files} 个重复文件"
            self.status_bar.showMessage(status_msg, 5000)

        except Exception as e:
            QMessageBox.critical(dialog, "错误", f"解析子系统失败: {str(e)}")
        finally:
            progress.close()

    def show_udtb_selection_dialog(self, subsys, proj_env):
        """显示 UDTB 目录选择对话框"""
        udtb_path = os.path.join(proj_env, 'udtb', subsys)
        if not os.path.exists(udtb_path):
            return []

        dialog = QDialog(self)
        dialog.setWindowTitle(f"选择 {subsys} UDTB 目录")
        dialog.setMinimumWidth(600)

        layout = QVBoxLayout()

        # 添加说明标签
        hint_label = QLabel(f"选择 {subsys} 的 UDTB 目录用于解析配置文件:")
        hint_label.setStyleSheet("color: #444; margin-bottom: 10px;")
        layout.addWidget(hint_label)

        # 添加搜索过滤功能
        filter_layout = QHBoxLayout()
        filter_label = QLabel("🔍")
        filter_input = QLineEdit()
        filter_input.setPlaceholderText("输入关键字过滤目录...")
        filter_layout.addWidget(filter_label)
        filter_layout.addWidget(filter_input)
        layout.addLayout(filter_layout)

        # 创建目录列表
        list_widget = QListWidget()
        list_widget.setSelectionMode(QAbstractItemView.SelectionMode.MultiSelection)
        list_widget.setAlternatingRowColors(True)
        list_widget.setStyleSheet("""
            QListWidget {
                border: 1px solid #cccccc;
                border-radius: 4px;
                background-color: white;
            }
            QListWidget::item {
                height: 25px;
                padding: 2px 5px;
            }
            QListWidget::item:selected {
                background-color: #e6f3ff;
                color: #000;
            }
            QListWidget::item:hover {
                background-color: #f5f5f5;
            }
        """)

        # 存储所有列表项以便过滤
        list_items = {}

        # 获取所有包含 bin 目录的子目录
        for root, dirs, _ in os.walk(udtb_path):
            if 'bin' in dirs:
                # 获取相对路径
                rel_path = os.path.relpath(root, udtb_path)
                if rel_path != '.':  # 排除当前目录
                    item = QListWidgetItem(rel_path)
                    item.setToolTip(f"完整路径: {root}")
                    list_widget.addItem(item)
                    list_items[rel_path] = item

        layout.addWidget(list_widget)

        # 添加按钮行
        btn_layout = QHBoxLayout()
        select_all_btn = QPushButton("全选")
        clear_btn = QPushButton("清空选择")
        btn_layout.addWidget(select_all_btn)
        btn_layout.addWidget(clear_btn)
        layout.addLayout(btn_layout)

        # 添加统计标签
        stats_label = QLabel(f"共找到 {len(list_items)} 个目录")
        stats_label.setStyleSheet("color: #666; margin-top: 5px;")
        layout.addWidget(stats_label)

        # 添加确定取消按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok |
            QDialogButtonBox.StandardButton.Cancel
        )
        ok_button = button_box.button(QDialogButtonBox.StandardButton.Ok)
        ok_button.setText("确定")
        cancel_button = button_box.button(QDialogButtonBox.StandardButton.Cancel)
        cancel_button.setText("取消")

        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        # 实现过滤功能
        def filter_items(text):
            filter_text = text.lower()
            for path, item in list_items.items():
                item.setHidden(not path.lower().__contains__(filter_text))

        def update_stats():
            selected_count = len(list_widget.selectedItems())
            visible_count = sum(1 for item in list_items.values() if not item.isHidden())
            if selected_count > 0:
                stats_label.setText(f"共 {len(list_items)} 个目录，显示 {visible_count} 个，已选择 {selected_count} 个")
            else:
                stats_label.setText(f"共 {len(list_items)} 个目录，显示 {visible_count} 个")

        def select_all():
            list_widget.selectAll()
            update_stats()

        def clear_selection():
            list_widget.clearSelection()
            update_stats()

        # 连接信号
        filter_input.textChanged.connect(filter_items)
        select_all_btn.clicked.connect(select_all)
        clear_btn.clicked.connect(clear_selection)
        list_widget.itemSelectionChanged.connect(update_stats)
        filter_input.textChanged.connect(update_stats)

        # 设置对话框默认大小
        dialog.resize(800, 600)
        dialog.setLayout(layout)

        # 获取选择结果
        selected_dirs = []
        if dialog.exec() == QDialog.DialogCode.Accepted:
            selected_items = list_widget.selectedItems()
            if not selected_items:
                if QMessageBox.question(
                    dialog,
                    "确认",
                    "您没有选择任何目录，是否继续？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                ) == QMessageBox.No:
                    return self.show_udtb_selection_dialog(subsys, proj_env)

            for item in selected_items:
                selected_dir = os.path.join(udtb_path, item.text())
                selected_dirs.append(selected_dir)

        return selected_dirs

if __name__ == '__main__':
    app = QApplication(sys.argv) # 创建 QApplication 实例
    ex = RunSimGUI() # 创建 RunSimGUI 实例
    ex.show() # 显示主窗口
    sys.exit(app.exec_()) # 运行 QApplication 应用，进入事件循环
