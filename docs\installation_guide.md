# RunSim GUI 安装指南

本文档提供了 RunSim GUI 应用程序的安装指南，包括环境要求、安装步骤和常见问题解决方法。

## 1. 环境要求

### 1.1 操作系统

RunSim GUI 支持以下操作系统：

- Windows 10/11
- Linux (Ubuntu 18.04+, CentOS 7+)
- macOS 10.14+

### 1.2 Python 环境

- Python 3.8 或更高版本
- pip 包管理器

### 1.3 硬件要求

- 处理器：双核 2GHz 或更高
- 内存：4GB 或更高
- 磁盘空间：500MB 可用空间

## 2. 安装步骤

### 2.1 安装 Python

#### 2.1.1 Windows

1. 访问 [Python 官网](https://www.python.org/downloads/)
2. 下载 Python 3.8 或更高版本的安装程序
3. 运行安装程序，勾选"Add Python to PATH"选项
4. 完成安装

#### 2.1.2 Linux

Ubuntu/Debian:

```bash
sudo apt update
sudo apt install python3 python3-pip
```

CentOS/RHEL:

```bash
sudo yum install python3 python3-pip
```

#### 2.1.3 macOS

使用 Homebrew:

```bash
brew install python
```

或者访问 [Python 官网](https://www.python.org/downloads/) 下载安装程序。

### 2.2 安装 RunSim GUI

#### 2.2.1 从源代码安装

1. 克隆或下载 RunSim GUI 源代码

```bash
git clone https://github.com/your-organization/runsim-gui.git
cd runsim-gui
```

2. 安装依赖

```bash
pip install -r requirements.txt
```

#### 2.2.2 使用安装包安装

1. 下载适用于您操作系统的安装包
2. 运行安装程序，按照提示完成安装

### 2.3 验证安装

运行以下命令启动 RunSim GUI：

```bash
python run_app.py
```

如果安装成功，应该会看到 RunSim GUI 的主窗口。

## 3. 依赖项

RunSim GUI 依赖以下 Python 包：

- PyQt5 5.15+：GUI 框架
- numpy：数学计算
- matplotlib：图表绘制
- pandas：数据处理
- psutil：系统资源监控

这些依赖项会在安装过程中自动安装。

## 4. 配置

### 4.1 配置文件

RunSim GUI 使用以下配置文件：

- `runsim_config.json`：应用程序配置
- `plugin_config.json`：插件配置

这些文件会在应用程序首次运行时自动创建。

### 4.2 环境变量

RunSim GUI 使用以下环境变量：

- `RUNSIM_HOME`：RunSim 安装目录
- `RUNSIM_PLUGINS`：插件目录（可选）

## 5. 常见问题

### 5.1 安装问题

#### 5.1.1 依赖项安装失败

问题：安装依赖项时出现错误。

解决方法：

1. 确保您的 pip 是最新版本：

```bash
pip install --upgrade pip
```

2. 尝试单独安装有问题的依赖项：

```bash
pip install PyQt5
```

3. 如果是 PyQt5 安装问题，可以尝试使用 conda 安装：

```bash
conda install pyqt
```

#### 5.1.2 找不到 Python

问题：运行 `python run_app.py` 时提示找不到 Python。

解决方法：

1. 确保 Python 已添加到 PATH 环境变量
2. 尝试使用 `python3` 而不是 `python`：

```bash
python3 run_app.py
```

### 5.2 运行问题

#### 5.2.1 GUI 无法启动

问题：运行 `python run_app.py` 时没有显示 GUI。

解决方法：

1. 检查错误消息
2. 确保 PyQt5 已正确安装
3. 在 Linux 上，确保 X11 或 Wayland 正在运行

#### 5.2.2 插件加载失败

问题：插件无法加载。

解决方法：

1. 检查 `plugin_config.json` 文件
2. 确保插件文件存在
3. 检查插件是否与当前版本兼容

## 6. 卸载

### 6.1 从源代码安装的卸载

1. 删除 RunSim GUI 目录
2. 可选：卸载依赖项

```bash
pip uninstall -r requirements.txt
```

### 6.2 使用安装包安装的卸载

1. 使用操作系统的卸载程序卸载 RunSim GUI
2. 删除配置文件（如果需要）

## 7. 更新

### 7.1 从源代码安装的更新

1. 拉取最新代码

```bash
git pull
```

2. 更新依赖项

```bash
pip install -r requirements.txt
```

### 7.2 使用安装包安装的更新

1. 下载最新版本的安装包
2. 运行安装程序，按照提示完成更新
