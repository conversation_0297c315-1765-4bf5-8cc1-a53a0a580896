{% extends "base.html" %}

{% block title %}错误 {{ error_code }} - RunSim 项目仪表板{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 text-center">
            <div class="error-template">
                <h1 class="display-1 text-muted">{{ error_code }}</h1>
                <h2 class="h4 mb-3">{{ error_message }}</h2>
                
                {% if error_code == 404 %}
                <p class="text-muted">抱歉，您访问的页面不存在。</p>
                {% elif error_code == 500 %}
                <p class="text-muted">服务器内部错误，请稍后重试。</p>
                {% elif error_code == 413 %}
                <p class="text-muted">上传的文件过大，请选择较小的文件。</p>
                {% else %}
                <p class="text-muted">发生了未知错误。</p>
                {% endif %}
                
                <div class="mt-4">
                    <a href="{{ url_for('index') }}" class="btn btn-primary me-2">
                        <i class="fas fa-home me-1"></i>返回首页
                    </a>
                    <button onclick="history.back()" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>返回上页
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
